# 🚀 Supabase项目设置说明

## ✅ 配置已完成

你的Supabase配置已经更新：
- **项目URL**: `https://azpanzufbuegpcuoybco.supabase.co`
- **匿名密钥**: 已配置

## 📋 下一步操作

### 1. 创建数据库表结构

请按照以下步骤在你的Supabase项目中创建数据库表：

#### 步骤1: 进入SQL编辑器
1. 登录 [supabase.com](https://supabase.com)
2. 进入你的项目 `azpanzufbuegpcuoybco`
3. 点击左侧菜单的 **SQL Editor**
4. 点击 **New Query**

#### 步骤2: 执行数据库创建脚本
复制以下SQL代码并在SQL编辑器中执行：

```sql
-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表 (扩展auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 塔罗解读记录表
CREATE TABLE public.tarot_readings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    spread_type TEXT NOT NULL,
    cards JSONB NOT NULL,
    interpretation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 每日塔罗表
CREATE TABLE public.daily_tarot (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    card JSONB,
    fortune TEXT,
    advice TEXT,
    is_drawn BOOLEAN DEFAULT FALSE,
    manifestation_goal TEXT,
    affirmation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- 用户偏好设置表
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    preferences JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_tarot_readings_user_id ON public.tarot_readings(user_id);
CREATE INDEX idx_tarot_readings_created_at ON public.tarot_readings(created_at);
CREATE INDEX idx_daily_tarot_user_id ON public.daily_tarot(user_id);
CREATE INDEX idx_daily_tarot_date ON public.daily_tarot(date);
CREATE INDEX idx_daily_tarot_user_date ON public.daily_tarot(user_id, date);

-- 行级安全策略 (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tarot_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_tarot ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- 用户表策略
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 塔罗解读记录策略
CREATE POLICY "Users can view own readings" ON public.tarot_readings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own readings" ON public.tarot_readings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own readings" ON public.tarot_readings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own readings" ON public.tarot_readings
    FOR DELETE USING (auth.uid() = user_id);

-- 每日塔罗策略
CREATE POLICY "Users can view own daily tarot" ON public.daily_tarot
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own daily tarot" ON public.daily_tarot
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own daily tarot" ON public.daily_tarot
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own daily tarot" ON public.daily_tarot
    FOR DELETE USING (auth.uid() = user_id);

-- 用户偏好设置策略
CREATE POLICY "Users can view own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own preferences" ON public.user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- 创建函数：自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON public.user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建函数：自动创建用户资料
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器：当新用户注册时自动创建资料
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### 2. 配置认证设置

#### 启用邮箱认证
1. 在Supabase项目中，点击 **Authentication** → **Settings**
2. 确保 **Enable email confirmations** 已启用
3. 设置 **Site URL** 为你的应用域名（开发时可以用 `http://localhost:3000`）

#### 配置Apple ID登录（可选）
1. 在 **Authentication** → **Providers** 中
2. 启用 **Apple** 提供商
3. 配置Apple的Client ID和Secret（需要Apple Developer账号）

### 3. 测试连接

现在你可以测试应用与Supabase的连接：

1. 重启Flutter应用
2. 进入"我的"页面
3. 点击"账号登录"
4. 尝试注册一个新账号

### 4. 验证数据库

在Supabase仪表板中：
1. 点击 **Table Editor**
2. 查看是否创建了以下表：
   - `users`
   - `tarot_readings`
   - `daily_tarot`
   - `user_preferences`

## 🔧 故障排除

### 如果遇到连接问题：
1. 检查网络连接
2. 确认Supabase项目状态正常
3. 验证URL和密钥是否正确复制

### 如果遇到权限问题：
1. 确认RLS策略已正确创建
2. 检查用户是否已正确认证

### 如果表创建失败：
1. 检查SQL语法是否正确
2. 确认有足够的权限执行DDL语句
3. 查看Supabase日志获取详细错误信息

## 📱 下一步功能测试

完成设置后，你可以测试以下功能：

1. **用户注册/登录** ✅
2. **塔罗解读记录保存** ✅
3. **每日塔罗数据同步** ✅
4. **用户偏好设置** ✅
5. **数据安全性** ✅

---

🎉 **恭喜！** 你的AI塔罗应用现在已经连接到Supabase云端数据库，具备了完整的后端功能！
