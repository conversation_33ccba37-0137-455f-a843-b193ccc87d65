{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e126a2d93ab4b7c10b621fb70d240e74", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987576475510d0d5b99e5cdf6a4e204f96", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982c333367ddc5040705e600fd896b7b8f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987d41a0ce9910bdbd5ef8a32bb2705030", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982c333367ddc5040705e600fd896b7b8f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ef22c223a3264638f2b864a2c4fc4ada", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984e9a6a74790ee7da6df29859c7c6954e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98878d133ba6cf07a42260e25cbf455d29", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986af456def2dfb93558c5e466041bdd95", "guid": "bfdfe7dc352907fc980b868725387e985efdef90a840a3b68c127c85c632605c"}], "guid": "bfdfe7dc352907fc980b868725387e988467ee0e59e0a6de8f2e84ac47f7a2cb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e0be3b0d5ad56f1985578b1f97431765", "name": "shared_preferences_foundation-shared_preferences_foundation_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad625504a4c1e61077bbfd33bd1d1785", "name": "shared_preferences_foundation_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}