{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b09599fbc28931a753fba4b6646235c9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892005d2921633428b21c2808d64855e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986883062b16537c13bb38e5a11fd3697c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997e66c0e98e1db88e18272b5fafa165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986883062b16537c13bb38e5a11fd3697c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8402fb7425dde778441c192bedf25e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b9aef16cd8d52cac2575c4c184771a88", "guid": "bfdfe7dc352907fc980b868725387e987c2aa214007013daf4bd66af7a5b280a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98317a88984aac34efc05d732e238d95a1", "guid": "bfdfe7dc352907fc980b868725387e9855758afaea36a8ee0affd0ececd66393", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c95067d5535414fa7aca2c7c9aeb58cb", "guid": "bfdfe7dc352907fc980b868725387e980d5058e6b4388d8dabd12cd3a195d565", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893688e1ab05b5d52dde3bc6993813c79", "guid": "bfdfe7dc352907fc980b868725387e98c930392f53d19599edaa36dc12b13d25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873638de1d3143bf17f837c433a665b4e", "guid": "bfdfe7dc352907fc980b868725387e98ea343a401ef0eb209f9d997861aa2b3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983efa8d1b2cadd04a8ff1272e2ae82801", "guid": "bfdfe7dc352907fc980b868725387e9831ca39b6b1c05701596d07af9aad33fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0483871155b137dcd7e59ac6a147bc", "guid": "bfdfe7dc352907fc980b868725387e98c8f1a01e2b82b505697679e628dc7090", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830bfe959d9cd237252ba5f114756749a", "guid": "bfdfe7dc352907fc980b868725387e981947bdc3ccd5a76716941d88a6821f38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885965a1db665a08c215ee0a934cd645e", "guid": "bfdfe7dc352907fc980b868725387e982da30a1624bb0ab8a803821646c11e75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6ffb722cf0fcccbe4b8688d0500a9d8", "guid": "bfdfe7dc352907fc980b868725387e981c0e833678c4209babdaea4e1cd0a362", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa3839ab9a255f595ae81da2e443431", "guid": "bfdfe7dc352907fc980b868725387e98ec267a442b7f06cde98d23ff9b61160c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a819c72ad626690e45cbff8cdbb2d30f", "guid": "bfdfe7dc352907fc980b868725387e98f1a380550addaaaf1ae0a10ead1b4272", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bce8f4c315c6168f459a8da3996fff6", "guid": "bfdfe7dc352907fc980b868725387e98410534e5af45d620a747f5cfbd78ddcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98081365c797ec63d9436abdfaa0b68c19", "guid": "bfdfe7dc352907fc980b868725387e98239c7d78371dd4116099b8b4676224c8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98709a4ba4d11b5df355eded857df06e02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dfca2b6e3f4d65f714db5b150397edd3", "guid": "bfdfe7dc352907fc980b868725387e983816ba789e328ba9b91a6547ca6b601a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616ffa6a412257cd2479741599fb2a89", "guid": "bfdfe7dc352907fc980b868725387e9873ce46668a3beaa188227998a5a4062e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b875c256408a1d189b26fa5947ab35a", "guid": "bfdfe7dc352907fc980b868725387e9897133cf44a01b0e8e3a89ffa8e1fbd5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afdb8939f11280bd749ab5eca5801984", "guid": "bfdfe7dc352907fc980b868725387e9832c69a56782292399cf4d67b1a92aaf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dbab058a03db28b6b21cfc1f7a780e0", "guid": "bfdfe7dc352907fc980b868725387e9865fb7357029df62222316f505a984b5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd50561bffb2f82e2560f64723449011", "guid": "bfdfe7dc352907fc980b868725387e98fb700676bafd2fa955fbedcad6b125c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aaee669341dd194a843f8a82810c301", "guid": "bfdfe7dc352907fc980b868725387e9820444d5f8a41d5c1dedf8493851ff170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9b9f4414805412d0d1a4f8a9f78df68", "guid": "bfdfe7dc352907fc980b868725387e98a9e8dd49510e5a777620cc030c0dddd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af77ba0c26dcc4aa82b376e3d62c575b", "guid": "bfdfe7dc352907fc980b868725387e98ab87ae9ffef9444078ba6286f6c837c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfdd592b8cd385e45cd2624a16e5c32e", "guid": "bfdfe7dc352907fc980b868725387e9822a6a014a5b0fc383a52d437583807c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d920dda7f507de5fec5d8c1cfc8f2e85", "guid": "bfdfe7dc352907fc980b868725387e986b7c2f8760ffaf440ea375915ddbb9ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffe962b00ab3c519c207f3162dfdac8d", "guid": "bfdfe7dc352907fc980b868725387e9828ed59274cbdfe0cd4761816cdeb59c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987731fa1ac2605369a9361f12651da6f1", "guid": "bfdfe7dc352907fc980b868725387e98ee8b5330698c230d6f11aece92a7891a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd8540b0253bc1df5cb306c1bbe155f", "guid": "bfdfe7dc352907fc980b868725387e981341ab01a20d3a27e79e0aa7cf600818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f0005a56502858c5a3d5055b8333856", "guid": "bfdfe7dc352907fc980b868725387e986f1ac148010354313e7c0ccd6f497378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ed5e4cbd195bb909182d9bab4df651", "guid": "bfdfe7dc352907fc980b868725387e98e1ebce383c8598b90ca7b4b8f16686ef"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5620dc55715e8e8f6fcd49a2ef83a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e984fe03a62b0f3efa2b48d3a3971811d17"}], "guid": "bfdfe7dc352907fc980b868725387e98319a14c9389993bee5d96d155795db97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cd2512697fd8475ad5d01e85d7d220e", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e9868992c43aa4117f939ab7493574523f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}