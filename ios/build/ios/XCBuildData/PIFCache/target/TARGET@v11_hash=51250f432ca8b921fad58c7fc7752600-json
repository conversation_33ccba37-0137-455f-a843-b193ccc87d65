{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98edcaa6e05b0e3d44b92da1c653630cea", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d07b6afadeeb3058bda9993fb5a4e81", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d07b6afadeeb3058bda9993fb5a4e81", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e5a89d57d289c6d43c7568c99e0d81d", "guid": "bfdfe7dc352907fc980b868725387e98fe92b5afa8fe58bae3c9229d058e616a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2548330e4a1d0c2ec634dd3576e5430", "guid": "bfdfe7dc352907fc980b868725387e985965519b3ec2b9053365fa530bf643bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98987d5de64b9da84810336c2d77805942", "guid": "bfdfe7dc352907fc980b868725387e98fa9d97f54a76579d59e404ebe634bcdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7bb1b6cbcaf99d9d011a03bdd03c4c1", "guid": "bfdfe7dc352907fc980b868725387e9850bdaa7011aa3c91740c213b1a7fa2cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efcc5a72dd85b2e6bf70c7b0418841ed", "guid": "bfdfe7dc352907fc980b868725387e98b69297aee86961e47e29414c45e849ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d322e87bb238c0e13c31872de467efb", "guid": "bfdfe7dc352907fc980b868725387e985ca3c30a7f4851eb9b4ef33952a3f482", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d78ee5e1b366609e022edcc9a51c8c", "guid": "bfdfe7dc352907fc980b868725387e9812df9d9973c9f97679f7de972b48f817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cdf8285e12ea9d39b080c7e04e7a8e1", "guid": "bfdfe7dc352907fc980b868725387e98abf8d1a3dd0a4ead2fb6398cea2425ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883514eeb71594de2e12295c4f26831b6", "guid": "bfdfe7dc352907fc980b868725387e986340d5835ff3d4930a87aff596993c8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848ef7da49ffe13053d7fe77a2ab0ea65", "guid": "bfdfe7dc352907fc980b868725387e98282e717420c34bd36656734c015ab0db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4759bd7c17a20ab2bb6e3f8ad3dd2c", "guid": "bfdfe7dc352907fc980b868725387e98f79617ce3eea92130c3e8deb24ac6052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887170fca6f31defa57e60673cf2e280b", "guid": "bfdfe7dc352907fc980b868725387e989a9c98af54d171ffc5dcfcd328a9f656", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6e6f08b9769b3cdf04a1fb7bcba2701", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ddf59767594b978ca64d834f16ae8c7", "guid": "bfdfe7dc352907fc980b868725387e98892ab7216e572450585dbb7fe836780a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d347481f7f3a2abbca7bd320a7b8baac", "guid": "bfdfe7dc352907fc980b868725387e983d12f44f6ad546c0e51ad40a38a62758", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d8b7e4061da268ca592d3b7d5bec4c6", "guid": "bfdfe7dc352907fc980b868725387e98a42139f6edf79b3b7cbcf3d1471e4431", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c08f7bec5e853ac6cac2db62aed4983", "guid": "bfdfe7dc352907fc980b868725387e98d954eaec4de1c4988534c19c3b497a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984113c24c5b3bc272059baed698d474b8", "guid": "bfdfe7dc352907fc980b868725387e98a1ebbf17f73356fde3a4c9c9ccaaf541", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b514d315dc2530469f2cb28a09af68c", "guid": "bfdfe7dc352907fc980b868725387e9821e54dc62d1296156df602834f20fcc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98069adf7b68f121abfe9bf4b7bb0bbd17", "guid": "bfdfe7dc352907fc980b868725387e986061ae92e4bce152885cc5c3a1c0e4d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a8b61d95ff14881041a2bc1f8e6498", "guid": "bfdfe7dc352907fc980b868725387e98e7e268d654b1604ef998bf4cc75336d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3d11b24b53898bff5329c8526a25870", "guid": "bfdfe7dc352907fc980b868725387e9854f174ef0b05263e6dcd47023ed0496c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f864009e2df714286de6f9d1882bd1", "guid": "bfdfe7dc352907fc980b868725387e98a3f25623979afa23466422cd7a073ec5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a40337bd8d21af749d4a7d9b0f5ff4b0", "guid": "bfdfe7dc352907fc980b868725387e98ffd488b77237b8fc440d41413454bb07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b607f91f56544c89d006e30dccd25f0", "guid": "bfdfe7dc352907fc980b868725387e98867e367d9e5822a1ed3da2e5d879f4ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab633db50016d42253cd024eab0c1bca", "guid": "bfdfe7dc352907fc980b868725387e98f4e8406eff8c37b5cf57678723c2865d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876fd0a44e4108b452f3caf29a2fe9abf", "guid": "bfdfe7dc352907fc980b868725387e986a9a5442fb214160e10f186b31f4351e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1a3465b7e9eeddb3458469378ce0b7a", "guid": "bfdfe7dc352907fc980b868725387e9856735a0df7a10f65de67a62396d1be8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60c81f1bc8cef4b8bbb4497c092b160", "guid": "bfdfe7dc352907fc980b868725387e98d490619ce99b46bd5cde304795058c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29e254b2e6039777bfbe0999d197c01", "guid": "bfdfe7dc352907fc980b868725387e985b6b972d31abdb37d83e0217390f1111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e8143efbb06a85091f24b555abeb96", "guid": "bfdfe7dc352907fc980b868725387e9826e5537c184a98a817fabddd8b1af46b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b84f584edd18d47d28b18736d9ed370", "guid": "bfdfe7dc352907fc980b868725387e98a6f139e04c42be401173e09b8eb64304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ce41f90b16f636b08f0008db08ba7c", "guid": "bfdfe7dc352907fc980b868725387e98bba9b3f581dbe27be7f77e7dba39135c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9813188cd6d03f2a6352f1f89165988", "guid": "bfdfe7dc352907fc980b868725387e98f5a18a02b63f8074b1935286ce097e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f39d48b0bff5f4cc30effb6d9efc76", "guid": "bfdfe7dc352907fc980b868725387e982a4d902c47d2e168ad1b9faadb826b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a033fcdd8c0805a45354f066e1e1d299", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cba021ebbd6037387147ec5cc0542b08", "guid": "bfdfe7dc352907fc980b868725387e98c7209acb4bbc11473400b95ba264e570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0b9e5843c82df2e32a9f731430675f", "guid": "bfdfe7dc352907fc980b868725387e98b9ad21760386409e6a8418d260c875a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8141c187543e50b73a4d48cff54c66", "guid": "bfdfe7dc352907fc980b868725387e9869daee10844bdc1377101bb06b07af6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b4b1a50930dc84c9ab41f488d0055d4", "guid": "bfdfe7dc352907fc980b868725387e9880b83d2e94cb821fc34990ec5eb08f30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c6211721568dc83820d237ed8b556d4", "guid": "bfdfe7dc352907fc980b868725387e983fc554d229dec51832d227dc3c6cd4f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0e8b4fd9bf492cb509bbe066fd4423", "guid": "bfdfe7dc352907fc980b868725387e984d92ce2a6a775539ad9cd12b7b3c8702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57b34972c0c2862bdf7fa8c0e9ddcdc", "guid": "bfdfe7dc352907fc980b868725387e98b55b1eb31da0207c73caefaa35380afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e60770e2e3d1ee2f12625f52969c9b", "guid": "bfdfe7dc352907fc980b868725387e982a5ad3c71031968c83cf1f0dd2275285"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}