PODS:
  - app_links (0.0.2):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  sensors_plus: 7229095999f30740798f0eeef5cd120357a8f4f2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: ba0d7af98d1a0db7cbf744edd0331fd4de934cd6

COCOAPODS: 1.16.2
