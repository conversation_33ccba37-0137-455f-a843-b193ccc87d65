import Flutter
import UIKit
import VisionKit
import Vision
import CoreImage

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // 注册VisionKit服务
    setupVisionKitChannel()

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  private func setupVisionKitChannel() {
    guard let controller = window?.rootViewController as? FlutterViewController else {
      return
    }

    let visionKitChannel = FlutterMethodChannel(
      name: "vision_kit_service",
      binaryMessenger: controller.binaryMessenger
    )

    visionKitChannel.setMethodCallHandler { [weak self] (call, result) in
      self?.handleVisionKitCall(call: call, result: result)
    }
  }

  private func handleVisionKitCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "removeBackground":
      handleRemoveBackground(call: call, result: result)
    case "isVisionKitAvailable":
      handleIsVisionKitAvailable(result: result)
    case "getVisionKitVersion":
      handleGetVisionKitVersion(result: result)
    case "preprocessImage":
      handlePreprocessImage(call: call, result: result)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

  private func handleRemoveBackground(call: FlutterMethodCall, result: @escaping FlutterResult) {
    guard let args = call.arguments as? [String: Any],
          let imagePath = args["imagePath"] as? String else {
      result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing imagePath", details: nil))
      return
    }

    // 检查VisionKit可用性
    if #available(iOS 17.0, *) {
      removeBackgroundWithVisionKit(imagePath: imagePath, result: result)
    } else {
      result(FlutterError(code: "VISION_KIT_NOT_AVAILABLE", message: "VisionKit requires iOS 17+", details: nil))
    }
  }

  @available(iOS 17.0, *)
  private func removeBackgroundWithVisionKit(imagePath: String, result: @escaping FlutterResult) {
    DispatchQueue.global(qos: .userInitiated).async {
      do {
        // 加载图片
        guard let inputImage = UIImage(contentsOfFile: imagePath) else {
          DispatchQueue.main.async {
            result(FlutterError(code: "IMAGE_LOAD_FAILED", message: "Failed to load image", details: nil))
          }
          return
        }

        // 创建VNImageRequestHandler
        let requestHandler = VNImageRequestHandler(cgImage: inputImage.cgImage!, options: [:])

        // 创建背景移除请求
        let request = VNGenerateForegroundInstanceMaskRequest { request, error in
          DispatchQueue.main.async {
            if let error = error {
              result(FlutterError(code: "IMAGE_PROCESSING_FAILED", message: error.localizedDescription, details: nil))
              return
            }

            guard let observations = request.results as? [VNInstanceMaskObservation],
                  let observation = observations.first else {
              result(FlutterError(code: "NO_SUBJECT_FOUND", message: "No foreground subject detected", details: nil))
              return
            }

            do {
              // 应用蒙版移除背景
              let maskedImage = try self.applyMask(observation: observation, to: inputImage, requestHandler: requestHandler)

              // 保存处理后的图片
              let outputPath = self.saveProcessedImage(maskedImage, originalPath: imagePath)

              if let outputPath = outputPath {
                result(outputPath)
              } else {
                result(FlutterError(code: "SAVE_FAILED", message: "Failed to save processed image", details: nil))
              }
            } catch {
              result(FlutterError(code: "MASK_APPLICATION_FAILED", message: error.localizedDescription, details: nil))
            }
          }
        }

        // 执行请求
        try requestHandler.perform([request])

      } catch {
        DispatchQueue.main.async {
          result(FlutterError(code: "REQUEST_FAILED", message: error.localizedDescription, details: nil))
        }
      }
    }
  }

  @available(iOS 17.0, *)
  private func applyMask(observation: VNInstanceMaskObservation, to image: UIImage, requestHandler: VNImageRequestHandler) throws -> UIImage {
    // 获取蒙版
    let mask = try observation.generateScaledMaskForImage(forInstances: observation.allInstances, from: requestHandler)

    // 将CVPixelBuffer转换为CGImage
    guard let maskCGImage = createCGImageFromPixelBuffer(mask) else {
      throw NSError(domain: "VisionKitError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert mask to CGImage"])
    }

    // 创建带透明背景的图片
    let renderer = UIGraphicsImageRenderer(size: image.size)
    let maskedImage = renderer.image { context in
      // 绘制原图
      image.draw(at: .zero)

      // 应用蒙版
      context.cgContext.setBlendMode(.destinationIn)
      context.cgContext.draw(maskCGImage, in: CGRect(origin: .zero, size: image.size))
    }

    return maskedImage
  }

  private func createCGImageFromPixelBuffer(_ pixelBuffer: CVPixelBuffer) -> CGImage? {
    let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
    let context = CIContext()
    return context.createCGImage(ciImage, from: ciImage.extent)
  }

  private func saveProcessedImage(_ image: UIImage, originalPath: String) -> String? {
    // 生成输出路径
    let originalURL = URL(fileURLWithPath: originalPath)
    let fileName = originalURL.deletingPathExtension().lastPathComponent + "_processed.png"
    let outputURL = originalURL.deletingLastPathComponent().appendingPathComponent(fileName)

    // 保存为PNG以保持透明度
    guard let pngData = image.pngData() else {
      return nil
    }

    do {
      try pngData.write(to: outputURL)
      return outputURL.path
    } catch {
      print("Failed to save processed image: \(error)")
      return nil
    }
  }

  private func handleIsVisionKitAvailable(result: @escaping FlutterResult) {
    if #available(iOS 17.0, *) {
      result(true)
    } else {
      result(false)
    }
  }

  private func handleGetVisionKitVersion(result: @escaping FlutterResult) {
    if #available(iOS 17.0, *) {
      result("iOS 17.0+")
    } else {
      result("Not Available")
    }
  }

  private func handlePreprocessImage(call: FlutterMethodCall, result: @escaping FlutterResult) {
    // 预处理图片的实现可以根据需要添加
    result("Not implemented yet")
  }
}
