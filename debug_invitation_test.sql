-- 邀请码调试测试脚本
-- 在 Supabase SQL Editor 中执行

-- 1. 检查表是否存在
SELECT 
    schemaname,
    tablename 
FROM pg_tables 
WHERE tablename IN ('invitation_codes', 'invitation_code_usages', 'user_invitation_stats')
ORDER BY tablename;

-- 2. 检查 invitation_codes 表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'invitation_codes'
ORDER BY ordinal_position;

-- 3. 查看现有邀请码数据
SELECT 
    code,
    code_type,
    reward_type,
    reward_value,
    max_uses,
    current_uses,
    is_active,
    expires_at,
    description,
    created_at
FROM invitation_codes
ORDER BY created_at DESC;

-- 4. 检查是否有使用记录
SELECT 
    icu.id,
    ic.code,
    icu.user_id,
    icu.reward_granted,
    icu.used_at
FROM invitation_code_usages icu
JOIN invitation_codes ic ON icu.invitation_code_id = ic.id
ORDER BY icu.used_at DESC
LIMIT 10;

-- 5. 检查用户统计表
SELECT 
    user_id,
    personal_invitation_code,
    has_used_invitation_code,
    total_invitations,
    successful_invitations,
    total_reward_days
FROM user_invitation_stats
ORDER BY created_at DESC
LIMIT 10;

-- 6. 插入测试邀请码（如果不存在）
INSERT INTO invitation_codes (
    code, 
    code_type, 
    reward_type, 
    reward_value, 
    max_uses, 
    current_uses,
    is_active,
    description
) VALUES 
('HELLO1', 'system', 'weekly_membership', 7, -1, 0, true, '新用户专享周会员'),
('TEST01', 'system', 'weekly_membership', 7, -1, 0, true, '测试专用邀请码'),
('DEBUG1', 'system', 'weekly_membership', 7, -1, 0, true, '调试专用邀请码')
ON CONFLICT (code) DO UPDATE SET
    is_active = EXCLUDED.is_active,
    description = EXCLUDED.description;

-- 7. 验证插入结果
SELECT 
    code,
    is_active,
    description,
    current_uses,
    max_uses
FROM invitation_codes 
WHERE code IN ('HELLO1', 'TEST01', 'DEBUG1');

-- 8. 检查 RLS 策略
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename IN ('invitation_codes', 'invitation_code_usages', 'user_invitation_stats');

-- 9. 测试查询权限（模拟应用查询）
-- 注意：这个查询可能会因为 RLS 策略而失败，这是正常的
SELECT 
    id, 
    code, 
    reward_type, 
    reward_value, 
    description, 
    max_uses, 
    current_uses, 
    expires_at
FROM invitation_codes
WHERE code = 'HELLO1' AND is_active = true;

-- 10. 检查 Edge Functions（如果有权限）
-- 注意：这个查询可能需要特殊权限
SELECT 
    name,
    status,
    created_at,
    updated_at
FROM supabase_functions.functions
WHERE name = 'use-invitation-code';

-- 调试信息汇总
SELECT 
    '=== 调试信息汇总 ===' as info,
    (SELECT COUNT(*) FROM invitation_codes) as total_codes,
    (SELECT COUNT(*) FROM invitation_codes WHERE is_active = true) as active_codes,
    (SELECT COUNT(*) FROM invitation_code_usages) as total_usages,
    (SELECT COUNT(*) FROM user_invitation_stats) as total_users;
