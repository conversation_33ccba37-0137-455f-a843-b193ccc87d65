-- 添加聊天相关字段到higher_self_memories表

-- 添加聊天会话ID字段
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'higher_self_memories' AND column_name = 'chat_session_id') THEN
        ALTER TABLE higher_self_memories ADD COLUMN chat_session_id TEXT;
    END IF;
END $$;

-- 添加完整对话字段
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'higher_self_memories' AND column_name = 'full_conversation') THEN
        ALTER TABLE higher_self_memories ADD COLUMN full_conversation TEXT;
    END IF;
END $$;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_higher_self_memories_chat_session ON higher_self_memories(chat_session_id);
CREATE INDEX IF NOT EXISTS idx_higher_self_memories_memory_type ON higher_self_memories(memory_type);
CREATE INDEX IF NOT EXISTS idx_higher_self_memories_user_date ON higher_self_memories(user_id, created_at);

-- 添加注释
COMMENT ON COLUMN higher_self_memories.chat_session_id IS '聊天会话的唯一标识符（仅用于chat_summary类型）';
COMMENT ON COLUMN higher_self_memories.full_conversation IS '完整的聊天对话内容（仅用于chat_summary类型）';
