-- Create manifestation_goals table
CREATE TABLE IF NOT EXISTS public.manifestation_goals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL CHECK (char_length(title) > 0 AND char_length(title) <= 500),
    description TEXT CHECK (char_length(description) <= 2000),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'manifesting', 'manifested')),
    affirmation TEXT CHECK (char_length(affirmation) <= 1000),
    is_affirmation_generated BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    -- Soft delete support
    deleted_at TIMESTAMPTZ NULL,
    -- Sync metadata
    local_id TEXT NULL, -- For migration from local storage
    sync_status TEXT NOT NULL DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'conflict')),
    last_synced_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_user_id ON public.manifestation_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_status ON public.manifestation_goals(status);
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_created_at ON public.manifestation_goals(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_updated_at ON public.manifestation_goals(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_deleted_at ON public.manifestation_goals(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_sync_status ON public.manifestation_goals(sync_status) WHERE sync_status != 'synced';

-- Create composite index for efficient user queries
CREATE INDEX IF NOT EXISTS idx_manifestation_goals_user_active ON public.manifestation_goals(user_id, created_at DESC) 
WHERE deleted_at IS NULL;

-- Enable Row Level Security
ALTER TABLE public.manifestation_goals ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own goals
CREATE POLICY "Users can view own manifestation goals" ON public.manifestation_goals
    FOR SELECT USING (auth.uid() = user_id AND deleted_at IS NULL);

-- Users can insert their own goals
CREATE POLICY "Users can insert own manifestation goals" ON public.manifestation_goals
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own goals
CREATE POLICY "Users can update own manifestation goals" ON public.manifestation_goals
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Users can soft delete their own goals
CREATE POLICY "Users can delete own manifestation goals" ON public.manifestation_goals
    FOR UPDATE USING (auth.uid() = user_id AND deleted_at IS NULL) 
    WITH CHECK (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER handle_manifestation_goals_updated_at
    BEFORE UPDATE ON public.manifestation_goals
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Create function for soft delete
CREATE OR REPLACE FUNCTION public.soft_delete_manifestation_goal(goal_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.manifestation_goals 
    SET deleted_at = NOW(), updated_at = NOW()
    WHERE id = goal_id AND user_id = auth.uid() AND deleted_at IS NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.manifestation_goals TO authenticated;
GRANT EXECUTE ON FUNCTION public.soft_delete_manifestation_goal TO authenticated;
