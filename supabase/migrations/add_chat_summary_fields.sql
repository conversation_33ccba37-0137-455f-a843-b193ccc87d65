-- 添加聊天摘要相关字段到diary_entries表
-- 如果字段不存在则添加

-- 添加聊天摘要字段
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'diary_entries' AND column_name = 'chat_summary') THEN
        ALTER TABLE diary_entries ADD COLUMN chat_summary TEXT;
    END IF;
END $$;

-- 添加聊天来源字段
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'diary_entries' AND column_name = 'chat_source') THEN
        ALTER TABLE diary_entries ADD COLUMN chat_source TEXT DEFAULT 'manual';
    END IF;
END $$;

-- 添加聊天会话ID字段
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'diary_entries' AND column_name = 'chat_session_id') THEN
        ALTER TABLE diary_entries ADD COLUMN chat_session_id TEXT;
    END IF;
END $$;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_diary_entries_chat_source ON diary_entries(chat_source);
CREATE INDEX IF NOT EXISTS idx_diary_entries_chat_session_id ON diary_entries(chat_session_id);
CREATE INDEX IF NOT EXISTS idx_diary_entries_user_date ON diary_entries(user_id, created_at);

-- 添加注释
COMMENT ON COLUMN diary_entries.chat_summary IS '聊天对话的简短摘要';
COMMENT ON COLUMN diary_entries.chat_source IS '聊天来源：manual, soul_mirror, tarot';
COMMENT ON COLUMN diary_entries.chat_session_id IS '聊天会话的唯一标识符';
