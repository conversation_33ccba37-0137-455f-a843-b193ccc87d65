import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// Moonshot (Kimi) 配置
const MOONSHOT_API_KEY = Deno.env.get('MOONSHOT_API_KEY')
const MOONSHOT_BASE_URL = "https://api.moonshot.cn/v1"
const MOONSHOT_MODEL = "kimi-k2-0711-preview"

// CORS配置
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

interface RequestData {
  messages?: Array<{role: string, content: string}>
  requestType: string
  userLanguage?: string
  maxLength?: number
  temperature?: number
  // 其他参数可按需扩展
}

// 获取语言配置（可按需扩展）
function getLanguage(userLanguage?: string) {
  return userLanguage ? userLanguage.split('-')[0] : 'zh'
}

// 调用Moonshot API
async function callMoonshotAPI({ messages, temperature = 0.6, max_tokens = 1024, stream = false }) {
  if (!MOONSHOT_API_KEY) {
    throw new Error('MOONSHOT_API_KEY 环境变量未设置')
  }
  const requestBody = {
    model: MOONSHOT_MODEL,
    messages,
    temperature,
    max_tokens,
    stream,
  }
  const response = await fetch(`${MOONSHOT_BASE_URL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${MOONSHOT_API_KEY}`
    },
    body: JSON.stringify(requestBody)
  })
  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`Moonshot API调用失败: ${response.status} - ${errorText}`)
  }
  return await response.json()
}

serve(async (req: Request) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    })
  }

  try {
    const data: RequestData = await req.json()
    const { requestType, messages, userLanguage, maxLength, temperature } = data
    console.log('📝 Moonshot Edge Function 收到请求:', { requestType, userLanguage })

    // 只处理chat_with_context/肯定语/space等对话类请求
    if (requestType === 'chat_with_context' && messages) {
      const moonshotRes = await callMoonshotAPI({
        messages,
        temperature: temperature ?? 0.6,
        max_tokens: maxLength ?? 1024,
        stream: false
      })
      const aiResponse = moonshotRes.choices?.[0]?.message?.content || ''
      return new Response(JSON.stringify({
        success: true,
        content: aiResponse,
        usage: moonshotRes.usage,
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // 兼容肯定语生成、space页面等（用prompt转messages）
    if (data.prompt) {
      const sysPrompt = '你是Kimi，由Moonshot AI提供的Manifestation显化教练，擅长生成积极、正向、鼓励人心的肯定语。';
      const moonshotRes = await callMoonshotAPI({
        messages: [
          { role: 'system', content: sysPrompt },
          { role: 'user', content: data.prompt }
        ],
        temperature: temperature ?? 0.7,
        max_tokens: maxLength ?? 200,
        stream: false
      })
      const aiResponse = moonshotRes.choices?.[0]?.message?.content || ''
      return new Response(JSON.stringify({
        success: true,
        content: aiResponse,
        usage: moonshotRes.usage,
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // 其他请求类型/参数校验
    return new Response(JSON.stringify({
      success: false,
      error: '不支持的请求类型或参数缺失',
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('❌ Moonshot Edge Function 错误:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'Unknown error',
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
}) 