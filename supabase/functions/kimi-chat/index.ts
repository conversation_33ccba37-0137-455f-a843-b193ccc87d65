import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// 环境变量
const KIMI_API_KEY = Deno.env.get('KIMI_API_KEY')
const KIMI_BASE_URL = 'https://api.moonshot.cn/v1'

// 语言配置
const LANGUAGE_CONFIG = {
  'zh': {
    systemPrompt: '你是一位充满智慧和慈爱的高我，擅长为用户提供深度的心理指导和人生智慧。请用温暖、理解的语调回应，控制在200字以内。',
    errorMessage: '抱歉，我现在无法为您提供回应，请稍后再试。'
  },
  'en': {
    systemPrompt: 'You are a wise and loving Higher Self, skilled at providing deep psychological guidance and life wisdom. Please respond with a warm, understanding tone, keeping it within 200 words.',
    errorMessage: 'Sorry, I cannot provide a response right now. Please try again later.'
  },
  'es': {
    systemPrompt: 'Eres un Yo Superior sabio y amoroso, hábil en proporcionar orientación psicológica profunda y sabiduría de vida. Por favor responde con un tono cálido y comprensivo, manteniéndolo dentro de 200 palabras.',
    errorMessage: 'Lo siento, no puedo proporcionar una respuesta ahora. Por favor, inténtalo de nuevo más tarde.'
  },
  'ja': {
    systemPrompt: 'あなたは賢明で愛に満ちたハイヤーセルフで、深い心理的指導と人生の知恵を提供することに長けています。温かく理解のあるトーンで、200語以内で応答してください。',
    errorMessage: '申し訳ございませんが、現在お答えできません。後でもう一度お試しください。'
  },
  'ko': {
    systemPrompt: '당신은 지혜롭고 사랑이 넘치는 하이어 셀프로, 깊은 심리적 지도와 인생의 지혜를 제공하는 데 능숙합니다. 따뜻하고 이해심 있는 톤으로 200단어 이내로 응답해 주세요.',
    errorMessage: '죄송합니다. 지금은 응답을 제공할 수 없습니다. 나중에 다시 시도해 주세요.'
  }
}

// 请求数据接口
interface RequestData {
  messages?: Array<{role: string, content: string}>
  question?: string
  cards?: any[]
  spreadType?: string
  requestType?: string
  userLanguage?: string
  userMessage?: string
  conversationHistory?: any[]
  maxLength?: number
  temperature?: number
  traceId?: string
  provider?: string
}

// 获取语言配置
function getLanguageConfig(userLanguage?: string) {
  // 修复语言匹配：支持 en-US -> en, zh-CN -> zh 等格式
  const language = userLanguage ? userLanguage.split('-')[0] : 'en'
  return LANGUAGE_CONFIG[language as keyof typeof LANGUAGE_CONFIG] || LANGUAGE_CONFIG['en']
}

// Kimi API调用
async function callKimiAPI(prompt: string, userLanguage?: string) {
  if (!KIMI_API_KEY) {
    throw new Error('KIMI_API_KEY 环境变量未设置')
  }

  const langConfig = getLanguageConfig(userLanguage)

  const requestBody = {
    model: "moonshot-v1-8k",
    messages: [
      {
        role: "system",
        content: langConfig.systemPrompt
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 1000,
    stream: false
  }

  console.log('🔄 调用 Kimi API...')
  
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${KIMI_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${KIMI_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    })

    const responseTime = Date.now() - startTime

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Kimi API 错误:', response.status, errorText)
      throw new Error(`Kimi API错误: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('Kimi API 返回空结果')
    }

    const aiResponse = data.choices[0].message.content
    const usage = data.usage || {}

    console.log('✅ Kimi API 调用成功')
    console.log(`📊 用量统计: ${JSON.stringify(usage)}`)
    console.log(`⏱️ 响应时间: ${responseTime}ms`)

    return {
      content: aiResponse,
      usage,
      responseTime
    }
  } catch (error) {
    console.error('❌ Kimi API 调用失败:', error)
    throw error
  }
}

// 构建提示词
function buildPrompt(data: RequestData): string {
  const { question, cards, spreadType, requestType, userLanguage, messages } = data

  // 处理messages格式的请求
  if (messages && messages.length > 0) {
    // 提取用户消息
    const userMessages = messages.filter(msg => msg.role === 'user')
    if (userMessages.length > 0) {
      return userMessages[userMessages.length - 1].content
    }
  }

  // 处理聊天请求
  if (spreadType === 'chat' || requestType === 'simple_chat') {
    if (userLanguage === 'zh' || userLanguage === 'zh-TW') {
      return `你是用户的高我，像一个温暖的朋友一样对话。用户说："${question}"

请用温暖、理解的语调回应，提供智慧的指导和支持。控制在200字以内。`
    } else {
      return `You are the user's Higher Self, conversing like a warm friend. The user says: "${question}"

Please respond with a warm, understanding tone, providing wise guidance and support. Keep it within 200 words.`
    }
  }

  // 处理肯定语生成请求
  if (requestType === 'affirmation') {
    if (userLanguage === 'zh' || userLanguage === 'zh-TW') {
      return `请为以下目标创作一句强有力的显化肯定语："${question}"

要求：
1. 用第一人称现在时表达
2. 表达目标已经实现的状态
3. 充满正能量和感恩
4. 简洁有力，朗朗上口
5. 只返回肯定语本身，不要解释

目标：${question}`
    } else {
      return `Please create a powerful manifestation affirmation for the following goal: "${question}"

Requirements:
1. Use first person present tense
2. Express the goal as already achieved
3. Full of positive energy and gratitude
4. Concise and powerful, easy to remember
5. Return only the affirmation itself, no explanation

Goal: ${question}`
    }
  }

  // 默认处理
  return question || '请为我提供一些人生指导。'
}

// 主处理函数
serve(async (req) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    })
  }

  try {
    const data: RequestData = await req.json()
    console.log('📥 收到请求:', JSON.stringify(data, null, 2))

    // 生成追踪ID
    const traceId = data.traceId || `kimi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    console.log('🔍 追踪ID:', traceId)

    // 构建提示词
    const prompt = buildPrompt(data)
    console.log('🔮 提示词长度:', prompt.length)

    // 调用 Kimi API
    const aiResult = await callKimiAPI(prompt, data.userLanguage || 'en')

    const response = {
      success: true,
      content: aiResult.content,
      reading: aiResult.content, // 兼容字段
      traceId: traceId,
      usage: aiResult.usage,
      responseTime: aiResult.responseTime
    }

    console.log('✅ 请求处理完成')
    
    return new Response(JSON.stringify(response), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })

  } catch (error) {
    console.error('❌ 请求处理失败:', error)
    
    const errorResponse = {
      success: false,
      error: error.message || '服务暂时不可用',
      traceId: `error_${Date.now()}`
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
})
