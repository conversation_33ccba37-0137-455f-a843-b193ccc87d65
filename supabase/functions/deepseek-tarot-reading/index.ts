import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// DeepSeek配置
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY')
const DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"

// CORS配置
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

interface TarotCard {
  id: string
  name: string
  description: string
  meaning: string
  keywords: string[]
  isMajorArcana: boolean
  isReversed: boolean
}

interface RequestData {
  question?: string
  cards?: TarotCard[]
  spreadType?: string
  requestType: string
  userMessage?: string
  previousReading?: string
  conversationHistory?: string[]
  questionType?: string
  maxLength?: number
  includeGuidingQuestion?: boolean
  stream?: boolean
  userLanguage?: string
  questionHistory?: string[]
  analysisPrompt?: string
  messages?: Array<{role: string, content: string}>
  temperature?: number
  analysisData?: {
    questionType?: string
    emotionIntensity?: string
    psychologicalBlock?: string
    urgencyLevel?: string
    specialist?: string
  }
  // 聊天摘要相关字段
  conversation?: string
  language?: string
  userId?: string
  sessionId?: string
  // 肯定语生成相关字段
  goalTitle?: string
  affirmationCount?: number
  affirmationVariation?: number
}

// 多语言配置系统
const LANGUAGE_CONFIG = {
  'zh': {
    name: '中文',
    systemPrompt: '你是一位充满魅力和智慧的女神级闺蜜，既有女神的洞察力，又有闺蜜的真实关怀。请用女神级闺蜜的语气提供塔罗解读。',
    affirmationSystemPrompt: '你是一位专业的显化教练，擅长创作充满力量和正能量的肯定语。请用温暖、鼓舞人心的语气创作肯定语。',
    friendTone: {
      endearments: ['亲爱的', '小可爱', '宝贝'],
      openings: ['哎呀', '哇', '嗯', '我懂', '真的吗', ''],
      particles: ['呀', '哦', '吧', '呢', '啦'],
      empathy: ['我懂的', '我理解', '我太理解了'],
      reactions: ['哈哈哈', '唉', '哎呀'],
      encouragement: ['试试看好吗', '相信我', '你会更棒的']
    },
    responses: {
      fallback: '亲爱的，AI解读服务暂时不可用呢，稍后再试试好吗？我们正在努力为你提供最好的塔罗解读体验！✨'
    }
  },
  'en': {
    name: 'English',
    systemPrompt: 'You are a warm female tarot reader, chatting like a close girlfriend. Please provide tarot readings with a friendly, natural tone.',
    affirmationSystemPrompt: 'You are a professional manifestation coach, skilled at creating powerful and positive affirmations. Please create affirmations with a warm, encouraging tone.',
    friendTone: {
      endearments: ['honey', 'sweetie', 'babe', 'dear'],
      particles: ['you know', 'right', 'okay'],
      empathy: ['I totally get it', 'I understand', 'I feel you'],
      reactions: ['haha', 'oh gosh', 'aww'],
      encouragement: ['give it a try', 'trust me', 'you got this']
    },
    responses: {
      fallback: 'Sorry honey, the AI reading service is temporarily unavailable. Please try again later, okay? We are working hard to provide you with the best tarot reading experience! ✨'
    }
  },
  'ja': {
    name: '日本語',
    systemPrompt: 'あなたは温かい女性のタロット師で、親友のようにユーザーとチャットします。親しみやすく自然な口調でタロット解読を提供してください。',
    affirmationSystemPrompt: 'あなたはプロのマニフェステーションコーチで、力強く前向きなアファメーションを作成するのが得意です。温かく励ましの口調でアファメーションを作成してください。',
    friendTone: {
      endearments: ['ちゃん', 'さん', 'お疲れ様'],
      particles: ['よ', 'ね', 'かな', 'でしょ'],
      empathy: ['わかるよ', '理解できる', 'そうだよね'],
      reactions: ['あはは', 'えー', 'そうそう'],
      encouragement: ['やってみて', '大丈夫だよ', '頑張って']
    },
    responses: {
      fallback: 'ごめんね、AI解読サービスが一時的に利用できないの。後でもう一度試してみて？最高のタロット解読体験を提供するために頑張ってるから！✨'
    }
  },
  'ko': {
    name: '한국어',
    systemPrompt: '당신은 따뜻한 여성 타로 리더로, 절친한 친구처럼 사용자와 대화합니다. 친근하고 자연스러운 말투로 타로 해석을 제공해주세요.',
    affirmationSystemPrompt: '당신은 전문적인 매니페스테이션 코치로, 강력하고 긍정적인 확언을 만드는 데 능숙합니다. 따뜻하고 격려하는 어조로 확언을 만들어주세요.',
    friendTone: {
      endearments: ['언니', '자기야', '친구'],
      particles: ['요', '야', '지', '네'],
      empathy: ['이해해', '알겠어', '공감해'],
      reactions: ['하하', '아이고', '맞아'],
      encouragement: ['해봐', '믿어', '잘할 거야']
    },
    responses: {
      fallback: '미안해, AI 해석 서비스가 일시적으로 안 돼. 나중에 다시 해봐? 최고의 타로 해석 경험을 위해 열심히 하고 있어! ✨'
    }
  },
  'es': {
    name: 'Español',
    systemPrompt: 'Eres una tarotista femenina cálida, charlando como una amiga íntima. Por favor proporciona lecturas de tarot con un tono amigable y natural.',
    affirmationSystemPrompt: 'Eres una coach de manifestación profesional, experta en crear afirmaciones poderosas y positivas. Por favor crea afirmaciones con un tono cálido y alentador.',
    friendTone: {
      endearments: ['cariño', 'amor', 'querida'],
      particles: ['¿verdad?', '¿no?', 'pues'],
      empathy: ['te entiendo', 'lo comprendo', 'te siento'],
      reactions: ['jaja', 'ay', 'claro'],
      encouragement: ['inténtalo', 'confía en mí', 'lo lograrás']
    },
    responses: {
      fallback: 'Lo siento cariño, el servicio de lectura de IA no está disponible temporalmente. Inténtalo de nuevo más tarde, ¿vale? ¡Estamos trabajando duro para brindarte la mejor experiencia de lectura de tarot! ✨'
    }
  },
  'fr': {
    name: 'Français',
    systemPrompt: 'Tu es une tarologue féminine chaleureuse, bavardant comme une amie intime. Veuillez fournir des lectures de tarot avec un ton amical et naturel.',
    affirmationSystemPrompt: 'Tu es une coach de manifestation professionnelle, experte dans la création d\'affirmations puissantes et positives. Veuillez créer des affirmations avec un ton chaleureux et encourageant.',
    friendTone: {
      endearments: ['chérie', 'ma belle', 'mon cœur'],
      particles: ['tu sais', 'hein', 'quoi'],
      empathy: ['je comprends', 'je te sens', 'je sais'],
      reactions: ['haha', 'oh là là', 'ah bon'],
      encouragement: ['essaie', 'fais-moi confiance', 'tu peux le faire']
    },
    responses: {
      fallback: 'Désolée chérie, le service de lecture IA est temporairement indisponible. Réessaie plus tard, d\'accord? Nous travaillons dur pour t\'offrir la meilleure expérience de lecture de tarot! ✨'
    }
  }
}

// 获取语言配置
function getLanguageConfig(userLanguage?: string) {
  // 修复语言匹配：支持 en-US -> en, zh-CN -> zh 等格式
  const language = userLanguage ? userLanguage.split('-')[0] : 'en'
  return LANGUAGE_CONFIG[language as keyof typeof LANGUAGE_CONFIG] || LANGUAGE_CONFIG['en']
}

// DeepSeek API调用
async function callDeepSeekAPI(prompt: string, userLanguage?: string) {
  if (!DEEPSEEK_API_KEY) {
    throw new Error('DEEPSEEK_API_KEY 环境变量未设置')
  }

  const langConfig = getLanguageConfig(userLanguage)

  const requestBody = {
    model: "deepseek-chat",
    messages: [
      {
        role: "system",
        content: langConfig.systemPrompt
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 1000,
    stream: false
  }

  console.log('🔄 调用 DeepSeek API...')
  
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    })

    const endTime = Date.now()
    const responseTime = endTime - startTime

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ DeepSeek API 错误:', response.status, errorText)
      throw new Error(`DeepSeek API调用失败: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('DeepSeek API 返回空响应')
    }

    const aiResponse = data.choices[0].message.content
    const usage = data.usage || {}

    console.log('✅ DeepSeek API 调用成功')
    console.log(`📊 用量统计: ${JSON.stringify(usage)}`)
    console.log(`⏱️ 响应时间: ${responseTime}ms`)

    return {
      content: aiResponse,
      usage,
      responseTime
    }
  } catch (error) {
    console.error('❌ DeepSeek API 调用失败:', error)
    throw error
  }
}

// 构建肯定语生成提示词
function buildAffirmationPrompt(goalTitle: string, language: string, variation: number): string {
  const langConfig = getLanguageConfig(language)
  
  if (language.startsWith('zh')) {
    const variations = [
      '请创作一句充满力量的显化肯定语',
      '请生成一句积极正面的肯定语句',
      '请创造一句能量满满的显化宣言',
      '请编写一句潜意识接受的肯定语',
      '请构思一句振动频率很高的肯定语',
    ]
    
    const selectedVariation = variations[(variation - 1) % variations.length]
    
    return `${selectedVariation}，目标是："${goalTitle}"

要求：
1. 用第一人称现在时表达（我是、我拥有、我感受到）
2. 表达目标已经实现的状态
3. 包含感恩和正面情感
4. 简洁有力，朗朗上口
5. 只返回肯定语本身，不要任何解释

目标：${goalTitle}`
  } else {
    const variations = [
      'Create a powerful manifestation affirmation',
      'Generate an empowering affirmation statement',
      'Craft an inspiring manifestation declaration',
      'Write a high-vibration affirmation',
      'Compose a transformative affirmation',
    ]
    
    const selectedVariation = variations[(variation - 1) % variations.length]
    
    return `${selectedVariation} for the goal: "${goalTitle}"

Requirements:
1. Use first person present tense (I am, I have, I feel)
2. Express the goal as already achieved
3. Include gratitude and positive emotions
4. Make it concise, powerful, and memorable
5. Return ONLY the affirmation itself, no explanations

Goal: ${goalTitle}`
  }
}

// 构建塔罗解读提示词
function buildTarotPrompt(data: RequestData): string {
  const { question, cards, spreadType, requestType, userLanguage } = data

  // 处理聊天请求
  if (spreadType === 'chat' || requestType === 'simple_chat') {
    if (userLanguage === 'zh' || userLanguage === 'zh-TW') {
      return `你是用户的高我，像一个温暖的朋友一样对话。用户说："${question}"

请用20-30字简短回应，要：
- 像朋友聊天一样自然
- 温暖理解，不说教
- 可以问一个简单问题
- 语气轻松亲切

直接回复，不要解释。`
    } else {
      return `You are the user's Higher Self, talking like a warm friend. User said: "${question}"

Please respond in 20-30 words:
- Natural like friend chatting
- Warm and understanding, not preachy
- Can ask a simple question
- Casual and caring tone

Reply directly, no explanations.`
    }
  }

  const cardInfo = cards?.map((card, index) => {
    if (userLanguage === 'en') {
      const position = card.isReversed ? 'Reversed' : 'Upright'
      return `${index + 1}. ${card.name} (${position}) - ${card.meaning}`
    } else {
      const position = card.isReversed ? '逆位' : '正位'
      return `${index + 1}. ${card.name}（${position}）- ${card.meaning}`
    }
  }).join('\n') || ''

  // 简化版初始解读 - 支持多种请求类型
  if (requestType === 'simple_initial_reading' || requestType === 'initial_reading') {
    if (userLanguage === 'zh') {
      return `你是女神级闺蜜，既有魅力又有智慧。

用户问题："${question}"
卡牌：${cardInfo}

回复要求：
- 开头自然多样化：偶尔用"哎呀"、"哇"、"嗯"、"亲爱的"或直接开始，避免每次都用"宝贝"
- 提到"能量"、"直觉"、"感受"等词汇
- 2-3句话，50字以内
- 语气亲切自然，不刻意
- 最后问1个简短问题

直接回复，不要解释。`
    } else {
      return `You are a goddess-level best friend with charm and wisdom.

User's question: "${question}"
Cards: ${cardInfo}

Requirements:
- Start with "Babe!"
- Mention "energy" or "intuition"
- 2-3 sentences, under 50 words
- End with 1 short question

Reply directly, no explanations.`
    }
  }

  // 简化版追问解读 - 心理开解 - 支持多种请求类型
  if (requestType === 'simple_follow_up_reading' || requestType === 'follow_up') {
    const userMessage = data.userMessage || ''

    if (userLanguage === 'zh') {
      return `你是女神级闺蜜。基于这些卡牌回复：
${cardInfo}

用户说："${userMessage}"

要求：
- 开头自然多样化：可以用"哎呀"、"嗯"、"我懂"、"亲爱的"或直接开始
- 提到卡牌和"能量"、"感受"
- 2句话，40字以内
- 语气温暖但不刻意
- 问1个简短问题

直接回复。`
    } else {
      return `You are goddess-level best friend. Reply based on these cards:
${cardInfo}

User said: "${userMessage}"

Requirements:
- Start with "Babe"
- Mention cards and "energy"
- 2 sentences, under 40 words
- Ask 1 short question

Reply directly.`
    }
  }

  // 默认fallback - 使用初始解读格式
  if (userLanguage === 'zh') {
    return `你是女神级闺蜜，既有魅力又有智慧。

用户问题："${question}"
卡牌：${cardInfo}

回复要求：
- 开头自然多样化：偶尔用"哎呀"、"哇"、"嗯"、"亲爱的"或直接开始
- 提到"能量"、"直觉"、"感受"等词汇
- 2-3句话，50字以内
- 语气亲切自然，不刻意
- 最后问1个简短问题

直接回复，不要解释。`
  } else {
    return `You are a goddess-level best friend with charm and wisdom.

User's question: "${question}"
Cards: ${cardInfo}

Requirements:
- Start with "Babe!"
- Mention "energy" or "intuition"
- 2-3 sentences, under 50 words
- End with 1 short question

Reply directly, no explanations.`
  }
}

// 构建聊天摘要提示词
function buildChatSummaryPrompt(conversation: string, language: string, maxLength: number): string {
  if (language.startsWith('zh')) {
    return `请为以下Soul Mirror聊天对话生成一个简洁的摘要，不超过${maxLength}个字符：

对话内容：
${conversation}

要求：
1. 捕捉对话的核心主题和情感
2. 突出用户的主要关切或分享的内容
3. 保持温暖和灵性的语调
4. 不超过${maxLength}个字符
5. 用一句话概括

摘要：`
  } else {
    return `Please generate a concise summary for the following Soul Mirror chat conversation, no more than ${maxLength} characters:

Conversation:
${conversation}

Requirements:
1. Capture the core theme and emotions of the conversation
2. Highlight the user's main concerns or shared content
3. Maintain a warm and spiritual tone
4. No more than ${maxLength} characters
5. Summarize in one sentence

Summary:`
  }
}

serve(async (req: Request) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    })
  }

  try {
    const data: RequestData = await req.json()
    console.log('📝 收到请求:', {
      requestType: data.requestType,
      question: data.question?.substring(0, 50) + '...',
      cardsCount: data.cards?.length,
      userLanguage: data.userLanguage,
      goalTitle: data.goalTitle?.substring(0, 30) + '...'
    })

    // 检查API密钥
    if (!DEEPSEEK_API_KEY) {
      console.error('❌ DEEPSEEK_API_KEY 未设置')
      const langConfig = getLanguageConfig(data.userLanguage)
      return new Response(JSON.stringify({
        success: false,
        error: 'API密钥未配置',
        reading: langConfig.responses.fallback
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      })
    }

    // 处理肯定语生成请求（新增）
    if (data.requestType === 'affirmation_generation') {
      console.log('🔮 处理肯定语生成请求')
      console.log('🎯 目标:', data.goalTitle)
      console.log('🌐 用户语言:', data.userLanguage || 'zh-CN')
      console.log('📊 变化数:', data.affirmationVariation || 1)

      const goalTitle = data.goalTitle || ''
      const language = data.userLanguage || 'zh-CN'
      const variation = data.affirmationVariation || 1

      if (!goalTitle) {
        return new Response(JSON.stringify({
          success: false,
          error: '缺少目标标题',
          content: '请提供显化目标'
        }), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      // 构建肯定语生成提示词
      const affirmationPrompt = buildAffirmationPrompt(goalTitle, language, variation)

      try {
        const langConfig = getLanguageConfig(language)
        
        const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          },
          body: JSON.stringify({
            model: "deepseek-chat",
            messages: [
              {
                role: "system",
                content: langConfig.affirmationSystemPrompt
              },
              {
                role: "user",
                content: affirmationPrompt
              }
            ],
            max_tokens: 100,
            temperature: 0.8,
            stream: false
          }),
        })

        if (!response.ok) {
          throw new Error(`DeepSeek API 调用失败: ${response.status}`)
        }

        const result = await response.json()
        const affirmation = result.choices[0]?.message?.content?.trim() || ''

        // 清理肯定语文本
        const cleanAffirmation = affirmation
          .replace(/^(肯定语[:：]?\s*|Affirmation[:：]?\s*)/i, '')
          .replace(/^["""]|["""]$/g, '')
          .trim()

        console.log('✅ 肯定语生成成功:', cleanAffirmation)

        return new Response(JSON.stringify({
          success: true,
          content: cleanAffirmation,
          response: cleanAffirmation,
          reading: cleanAffirmation,
          goalTitle: goalTitle,
          variation: variation
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })

      } catch (error) {
        console.error('❌ 生成肯定语失败:', error)
        return new Response(JSON.stringify({
          success: false,
          error: error.message,
          content: '我值得拥有美好的生活，我的目标正在实现。'
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }

    // 处理聊天摘要请求
    if (data.requestType === 'generate_chat_summary') {
      console.log('📝 处理聊天摘要请求')
      console.log('📊 对话内容长度:', data.conversation?.length || 0)
      console.log('🌐 用户语言:', data.language || 'zh-TW')
      console.log('📊 用户ID:', data.userId)
      console.log('📊 会话ID:', data.sessionId)

      const conversation = data.conversation || ''
      const maxLength = data.maxLength || 25
      const language = data.language || 'zh-TW'

      // 构建摘要生成的prompt
      const summaryPrompt = buildChatSummaryPrompt(conversation, language, maxLength)

      try {
        const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          },
          body: JSON.stringify({
            model: "deepseek-chat",
            messages: [{ role: "user", content: summaryPrompt }],
            max_tokens: 80,
            temperature: 0.3,
            stream: false
          }),
        })

        if (!response.ok) {
          throw new Error(`DeepSeek API 调用失败: ${response.status}`)
        }

        const result = await response.json()
        const summary = result.choices[0]?.message?.content?.trim() || ''

        // 清理摘要文本
        const cleanSummary = summary.replace(/^(摘要[:：]?\s*|Summary[:：]?\s*)/i, '').trim()

        console.log('✅ 聊天摘要生成成功:', cleanSummary)

        return new Response(JSON.stringify({
          success: true,
          summary: cleanSummary,
          sessionId: data.sessionId || null,
          userId: data.userId || null
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })

      } catch (error) {
        console.error('❌ 生成聊天摘要失败:', error)
        return new Response(JSON.stringify({
          success: false,
          error: error.message,
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        })
      }
    }

    // 处理聊天请求
    if (data.requestType === 'chat_with_context' && data.messages) {
      console.log('🔄 处理聊天请求，messages数量:', data.messages.length)
      console.log('🌐 用户语言:', data.userLanguage || 'zh-TW')

      // 获取语言配置
      const langConfig = getLanguageConfig(data.userLanguage || 'zh-TW')
      console.log('📋 语言配置:', langConfig.name)

      // 直接使用前端发送的messages
      const requestBody = {
        model: "deepseek-chat",
        messages: data.messages,
        temperature: data.temperature || 0.7,
        max_tokens: data.maxLength || 200,
        stream: false
      }

      console.log('🔮 调用DeepSeek API (聊天模式)...')

      try {
        const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
          },
          body: JSON.stringify(requestBody)
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error('❌ DeepSeek API 错误:', response.status, errorText)
          throw new Error(`DeepSeek API调用失败: ${response.status}`)
        }

        const apiResponse = await response.json()

        if (!apiResponse.choices || apiResponse.choices.length === 0) {
          throw new Error('DeepSeek API 返回空响应')
        }

        const aiResponse = apiResponse.choices[0].message.content
        const usage = apiResponse.usage || {}

        console.log('✅ 聊天API调用成功，回复长度:', aiResponse.length)

        return new Response(JSON.stringify({
          success: true,
          reading: aiResponse,
          response: aiResponse,
          content: aiResponse,
          usage: usage,
          responseTime: 0
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        })

      } catch (error) {
        console.error('❌ 聊天API调用失败:', error)

        return new Response(JSON.stringify({
          success: false,
          error: error.message || '聊天API调用失败',
          reading: langConfig.responses.fallback
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        })
      }
    }

    // 处理塔罗牌请求（原有逻辑）
    if (!data.question || !data.cards) {
      console.error('❌ 缺少必要的塔罗牌数据')
      return new Response(JSON.stringify({
        success: false,
        error: '缺少问题或卡牌数据',
        reading: '请提供完整的塔罗牌信息'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      })
    }

    // 构建提示词
    const prompt = buildTarotPrompt(data)
    console.log('🔮 提示词长度:', prompt.length)

    // 调用 DeepSeek API
    const aiResult = await callDeepSeekAPI(prompt, data.userLanguage || 'en')

    const response = {
      success: true,
      reading: aiResult.content,
      usage: aiResult.usage,
      responseTime: aiResult.responseTime
    }

    console.log('✅ 成功生成解读，长度:', aiResult.content.length)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })

  } catch (error) {
    console.error('❌ Edge Function 错误:', error)

    const langConfig = getLanguageConfig()
    const errorResponse = {
      success: false,
      error: error.message || 'Unknown error',
      reading: langConfig.responses.fallback
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 200, // 仍然返回200，让客户端处理错误
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
}) 