import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatSummaryRequest {
  conversation: string
  language: string
  maxLength?: number
  sessionId?: string
  userId: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { conversation, language, maxLength = 50, sessionId, userId }: ChatSummaryRequest = await req.json()

    console.log('📝 开始生成聊天摘要')
    console.log('📊 对话长度:', conversation.length)
    console.log('📊 用户ID:', userId)
    console.log('📊 会话ID:', sessionId)

    // 验证必需参数
    if (!conversation || !userId) {
      throw new Error('缺少必需参数: conversation 或 userId')
    }

    // 构建摘要生成的prompt
    const prompt = buildSummaryPrompt(conversation, language, maxLength)
    
    // 调用DeepSeek API生成摘要
    const summary = await generateSummaryWithDeepSeek(prompt)
    
    console.log('✅ 摘要生成成功:', summary)

    return new Response(
      JSON.stringify({
        success: true,
        summary: summary,
        sessionId: sessionId,
        userId: userId
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('❌ 生成聊天摘要失败:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

function buildSummaryPrompt(conversation: string, language: string, maxLength: number): string {
  if (language.startsWith('zh')) {
    return `请为以下Soul Mirror聊天对话生成一个简洁的摘要，不超过${maxLength}个字符：

对话内容：
${conversation}

要求：
1. 捕捉对话的核心主题和情感
2. 突出用户的主要关切或分享的内容
3. 保持温暖和灵性的语调
4. 不超过${maxLength}个字符
5. 用一句话概括

摘要：`
  } else {
    return `Please generate a concise summary for the following Soul Mirror chat conversation, no more than ${maxLength} characters:

Conversation:
${conversation}

Requirements:
1. Capture the core theme and emotions of the conversation
2. Highlight the user's main concerns or shared content
3. Maintain a warm and spiritual tone
4. No more than ${maxLength} characters
5. Summarize in one sentence

Summary:`
  }
}

async function generateSummaryWithDeepSeek(prompt: string): string {
  const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY')
  
  if (!DEEPSEEK_API_KEY) {
    throw new Error('DEEPSEEK_API_KEY 环境变量未设置')
  }

  const response = await fetch('https://api.deepseek.com/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
    },
    body: JSON.stringify({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 100,
      temperature: 0.3,
      stream: false
    }),
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error('DeepSeek API 错误:', errorText)
    throw new Error(`DeepSeek API 调用失败: ${response.status}`)
  }

  const data = await response.json()
  
  if (!data.choices || !data.choices[0] || !data.choices[0].message) {
    throw new Error('DeepSeek API 返回格式异常')
  }

  const summary = data.choices[0].message.content.trim()
  
  // 清理摘要文本，移除可能的前缀
  return summary.replace(/^(摘要[:：]?\s*|Summary[:：]?\s*)/i, '').trim()
}
