# 对话汇总记录存储位置说明

## 📍 **存储位置总览**

对话汇总记录存储在 **Supabase数据库** 的 `diary_entries` 表中，具体字段如下：

### 🗄️ **主要存储表：diary_entries**

```sql
CREATE TABLE diary_entries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,                    -- 完整对话内容
  chat_summary TEXT,                        -- 对话摘要 ⭐
  chat_source TEXT,                         -- 聊天来源 ('soul_mirror', 'tarot')
  chat_session_id TEXT,                     -- 会话ID
  mood_score INTEGER,                       -- 心情评分 (1-10)
  tags TEXT[],                             -- 标签数组
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔄 **存储流程**

### **1. 触发条件**
- 用户在Soul Mirror聊天界面进行对话
- 对话消息数量 ≥ 4条
- 用户点击刷新按钮或清空会话时

### **2. 处理流程**
```
用户清空会话 
→ HigherSelfService.clearSession()
→ _generateChatSummary()
→ Edge Function: generate-chat-summary
→ DiaryService.saveChatSummary()
→ 保存到 diary_entries 表
```

### **3. 关键代码位置**

#### **触发入口**
- 文件：`lib/services/higher_self_service.dart`
- 方法：`clearSession()` (第192行)
- 条件：`_messages.length >= 4 && _currentUserId != null`

#### **摘要生成**
- 文件：`lib/services/higher_self_service.dart`
- 方法：`_generateChatSummary()` (第217行)
- Edge Function：`generate-chat-summary`

#### **数据保存**
- 文件：`lib/services/diary_service.dart`
- 方法：`saveChatSummary()` (第10行)
- 表：`diary_entries`

## 📊 **存储的数据内容**

### **完整对话内容 (content字段)**
```
用户: 我今天很开心
高我: 太好了！是什么让你这么开心呢？
用户: 工作上有了突破
高我: 恭喜你！这种成就感一定很棒 ✨
```

### **对话摘要 (chat_summary字段)**
```
用户分享工作突破带来的喜悦，高我给予肯定和鼓励
```

### **其他字段**
- `chat_source`: "soul_mirror"
- `chat_session_id`: 会话唯一标识
- `tags`: ["soul_mirror", "chat"]
- `user_id`: 用户UUID

## 🔍 **查询和访问**

### **获取聊天摘要列表**
```dart
// 文件：lib/services/diary_service.dart
// 方法：getChatSummaries()
final summaries = await DiaryService.getChatSummaries(
  userId: userId,
  limit: 20,
);
```

### **SQL查询示例**
```sql
SELECT 
  id,
  chat_summary,
  chat_session_id,
  created_at
FROM diary_entries 
WHERE user_id = $1 
  AND chat_source IN ('soul_mirror', 'tarot')
  AND chat_summary IS NOT NULL
ORDER BY created_at DESC
LIMIT 20;
```

## 🎯 **前端显示位置和方式**

### **1. 日历界面显示**
**文件位置**：`lib/screens/daily_tarot_screen.dart` (第551-576行)

**显示逻辑**：
- 用户点击日历中的日期
- 系统查询该日期的聊天摘要 (`_selectedDateDiaries`)
- 在弹出的详情中显示 "💬 聊天记录" 部分
- 使用 `ChatSummaryCard` 组件展示每条聊天摘要

**代码示例**：
```dart
// 聊天摘要部分
if (_selectedDateDiaries.isNotEmpty) ...[
  const Text('💬 聊天记录', style: TextStyle(...)),
  ..._selectedDateDiaries.map((diary) =>
    ChatSummaryCard(
      chatEntry: diary,
      onTap: () => Navigator.push(...ChatDetailScreen...)
    )
  ),
],
```

### **2. ChatSummaryCard 组件**
**文件位置**：`lib/widgets/chat_summary_card.dart`

**显示内容**：
- **头部**：来源图标 + "灵魂之镜" + 时间
- **摘要**：对话摘要内容 (最多3行，超出显示省略号)
- **标签**：显示聊天来源标识
- **点击**：跳转到详细对话页面

**视觉样式**：
- 卡片式设计，圆角边框
- 紫色图标表示Soul Mirror来源
- 支持点击查看详情

### **3. 详细对话页面**
**文件位置**：`lib/screens/chat_detail_screen.dart`

**显示内容**：
- **摘要卡片**：渐变背景，显示来源和摘要
- **完整对话**：解析并显示所有对话消息
- **消息气泡**：区分用户和AI消息
- **分享功能**：支持分享对话内容

**解析逻辑**：
```dart
// 解析对话内容 (格式: "用户: 消息\n高我: 回复")
final messages = _parseConversation(chatEntry.content);
```

### **4. 数据加载流程**

**查询方法**：`DiaryService.getChatSummaries()`
```dart
// 获取用户的聊天摘要列表
final summaries = await DiaryService.getChatSummaries(
  userId: userId,
  limit: 20,
);
```

**过滤条件**：
- `chat_source IN ('soul_mirror', 'tarot')`
- `chat_summary IS NOT NULL`
- 按创建时间倒序排列

### **5. 用户交互流程**

```
用户操作流程：
1. 打开日历界面
2. 点击某个日期
3. 查看该日期的聊天摘要卡片
4. 点击摘要卡片
5. 进入详细对话页面
6. 查看完整对话内容
7. 可选择分享对话
```

### **6. 视觉设计特点**

**ChatSummaryCard样式**：
- 白色卡片背景，轻微阴影
- 12px圆角边框
- 紫色Soul Mirror图标
- 灰色时间戳
- 3行文本截断

**ChatDetailScreen样式**：
- 渐变背景摘要卡片
- 消息气泡式对话显示
- 用户消息：右对齐，蓝色背景
- AI消息：左对齐，灰色背景

## 🔧 **相关配置**

### **Edge Function**
- 名称：`generate-chat-summary`
- 版本：v5
- 功能：生成对话摘要

### **数据库权限**
- RLS策略：用户只能访问自己的数据
- 权限：`auth.uid() = user_id`

## 📝 **总结**

### **存储方面**：
1. **主表**：`diary_entries`
2. **关键字段**：`chat_summary`, `content`, `chat_source`
3. **触发条件**：对话≥4条且清空会话
4. **访问方式**：通过DiaryService查询

### **前端显示方面**：
1. **主要入口**：日历界面点击日期
2. **列表组件**：`ChatSummaryCard` 显示摘要
3. **详情页面**：`ChatDetailScreen` 显示完整对话
4. **交互方式**：点击摘要卡片查看详情
5. **视觉设计**：卡片式布局，消息气泡显示

### **完整用户体验**：
```
Soul Mirror聊天 → 自动生成摘要 → 保存到数据库 → 日历中显示 → 点击查看详情
```
