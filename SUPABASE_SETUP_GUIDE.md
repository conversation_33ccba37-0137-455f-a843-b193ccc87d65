# 🚀 Supabase数据库设置指南

## ✅ 配置状态

你的Supabase配置已经更新：
- **项目URL**: `https://ktqlxbcauxomczubqasp.supabase.co`
- **匿名密钥**: 已正确配置
- **应用配置**: 已更新
- **数据库设计**: 已完成

## 📋 立即执行：创建数据库表

### 步骤1: 进入Supabase SQL编辑器
1. 访问 [你的Supabase项目](https://supabase.com/dashboard/project/ktqlxbcauxomczubqasp)
2. 点击左侧菜单 **SQL Editor**
3. 点击 **New Query**

### 步骤2: 执行完整的数据库建表脚本

**重要**: 请复制 `create_database.sql` 文件中的完整SQL代码并执行。

该脚本包含以下内容：
- ✅ 6个核心数据表
- ✅ 性能优化索引
- ✅ 行级安全策略
- ✅ 自动触发器
- ✅ 数据完整性约束

### 数据表结构概览：

1. **users** - 用户基础信息
2. **tarot_readings** - 塔罗解读记录
3. **daily_tarot** - 每日塔罗
4. **manifestation_records** - 显化记录
5. **user_preferences** - 用户偏好设置
6. **notification_settings** - 通知设置

```sql
-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 塔罗解读记录表
CREATE TABLE public.tarot_readings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    spread_type TEXT NOT NULL,
    cards JSONB NOT NULL,
    interpretation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 每日塔罗表
CREATE TABLE public.daily_tarot (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    card JSONB,
    fortune TEXT,
    advice TEXT,
    is_drawn BOOLEAN DEFAULT FALSE,
    manifestation_goal TEXT,
    affirmation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- 用户偏好设置表
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    preferences JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_tarot_readings_user_id ON public.tarot_readings(user_id);
CREATE INDEX idx_daily_tarot_user_date ON public.daily_tarot(user_id, date);

-- 启用行级安全
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tarot_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_tarot ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- 用户表安全策略
CREATE POLICY "Users can manage own profile" ON public.users
    USING (auth.uid() = id);

-- 塔罗解读安全策略
CREATE POLICY "Users can manage own readings" ON public.tarot_readings
    USING (auth.uid() = user_id);

-- 每日塔罗安全策略
CREATE POLICY "Users can manage own daily tarot" ON public.daily_tarot
    USING (auth.uid() = user_id);

-- 用户偏好安全策略
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    USING (auth.uid() = user_id);

-- 自动创建用户资料函数
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 新用户触发器
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### 步骤3: 点击 **Run** 执行SQL

## 🔧 认证设置

### 启用邮箱认证
1. 点击 **Authentication** → **Settings**
2. 在 **Auth Settings** 中确保：
   - ✅ **Enable email confirmations** 已启用
   - ✅ **Enable phone confirmations** 可选
3. 设置 **Site URL**: `http://localhost:3000` (开发环境)

## 📱 测试应用连接

现在重启你的Flutter应用并测试：

### 1. 重启应用
```bash
flutter run
```

### 2. 测试注册功能
1. 进入"我的"页面
2. 点击"账号登录"
3. 切换到"注册"标签
4. 输入邮箱和密码进行注册

### 3. 验证数据库
在Supabase仪表板中：
1. 点击 **Table Editor**
2. 查看 `users` 表是否有新用户数据

## 🎯 功能验证清单

完成设置后，请验证以下功能：

- [ ] 用户注册成功
- [ ] 用户登录成功
- [ ] 塔罗解读记录保存
- [ ] 每日塔罗数据同步
- [ ] 用户偏好设置保存

## 🚨 常见问题

### 问题1: 连接失败
**解决方案**: 检查网络连接，确认Supabase项目状态

### 问题2: 表创建失败
**解决方案**: 确认SQL语法正确，检查权限

### 问题3: 认证失败
**解决方案**: 检查邮箱确认设置，验证用户输入

## 📞 需要帮助？

如果遇到任何问题，请告诉我：
1. 具体的错误信息
2. 执行到哪一步出现问题
3. Supabase仪表板中的日志信息

---

🎉 **准备就绪！** 你的AI塔罗应用现在具备了完整的云端后端功能！
