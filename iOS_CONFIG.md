# iOS 18 配置文档

## 📱 iOS版本要求

本项目**永久设置为iOS 18.0**作为最低部署目标版本。

### 🎯 版本配置

- **最低iOS版本**: 18.0
- **目标设备**: iPhone 16 及以上
- **模拟器要求**: iPhone 16 iOS 18

## 🔧 配置文件

### 1. Xcode项目配置 (`ios/Runner.xcodeproj/project.pbxproj`)

```
IPHONEOS_DEPLOYMENT_TARGET = 18.0;
```

此设置在以下三个构建配置中都已设置：
- Debug
- Profile  
- Release

### 2. CocoaPods配置 (`ios/Podfile`)

```ruby
platform :ios, '18.0'
```

## 🚀 构建命令

### 标准构建命令
```bash
# 清理项目
flutter clean

# 获取依赖
flutter pub get

# 在iPhone 16模拟器上运行
flutter run -d "iPhone 16"

# 或使用设备ID运行
flutter run -d FB1695F9-37E6-4973-AF3A-D6B544B586AF
```

### 发布构建
```bash
# iOS发布构建
flutter build ios --release

# 构建IPA文件
flutter build ipa --release
```

## 📋 验证清单

在每次修改后，必须验证以下项目：

- [ ] 应用能在iPhone 16 iOS 18模拟器上正常启动
- [ ] 所有功能正常工作（聊天、日历、塔罗牌等）
- [ ] 没有iOS版本相关的警告或错误
- [ ] UI布局在iOS 18上显示正确
- [ ] 权限请求正常工作

## ⚠️ 重要注意事项

### 🚫 禁止操作
- **绝对不要**将iOS版本降级到18.0以下
- **不要**修改`IPHONEOS_DEPLOYMENT_TARGET`为其他值
- **不要**在旧版本iOS模拟器上测试

### ✅ 必须操作
- 每次代码修改后都要在iPhone 16 iOS 18上测试
- 保持Flutter和依赖包为最新稳定版本
- 定期检查iOS 18的新特性和API变更

## 🔄 依赖更新流程

### 定期更新命令
```bash
# 更新Flutter
flutter upgrade

# 更新依赖包
flutter pub upgrade

# 清理并重新构建
flutter clean
flutter pub get
cd ios && rm -rf Pods Podfile.lock && cd ..
flutter run -d "iPhone 16"
```

## 🛠 故障排除

### 常见问题

1. **构建失败**
   ```bash
   flutter clean
   flutter pub get
   cd ios && pod install && cd ..
   ```

2. **模拟器问题**
   ```bash
   # 列出可用设备
   flutter devices
   
   # 重启模拟器
   xcrun simctl shutdown all
   xcrun simctl boot "iPhone 16"
   ```

3. **依赖冲突**
   ```bash
   flutter pub deps
   flutter pub outdated
   ```

## 📊 当前环境信息

- **Flutter版本**: 3.32.8 (stable)
- **Dart版本**: 3.8.1
- **Xcode版本**: 需要支持iOS 18的最新版本
- **CocoaPods**: 最新稳定版本

## 🔗 相关文档

- [Flutter iOS部署指南](https://docs.flutter.dev/deployment/ios)
- [iOS 18开发者文档](https://developer.apple.com/ios/)
- [Xcode发布说明](https://developer.apple.com/xcode/releases/)

---

**最后更新**: 2025-07-28  
**维护者**: 开发团队  
**版本**: 1.0.0

> ⚠️ **重要提醒**: 此配置为永久设置，任何修改都必须经过完整测试验证！
