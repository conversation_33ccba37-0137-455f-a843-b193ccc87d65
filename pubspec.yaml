name: ai_tarot_reading
description: An AI-powered Tarot reading mobile app with Figma UI components.
publish_to: 'none'

version: 1.0.9+12

environment:
  sdk: '>=3.1.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.0.5
  intl: ^0.20.2
  flutter_chat_ui: ^2.9.0
  flutter_chat_types: ^3.6.2
  table_calendar: ^3.0.9
  shared_preferences: ^2.2.2
  uuid: ^4.1.0
  flutter_rating_bar: ^4.0.1
  flutter_animate: ^4.2.0+1
  google_fonts: ^6.1.0
  http: ^1.1.0
  image_picker: ^1.0.4
  sign_in_with_apple: ^7.0.1
  url_launcher: ^6.2.1
  permission_handler: ^12.0.1
  supabase_flutter: ^2.5.6
  crypto: ^3.0.3
  in_app_purchase: ^3.1.11
  sensors_plus: ^6.1.1
  flutter_local_notifications: ^19.4.0
  timezone: ^0.10.1
  connectivity_plus: ^6.1.4
  device_info_plus: ^11.5.0
  flame: ^1.12.0
  flame_forge2d: ^0.19.0+4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  # Langfuse目前没有官方Dart SDK，我们需要通过HTTP API集成

flutter:
  uses-material-design: true
  assets:
    - assets/images/
