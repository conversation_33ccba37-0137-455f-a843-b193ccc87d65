# 🚀 生产环境发布配置

## 版本信息
- **版本号**: 1.0.9 (Build 12)
- **发布类型**: App Store 正式版本
- **发布日期**: 2025年1月

## 🔧 生产环境配置更改

### 1. 订阅服务配置
- ✅ **测试模式**: 已禁用 (`_isTestFlightMode = false`)
- ✅ **收据验证**: 启用严格验证模式
- ✅ **调试日志**: 仅在调试模式下输出
- ✅ **错误处理**: 生产环境严格错误处理

### 2. 应用内购买产品ID
```
基础版订阅:
- 周订阅: com.G3RHCPDDQR.aitarotreading.basic_weekly_usd
- 月订阅: com.G3RHCPDDQR.aitarotreading.basic_monthly_usd  
- 年订阅: com.G3RHCPDDQR.aitarotreading.basic_yearly_usd

高级版订阅:
- 周订阅: com.G3RHCPDDQR.aitarotreading.p_weekly_usd
- 月订阅: com.G3RHCPDDQR.aitarotreading.p_monthly_usd
- 年订阅: com.G3RHCPDDQR.aitarotreading.p_yearly_usd
```

### 3. 收据验证配置
- **验证服务**: Supabase Edge Function (`swift-responder`)
- **验证模式**: 生产环境严格验证
- **错误处理**: 验证失败时拒绝购买
- **沙盒支持**: 自动检测沙盒/生产环境

## 📱 App Store Connect 检查清单

### 必须完成的配置:
- [ ] **应用内购买产品**: 所有产品状态为 "Ready for Sale"
- [ ] **App Store Connect**: 应用版本已提交审核
- [ ] **测试账号**: 移除所有沙盒测试账号配置
- [ ] **收据验证**: 确认 Edge Function 部署正常
- [ ] **权限说明**: 检查所有权限描述文案

### 产品配置验证:
```bash
# 检查产品ID是否正确配置
基础版周订阅: com.G3RHCPDDQR.aitarotreading.basic_weekly_usd
基础版月订阅: com.G3RHCPDDQR.aitarotreading.basic_monthly_usd
基础版年订阅: com.G3RHCPDDQR.aitarotreading.basic_yearly_usd
高级版周订阅: com.G3RHCPDDQR.aitarotreading.p_weekly_usd
高级版月订阅: com.G3RHCPDDQR.aitarotreading.p_monthly_usd
高级版年订阅: com.G3RHCPDDQR.aitarotreading.p_yearly_usd
```

## 🔒 安全配置

### 收据验证安全:
- ✅ 使用 Apple 官方收据验证 API
- ✅ 通过 Supabase Edge Function 验证
- ✅ 防止收据重放攻击
- ✅ 严格的错误处理和日志记录

### 用户数据安全:
- ✅ 本地数据加密存储
- ✅ 网络传输 HTTPS 加密
- ✅ 用户隐私保护合规

## 🚀 发布流程

### 1. 构建 Archive
```bash
# 清理项目
flutter clean
flutter pub get

# 构建 iOS Archive
flutter build ios --release
```

### 2. Xcode Archive
1. 打开 `ios/Runner.xcworkspace`
2. 选择 "Any iOS Device" 
3. Product → Archive
4. 上传到 App Store Connect

### 3. App Store Connect 配置
1. 设置应用元数据
2. 配置应用内购买产品
3. 设置价格和可用性
4. 提交审核

## ⚠️ 重要注意事项

### 订阅功能:
- 确保所有产品在 App Store Connect 中状态为 "Ready for Sale"
- 测试所有订阅流程在生产环境中正常工作
- 验证收据验证服务在生产环境中可用

### 用户体验:
- 所有付费功能在订阅后立即可用
- 错误消息用户友好且本地化
- 网络异常时有适当的重试机制

### 合规性:
- 隐私政策已更新
- 服务条款已更新  
- 应用内购买说明清晰

## 📊 监控和分析

### 关键指标监控:
- 订阅转化率
- 收据验证成功率
- 应用崩溃率
- 用户留存率

### 错误监控:
- 订阅购买失败率
- 收据验证失败原因
- 网络请求失败率

## 🔄 发布后验证

### 必须验证的功能:
1. **订阅购买流程**: 完整测试所有订阅产品
2. **收据验证**: 确认验证服务正常工作
3. **功能解锁**: 订阅后所有付费功能可用
4. **用户界面**: 所有界面显示正确的订阅状态
5. **错误处理**: 网络异常时的用户体验

---

**发布负责人**: 开发团队  
**发布日期**: 2025年1月  
**版本**: 1.0.9 (Build 12)
