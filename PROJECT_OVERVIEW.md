# 🌟 GrowCard - 项目概览

## 📱 应用简介

GrowCard是一款革命性的AI驱动心灵成长应用，专注于识别和化解阻碍显化的内在障碍。与传统算命应用不同，GrowCard强调赋权和内在转化。

## ✨ 核心功能

### 🎯 显化目标设定
- 个性化目标设置界面
- 目标追踪和进度管理
- 显化指导和建议

### 💬 Soul Mirror 对话系统
三种对话模式：
1. **探索内心** - 心灵指引和洞察
2. **分享美好** - 正向强化和庆祝
3. **站在终点** - 未来自我显化指导

### 📅 日历集成
- 心灵成长日记
- 对话记录自动保存
- 成长轨迹可视化

### 🌍 多语言支持
- 中文（简体/繁体）
- 英语
- 西班牙语
- 日语
- 韩语

### 💎 订阅系统
- 免费版：基础功能
- 基础会员：$2.99/周
- 高级会员：$4.99/周

## 🛠 技术架构

### 前端技术栈
- **框架**: Flutter 3.x
- **状态管理**: Provider
- **UI设计**: 毛玻璃风格，渐变背景
- **动画**: Flutter Animate

### 后端服务
- **数据库**: Supabase (PostgreSQL)
- **认证**: Apple Sign-In, 邮箱登录
- **存储**: Supabase Storage
- **实时功能**: Supabase Realtime

### AI集成
- **主要AI服务**: DeepSeek AI
- **功能**: 个性化心灵指导
- **特色**: 情感分析和心理模式识别

### 支付系统
- **平台**: Apple In-App Purchases
- **货币**: USD
- **周期**: 周/月/年订阅

## 📂 项目结构

```
lib/
├── models/          # 数据模型
│   ├── soul_message.dart
│   ├── user.dart
│   └── tarot_card.dart
├── screens/         # 页面组件
│   ├── soul_mirror_chat_screen.dart
│   ├── manifestation_goal_screen.dart
│   └── subscription_screen.dart
├── widgets/         # UI组件
│   ├── soul_chat_bubble.dart
│   └── soul_choice_buttons.dart
├── services/        # 业务逻辑
│   ├── higher_self_service.dart
│   ├── deepseek_service.dart
│   └── supabase_auth_service.dart
├── providers/       # 状态管理
├── utils/           # 工具类
└── theme/           # 主题配置
```

## 🚀 最新更新 (v1.0)

### ✨ 新功能
- 添加"站在终点"功能
- 增强AI回应个性化
- 改进加载状态显示

### 🐛 Bug修复
- 修复订阅界面导航问题
- 修复绿色标签位置重叠
- 移除所有调试面板
- 修复通知国际化问题

### 🎨 UI/UX改进
- 优化订阅卡片布局
- 增强用户反馈机制
- 改进错误处理

## 🔐 安全与隐私

- 端到端数据加密
- GDPR合规数据处理
- Apple隐私指南合规
- Supabase安全认证

## 📱 平台支持

- ✅ iOS 12.0+
- 🔄 Android (开发中)
- 🔄 Web (计划中)

## 🎨 设计理念

GrowCard采用心灵和神秘的设计语言：
- 渐变背景和空灵色彩
- 毛玻璃UI元素
- 流畅动画和过渡
- 直观用户体验

## 📊 性能指标

- 应用启动时间: <2秒
- AI响应时间: <3秒
- 内存使用: <100MB
- 电池优化: 后台最小化

## 🔮 未来规划

### 短期目标 (1-3个月)
- Android版本发布
- 更多AI模型集成
- 社区功能开发

### 中期目标 (3-6个月)
- Web版本发布
- 高级分析功能
- 个性化推荐系统

### 长期目标 (6-12个月)
- 全球市场扩展
- 企业版本开发
- AR/VR功能集成

## 📞 联系信息

- **开发团队**: [团队邮箱]
- **技术支持**: [支持邮箱]
- **商务合作**: [商务邮箱]

---

**用❤️构建，为心灵成长和显化而生**
