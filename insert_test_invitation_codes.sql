-- 插入测试邀请码数据
-- 在 Supabase SQL Editor 中执行此脚本

-- 插入预设邀请码
INSERT INTO public.invitation_codes (code, code_type, reward_type, reward_value, max_uses, description) VALUES
('HELLO1', 'system', 'weekly_membership', 7, -1, '新用户专享周会员'),
('LUCK88', 'system', 'weekly_membership', 7, -1, '幸运活动周会员'),
('VIP024', 'system', 'weekly_membership', 7, -1, '2024特别版周会员'),
('SPRING', 'system', 'weekly_membership', 7, -1, '春季限定周会员'),
('FRIEND', 'system', 'weekly_membership', 7, -1, '好友推荐周会员'),
('BETA01', 'system', 'weekly_membership', 7, -1, '内测用户专享'),
('GIFT99', 'system', 'weekly_membership', 7, -1, '礼品码周会员'),
('TEST01', 'system', 'weekly_membership', 7, -1, '测试专用邀请码')
ON CONFLICT (code) DO NOTHING;

-- 查看插入的数据
SELECT code, description, is_active, max_uses, current_uses 
FROM public.invitation_codes 
ORDER BY created_at DESC;
