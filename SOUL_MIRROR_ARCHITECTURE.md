# Soul Mirror (灵魂之镜) 架构文档

## 🎯 功能概述

Soul Mirror是AI塔罗应用中的**高我对话功能**，允许用户与自己的"高我"进行深度对话，获得内在智慧和指导。

## 📱 功能入口

**主要入口**：主页底部导航 → "Soul Mirror" 或 "灵魂之镜"

## 🏗️ 核心架构

### 1. 主要文件结构

```
lib/screens/soul_mirror_chat_screen.dart    # 主聊天界面
lib/services/higher_self_service.dart       # 高我对话服务
lib/services/higher_self_memory_service.dart # 高我记忆系统
lib/widgets/soul_chat_bubble.dart          # 聊天气泡组件
lib/widgets/soul_choice_buttons.dart       # 选择按钮组件
lib/models/soul_message.dart               # 消息模型
```

### 2. AI服务架构

**当前使用**：DeepSeek API (通过Supabase Edge Function)
**备选方案**：可切换到GPT API

**服务调用链**：
```
用户输入 → HigherSelfService → DeepSeekService → Supabase Edge Function → DeepSeek API
```

**Fallback机制**：
```
AI服务失败 → 本地智慧回应 → 确保用户始终获得回应
```

## 🔄 消息处理流程

### 1. 消息类型识别

```dart
// 在 HigherSelfService.getHigherSelfResponse()
if (_isNumberSequence(userMessage)) {
    // 塔罗数字解读 (1-78的3个数字)
    await _performTarotReading(userMessage, language);
} else if (_isPraiseRequest(userMessage)) {
    // 分享美好/夸夸功能
    await _generatePraiseResponse(userMessage, language);
} else {
    // 普通高我对话
    await _generatePersonalizedResponse(userMessage, language);
}
```

### 2. 回应生成策略

**个性化回应**：
- 使用用户历史日记数据
- 调用 `HigherSelfMemoryService.generatePersonalizedResponse()`
- 失败时降级到基础回应

**基础回应**：
- 使用 `_generateBasicResponse()` 
- 包含本地智慧回应 + AI增强
- 失败时使用纯本地回应

## 🎨 UI组件说明

### 1. 聊天气泡 (SoulChatBubble)

**用户消息**：紫色渐变背景，白色文字
**AI消息**：磨砂玻璃效果，深色文字，透明背景

### 2. 选择按钮 (SoulChoiceButtons)

**主要选项**：
- 🧘 探索内心 → 触发内在探索对话
- ✨ 分享美好 → 触发分享/夸夸功能

**分享子选项**：
- 🎉 开心 → 分享快乐时光
- 🌱 成长 → 分享成长经历  
- 💪 努力 → 分享努力时刻
- 🙏 感恩 → 分享感恩回忆

## 🔧 配置和设置

### 1. AI服务配置

**DeepSeek配置**：
- API通过Supabase Edge Function调用
- 函数名：`deepseek-tarot-reading`
- 模型：`deepseek-chat`

**GPT配置**（备选）：
- 可通过修改服务调用切换到OpenAI API
- 需要配置API密钥和端点

### 2. 语言支持

- 中文 (zh)
- 英文 (en) 
- 日文 (ja) - 部分支持

## 🚨 常见问题

### 1. AI回应不工作
- 检查Supabase Edge Function状态
- 查看DeepSeek API配额
- 确认网络连接
- 查看fallback机制是否生效

### 2. 消息类型识别错误
- 检查 `_isNumberSequence()` 逻辑
- 检查 `_isPraiseRequest()` 关键词
- 调整关键词匹配规则

### 3. UI显示问题
- 确认磨砂玻璃效果 (BackdropFilter)
- 检查选择按钮布局
- 验证颜色对比度

## 🔄 切换到GPT API

如需切换到GPT，需要修改：
1. `DeepSeekService` → `OpenAIService`
2. 更新API端点和认证
3. 调整prompt格式
4. 测试回应质量

## 📝 开发注意事项

1. **始终保持fallback机制**：确保AI服务失败时用户仍能获得回应
2. **消息类型要明确**：不同类型消息有不同处理逻辑
3. **UI要保持一致性**：与显化目标页面等保持设计一致
4. **多语言支持**：所有文本都要支持多语言
5. **调试信息要详细**：便于排查问题

---

**最后更新**：2025-01-13
**版本**：Soul Mirror v2.0
