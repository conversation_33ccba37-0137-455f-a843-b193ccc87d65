<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>塔罗解读 - 云朵背景演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            height: 100vh;
            background: linear-gradient(
                to bottom,
                #E8F4FD,
                #F8E8FF,
                #FFE8F8
            );
            overflow: hidden;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .cloud-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .cloud-1 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                circle at 30% -50%,
                rgba(255, 255, 255, 0.8) 0%,
                transparent 70%
            );
        }

        .cloud-2 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                circle at -40% 20%,
                rgba(255, 255, 255, 0.6) 0%,
                transparent 60%
            );
        }

        .header {
            background: rgba(45, 55, 72, 0.9);
            color: white;
            text-align: center;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
        }

        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 24px;
            position: relative;
            z-index: 1;
        }

        .mascot-area {
            flex: 2;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 40px;
        }

        .mascot-container {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: radial-gradient(
                circle,
                rgba(116, 109, 255, 0.2) 0%,
                rgba(185, 181, 251, 0.1) 70%,
                transparent 100%
            );
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            animation: float 2s ease-in-out infinite alternate;
        }

        .mascot-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 2px solid rgba(116, 109, 255, 0.3);
            border-radius: 50%;
            animation: pulse 3s ease-in-out infinite;
        }

        .mascot-icon {
            width: 80px;
            height: 80px;
            background: #746DFF;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 8px 20px rgba(116, 109, 255, 0.4);
            color: white;
            font-size: 40px;
        }

        .question-area {
            flex: 2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 32px;
        }

        .question-title {
            font-size: 24px;
            font-weight: bold;
            color: #2D3748;
            text-align: center;
        }

        .question-input {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 16px;
            padding: 20px;
            font-size: 16px;
            color: #2D3748;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            resize: none;
            height: 120px;
        }

        .question-input::placeholder {
            color: #A0AEC0;
        }

        .continue-button {
            width: 100%;
            height: 87px;
            background: #746DFF;
            border: 1px solid #B9B5FB;
            border-radius: 33px;
            color: #DEDCFE;
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            font-size: 28.2px;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(116, 109, 255, 0.3);
            transition: all 0.3s ease;
        }

        .continue-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(116, 109, 255, 0.4);
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-8px); }
        }

        @keyframes pulse {
            0%, 100% { 
                transform: scale(0.8);
                opacity: 1;
            }
            50% { 
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .bottom-nav {
            display: flex;
            background: rgba(45, 55, 72, 0.9);
            padding: 12px 0;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #A0AEC0;
            font-size: 12px;
            gap: 4px;
        }

        .nav-item.active {
            color: #746DFF;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            background: currentColor;
            mask-size: contain;
            mask-repeat: no-repeat;
            mask-position: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 云朵背景 -->
        <div class="cloud-background">
            <div class="cloud-1"></div>
            <div class="cloud-2"></div>
        </div>

        <!-- 顶部导航 -->
        <div class="header">
            塔罗解读
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <!-- 吉祥物区域 -->
            <div class="mascot-area">
                <div class="mascot-container">
                    <div class="mascot-ring"></div>
                    <div class="mascot-icon">✨</div>
                </div>
            </div>

            <!-- 问题输入区域 -->
            <div class="question-area">
                <h2 class="question-title">您想了解什么？</h2>
                <textarea class="question-input" placeholder="请输入您的问题..."></textarea>
                <button class="continue-button">继续</button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <div class="nav-icon"></div>
                <span>历史</span>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"></div>
                <span>解读</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon"></div>
                <span>每日</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon"></div>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
