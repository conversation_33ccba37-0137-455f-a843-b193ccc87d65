# 页面映射关系文档

## 🚨 重要提醒：请勿随意修改页面映射！

当前的页面映射关系是**正确的**，请严格按照以下映射关系维护代码：

## 📱 底部导航栏页面映射

### 正确的映射关系 (lib/screens/home_screen.dart)

```dart
Widget _buildCurrentScreen(int index) {
  switch (index) {
    case 0:
      return const HistoryScreen();        // Space界面 - 显化目标管理页面
    case 1:
      return const TarotMainPage();        // Tarot界面 - 塔罗主页
    case 2:
      return const DailyTarotScreen();     // Manifestation界面 - 每日塔罗
    case 3:
      return const UserAccountScreen();    // Profile界面 - 用户资料
    default:
      return const TarotReadingScreen();
  }
}
```

### 导航标签对应关系

```dart
Row(
  mainAxisAlignment: MainAxisAlignment.spaceAround,
  children: [
    _buildNavItem(0, Icons.auto_awesome, 'nav_space', appState),        // Space - 显化目标管理
    _buildNavItem(1, Icons.auto_awesome, 'nav_tarot', appState),        // Tarot - 塔罗主页
    _buildNavItem(2, Icons.star, 'nav_manifestation', appState),        // Manifestation - 每日塔罗
    _buildNavItem(3, Icons.person, 'nav_profile', appState),            // Profile - 用户资料
  ],
);
```

## 🎯 各页面功能说明

### Space界面 (索引0) - HistoryScreen
- **主要功能**：显化目标管理页面（成长记录）
- **包含内容**：
  - 显化目标列表
  - 目标状态管理（待显化/正显化/已显化）
  - AI肯定语生成
  - 目标统计和搜索功能
- **文件位置**：`lib/screens/history_screen.dart`

### Tarot界面 (索引1) - TarotMainPage
- **主要功能**：塔罗主页，应用的核心入口
- **包含内容**：
  - 滚动话题画廊
  - 开始占卜按钮
  - 与高我对话功能
  - 各种塔罗牌阵选择
- **文件位置**：`lib/screens/tarot_main_page.dart`

### Manifestation界面 (索引2) - DailyTarotScreen
- **主要功能**：每日塔罗功能
- **包含内容**：
  - 每日塔罗抽牌
  - 每日运势解读
  - 历史记录查看
- **文件位置**：`lib/screens/daily_tarot_screen.dart`

### Profile界面 (索引3) - UserAccountScreen
- **主要功能**：用户个人资料和设置
- **包含内容**：
  - 用户信息
  - 会员状态
  - 应用设置
  - 账户管理
- **文件位置**：`lib/screens/user_account_screen.dart`

## ⚠️ 修改注意事项

1. **禁止随意修改页面映射**：除非有明确的产品需求变更
2. **修改前必须确认**：与产品经理确认页面功能调整
3. **保持一致性**：导航标签名称与页面功能必须匹配
4. **测试验证**：修改后必须全面测试所有导航功能

## 📝 修改记录

- 2024-01-XX：创建文档，记录当前正确的页面映射关系
- 用户明确要求：**不要修改页面映射，只优化UI**

---

**最后更新**：2024-01-XX  
**维护者**：开发团队  
**状态**：当前映射关系正确，请勿修改
