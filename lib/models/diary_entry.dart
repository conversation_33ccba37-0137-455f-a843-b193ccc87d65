class DiaryEntry {
  final String id;
  final String userId;
  final String content;
  final int? moodScore; // 1-10，心情评分
  final List<String> tags; // 情感标签
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? chatSummary; // 聊天摘要
  final String chatSource; // 'manual', 'soul_mirror', 'tarot'
  final String? chatSessionId; // 聊天会话ID

  DiaryEntry({
    required this.id,
    required this.userId,
    required this.content,
    this.moodScore,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
    this.chatSummary,
    this.chatSource = 'manual',
    this.chatSessionId,
  });

  factory DiaryEntry.fromJson(Map<String, dynamic> json) {
    return DiaryEntry(
      id: json['id'],
      userId: json['user_id'],
      content: json['content'],
      moodScore: json['mood_score'],
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      chatSummary: json['chat_summary'],
      chatSource: json['chat_source'] ?? 'manual',
      chatSessionId: json['chat_session_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'content': content,
      'mood_score': moodScore,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'chat_summary': chatSummary,
      'chat_source': chatSource,
      'chat_session_id': chatSessionId,
    };
  }

  String get moodEmoji {
    if (moodScore == null) return '😐';
    if (moodScore! >= 9) return '😄';
    if (moodScore! >= 7) return '😊';
    if (moodScore! >= 5) return '😐';
    if (moodScore! >= 3) return '😔';
    return '😢';
  }

  String get summary {
    if (content.length <= 50) return content;
    return '${content.substring(0, 50)}...';
  }
}
