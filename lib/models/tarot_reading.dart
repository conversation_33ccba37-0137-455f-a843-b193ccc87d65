import 'package:ai_tarot_reading/models/tarot_card.dart';

class TarotReading {
  final String id;
  final String question;
  final SpreadType spreadType;
  final List<TarotCard> cards;
  final String interpretation;
  final DateTime date;
  final List<String> followUpQuestions;
  final List<String> followUpResponses;
  final int? accuracy;
  final int? usefulness;
  final int? satisfaction;
  final String? feedback;

  TarotReading({
    required this.id,
    required this.question,
    required this.spreadType,
    required this.cards,
    required this.interpretation,
    required this.date,
    this.followUpQuestions = const [],
    this.followUpResponses = const [],
    this.accuracy,
    this.usefulness,
    this.satisfaction,
    this.feedback,
  });

  TarotReading copyWith({
    String? id,
    String? question,
    SpreadType? spreadType,
    List<TarotCard>? cards,
    String? interpretation,
    DateTime? date,
    List<String>? followUpQuestions,
    List<String>? followUpResponses,
    int? accuracy,
    int? usefulness,
    int? satisfaction,
    String? feedback,
  }) {
    return TarotReading(
      id: id ?? this.id,
      question: question ?? this.question,
      spreadType: spreadType ?? this.spreadType,
      cards: cards ?? this.cards,
      interpretation: interpretation ?? this.interpretation,
      date: date ?? this.date,
      followUpQuestions: followUpQuestions ?? this.followUpQuestions,
      followUpResponses: followUpResponses ?? this.followUpResponses,
      accuracy: accuracy ?? this.accuracy,
      usefulness: usefulness ?? this.usefulness,
      satisfaction: satisfaction ?? this.satisfaction,
      feedback: feedback ?? this.feedback,
    );
  }
}
