
/// 显化目标状态枚举
enum ManifestationStatus {
  pending,     // 待显化
  manifesting, // 正显化
  manifested,  // 已显化
}

/// 同步状态枚举
enum SyncStatus {
  synced,   // 已同步
  pending,  // 待同步
  conflict, // 冲突
}

/// 显化目标数据模型
class ManifestationGoal {
  final String id;
  final String? userId; // Supabase用户ID
  final String title;
  final String? description;
  final ManifestationStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? affirmation; // AI生成的肯定语
  final bool isAffirmationGenerated; // 是否已生成肯定语
  final DateTime? deletedAt; // 软删除时间
  final String? localId; // 本地存储ID，用于迁移
  final SyncStatus syncStatus; // 同步状态
  final DateTime? lastSyncedAt; // 最后同步时间

  ManifestationGoal({
    required this.id,
    this.userId,
    required this.title,
    this.description,
    this.status = ManifestationStatus.pending,
    required this.createdAt,
    this.updatedAt,
    this.affirmation,
    this.isAffirmationGenerated = false,
    this.deletedAt,
    this.localId,
    this.syncStatus = SyncStatus.pending,
    this.lastSyncedAt,
  });

  /// 创建副本并更新指定字段
  ManifestationGoal copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    ManifestationStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? affirmation,
    bool? isAffirmationGenerated,
    DateTime? deletedAt,
    String? localId,
    SyncStatus? syncStatus,
    DateTime? lastSyncedAt,
  }) {
    return ManifestationGoal(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      affirmation: affirmation ?? this.affirmation,
      isAffirmationGenerated: isAffirmationGenerated ?? this.isAffirmationGenerated,
      deletedAt: deletedAt ?? this.deletedAt,
      localId: localId ?? this.localId,
      syncStatus: syncStatus ?? this.syncStatus,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
    );
  }

  /// 转换为JSON (本地存储)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'description': description,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'affirmation': affirmation,
      'isAffirmationGenerated': isAffirmationGenerated,
      'deletedAt': deletedAt?.toIso8601String(),
      'localId': localId,
      'syncStatus': syncStatus.name,
      'lastSyncedAt': lastSyncedAt?.toIso8601String(),
    };
  }

  /// 转换为Supabase JSON格式
  Map<String, dynamic> toSupabaseJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'affirmation': affirmation,
      'is_affirmation_generated': isAffirmationGenerated,
      'deleted_at': deletedAt?.toIso8601String(),
      'local_id': localId,
      'sync_status': syncStatus.name,
      'last_synced_at': lastSyncedAt?.toIso8601String(),
    };
  }

  /// 从JSON创建对象 (本地存储)
  factory ManifestationGoal.fromJson(Map<String, dynamic> json) {
    return ManifestationGoal(
      id: json['id'] as String,
      userId: json['userId'] as String?,
      title: json['title'] as String,
      description: json['description'] as String?,
      status: ManifestationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ManifestationStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      affirmation: json['affirmation'] as String?,
      isAffirmationGenerated: json['isAffirmationGenerated'] as bool? ?? false,
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'] as String)
          : null,
      localId: json['localId'] as String?,
      syncStatus: SyncStatus.values.firstWhere(
        (e) => e.name == json['syncStatus'],
        orElse: () => SyncStatus.pending,
      ),
      lastSyncedAt: json['lastSyncedAt'] != null
          ? DateTime.parse(json['lastSyncedAt'] as String)
          : null,
    );
  }

  /// 从Supabase JSON创建对象
  factory ManifestationGoal.fromSupabaseJson(Map<String, dynamic> json) {
    return ManifestationGoal(
      id: json['id'] as String,
      userId: json['user_id'] as String?,
      title: json['title'] as String,
      description: json['description'] as String?,
      status: ManifestationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ManifestationStatus.pending,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      affirmation: json['affirmation'] as String?,
      isAffirmationGenerated: json['is_affirmation_generated'] as bool? ?? false,
      deletedAt: json['deleted_at'] != null
          ? DateTime.parse(json['deleted_at'] as String)
          : null,
      localId: json['local_id'] as String?,
      syncStatus: SyncStatus.values.firstWhere(
        (e) => e.name == json['sync_status'],
        orElse: () => SyncStatus.synced,
      ),
      lastSyncedAt: json['last_synced_at'] != null
          ? DateTime.parse(json['last_synced_at'] as String)
          : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ManifestationGoal && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ManifestationGoal(id: $id, title: $title, status: $status)';
  }
}

/// 显化目标状态扩展方法
extension ManifestationStatusExtension on ManifestationStatus {
  /// 获取状态对应的图标
  String get icon {
    switch (this) {
      case ManifestationStatus.pending:
        return '⏳';
      case ManifestationStatus.manifesting:
        return '🌟';
      case ManifestationStatus.manifested:
        return '✨';
    }
  }

  /// 获取状态对应的颜色
  int get colorValue {
    switch (this) {
      case ManifestationStatus.pending:
        return 0xFF9E9E9E; // 灰色
      case ManifestationStatus.manifesting:
        return 0xFFFF9800; // 橙色
      case ManifestationStatus.manifested:
        return 0xFF4CAF50; // 绿色
    }
  }
}

/// 同步状态扩展方法
extension SyncStatusExtension on SyncStatus {
  /// 获取状态对应的图标
  String get icon {
    switch (this) {
      case SyncStatus.synced:
        return '✅';
      case SyncStatus.pending:
        return '⏳';
      case SyncStatus.conflict:
        return '⚠️';
    }
  }

  /// 获取状态对应的颜色
  int get colorValue {
    switch (this) {
      case SyncStatus.synced:
        return 0xFF4CAF50; // 绿色
      case SyncStatus.pending:
        return 0xFFFF9800; // 橙色
      case SyncStatus.conflict:
        return 0xFFF44336; // 红色
    }
  }
}
