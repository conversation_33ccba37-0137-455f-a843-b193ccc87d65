class SoulMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final SoulMessageType messageType;
  final EnergyLevel energyLevel;
  final List<String>? tarotCards;
  final Map<String, dynamic>? metadata;
  final bool isLoading; // 新增：是否为加载状态

  SoulMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.messageType = SoulMessageType.normal,
    this.energyLevel = EnergyLevel.neutral,
    this.tarotCards,
    this.metadata,
    this.isLoading = false, // 默认不是加载状态
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'messageType': messageType.toString(),
      'energyLevel': energyLevel.toString(),
      'tarotCards': tarotCards,
      'metadata': metadata,
      'isLoading': isLoading,
    };
  }

  factory SoulMessage.fromJson(Map<String, dynamic> json) {
    return SoulMessage(
      id: json['id'],
      content: json['content'],
      isUser: json['isUser'],
      timestamp: DateTime.parse(json['timestamp']),
      messageType: SoulMessageType.values.firstWhere(
        (e) => e.toString() == json['messageType'],
        orElse: () => SoulMessageType.normal,
      ),
      energyLevel: EnergyLevel.values.firstWhere(
        (e) => e.toString() == json['energyLevel'],
        orElse: () => EnergyLevel.neutral,
      ),
      tarotCards: json['tarotCards']?.cast<String>(),
      metadata: json['metadata'],
      isLoading: json['isLoading'] ?? false,
    );
  }
}

enum SoulMessageType {
  welcome,
  normal,
  tarotRequest,
  tarotReading,
  insight,
  healing,
  guidance,
  exploration, // 探索内心模式
}

enum EnergyLevel {
  divine,     // 神圣能量 - 金色
  mystical,   // 神秘能量 - 紫色
  healing,    // 疗愈能量 - 绿色
  wisdom,     // 智慧能量 - 蓝色
  love,       // 爱的能量 - 粉色
  neutral,    // 中性能量 - 白色
}
