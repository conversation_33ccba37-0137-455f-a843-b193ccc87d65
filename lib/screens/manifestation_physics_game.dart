import 'dart:async';
import 'dart:ui' as ui;
import 'package:flame/components.dart' as flame;
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flame_forge2d/flame_forge2d.dart' as forge2d;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';

/// 罐子物理世界主类
class ManifestationPhysicsGame extends forge2d.Forge2DGame {
  ManifestationPhysicsGame({
    this.iconImageProvider,
    this.goal,
    this.version,
  }) : super(gravity: forge2d.Vector2(0, 20)); // 增加重力

  final ImageProvider? iconImageProvider;
  final ManifestationGoal? goal;
  final ManifestationVersion? version;

  late double worldWidth;
  late double worldHeight;

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    worldWidth = size.x;
    worldHeight = size.y;
    
    // 添加罐子边界 - 优化边界位置
    addAll([
      Wall(forge2d.Vector2(worldWidth / 2, worldHeight - 5), worldWidth, 10), // 底部 - 更薄
      Wall(forge2d.Vector2(5, worldHeight / 2), 10, worldHeight), // 左侧 - 更薄
      Wall(forge2d.Vector2(worldWidth - 5, worldHeight / 2), 10, worldHeight), // 右侧 - 更薄
    ]);
  }

  // 添加图标到指定位置的方法
  void addIconAtPosition(Offset position) {
    // 限制点击范围，避免在边界处生成
    if (position.dx > 20 && position.dx < worldWidth - 20 && 
        position.dy > 20 && position.dy < worldHeight - 20) {
      add(FallingIcon(
        position: forge2d.Vector2(position.dx, 50), // 从顶部生成
        iconImageProvider: _getCurrentIconProvider(),
      ));
    }
  }

  // 获取当前图标提供者
  ImageProvider _getCurrentIconProvider() {
    if (iconImageProvider != null) {
      return iconImageProvider!;
    }
    
    // 根据目标和版本返回默认图标
    switch (goal) {
      case ManifestationGoal.wealth:
        if (version == ManifestationVersion.eastern) {
          // 东方版财富：使用金元宝图片
          return const AssetImage('assets/images/gold_ingot_1.png');
        } else {
          // 西方版财富：使用钻石图片
          return const AssetImage('assets/images/money.png');
        }
      case ManifestationGoal.career:
        return const AssetImage('assets/images/money.png'); // 使用通用图标
      case ManifestationGoal.beauty:
        return const AssetImage('assets/images/money.png'); // 使用通用图标
      case ManifestationGoal.fame:
        return const AssetImage('assets/images/money.png'); // 使用通用图标
      case ManifestationGoal.love:
        return const AssetImage('assets/images/money.png'); // 使用通用图标
      // 其他目标都使用通用图标
      default:
        return const AssetImage('assets/images/money.png'); // 默认图标
    }
  }
}

/// 罐子边界
class Wall extends forge2d.BodyComponent {
  final forge2d.Vector2 wallCenter;
  final double width;
  final double height;
  Wall(this.wallCenter, this.width, this.height);

  @override
  forge2d.Body createBody() {
    final shape = forge2d.PolygonShape()..setAsBoxXY(width / 2, height / 2);
    final fixtureDef = forge2d.FixtureDef(
      shape,
      friction: 0.3, // 减少摩擦
      restitution: 0.2, // 减少弹性
    );
    final bodyDef = forge2d.BodyDef(position: wallCenter, type: forge2d.BodyType.static);
    final body = world.createBody(bodyDef)..createFixture(fixtureDef);
    return body;
  }
}

/// 下坠物体（支持PNG图片）
class FallingIcon extends forge2d.BodyComponent {
  final forge2d.Vector2 position;
  final ImageProvider iconImageProvider;
  FallingIcon({required this.position, required this.iconImageProvider});

  late flame.SpriteComponent spriteComponent;
  bool _isLoaded = false;

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    try {
      // 通过ImageProvider加载ui.Image
      final completer = Completer<ui.Image>();
      final stream = iconImageProvider.resolve(const ImageConfiguration());
      late ImageStreamListener listener;
      
      listener = ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(info.image);
        stream.removeListener(listener);
      });
      
      stream.addListener(listener);
      final uiImage = await completer.future;
      
      // 创建精灵组件
      final sprite = flame.Sprite(uiImage);
      spriteComponent = flame.SpriteComponent(
        sprite: sprite,
        size: flame.Vector2(40, 40), // 稍微减小尺寸
        anchor: flame.Anchor.center,
      );
      
      // 添加到组件树
      add(spriteComponent);
      _isLoaded = true;
      
      // 初始位置设置
      spriteComponent.position = flame.Vector2(body.position.x, body.position.y);
      
    } catch (e) {
      debugPrint('❌ 加载图标失败: $e');
      // 如果加载失败，移除这个组件
      removeFromParent();
    }
  }

  @override
  forge2d.Body createBody() {
    // 使用更小的碰撞体，避免过度旋转
    final shape = forge2d.CircleShape()..radius = 18;
    final fixtureDef = forge2d.FixtureDef(
      shape,
      density: 0.8, // 减少密度
      restitution: 0.3, // 减少弹性
      friction: 0.4, // 增加摩擦
    );
    
    final bodyDef = forge2d.BodyDef(
      position: position,
      type: forge2d.BodyType.dynamic,
      linearDamping: 0.5, // 添加线性阻尼，减少过度运动
      angularDamping: 0.8, // 添加角度阻尼，减少旋转
    );
    
    return world.createBody(bodyDef)..createFixture(fixtureDef);
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // 只有在加载完成后才更新精灵位置
    if (_isLoaded && spriteComponent.parent == this) {
      spriteComponent.position = flame.Vector2(body.position.x, body.position.y);
      
      // 限制旋转角度，避免过度旋转
      final angle = body.angle;
      if (angle.abs() > 0.5) { // 如果旋转角度过大
        body.setTransform(body.position, 0); // 重置旋转
      }
    }
  }
}

/// Flutter页面嵌入物理世界
class ManifestationPhysicsWidget extends StatefulWidget {
  final ImageProvider? iconImageProvider;
  final VoidCallback? onTap; // 添加点击回调
  final ManifestationGoal? goal;
  final ManifestationVersion? version;
  
  const ManifestationPhysicsWidget({
    super.key, 
    this.iconImageProvider,
    this.onTap,
    this.goal,
    this.version,
  });

  @override
  State<ManifestationPhysicsWidget> createState() => _ManifestationPhysicsWidgetState();
}

class _ManifestationPhysicsWidgetState extends State<ManifestationPhysicsWidget> {
  ManifestationPhysicsGame? _game;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (details) {
        // 调用父组件的点击回调
        widget.onTap?.call();
        
        // 同时添加物理图标
        _game?.addIconAtPosition(details.localPosition);
      },
      child: Builder(
        builder: (context) {
          final game = ManifestationPhysicsGame(
            iconImageProvider: widget.iconImageProvider,
            goal: widget.goal,
            version: widget.version,
          );
          _game = game;
          return GameWidget(
            game: game,
            loadingBuilder: (context) => const Center(child: CircularProgressIndicator()),
            errorBuilder: (context, error) => Center(child: Text('Error: $error')),
          );
        },
      ),
    );
  }
} 