import 'package:flutter/material.dart';
import '../models/diary_entry.dart';

class ChatDetailScreen extends StatelessWidget {
  final DiaryEntry chatEntry;

  const ChatDetailScreen({
    super.key,
    required this.chatEntry,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(_getTitle()),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black87,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareChat(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 摘要卡片
            _buildSummaryCard(),
            const SizedBox(height: 20),
            
            // 对话详情
            _buildChatContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: _getGradientColors(),
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildSourceIcon(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getSourceText(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _formatDateTime(chatEntry.createdAt),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (chatEntry.chatSummary != null) ...[
            const SizedBox(height: 16),
            Text(
              chatEntry.chatSummary!,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChatContent() {
    if (chatEntry.content.isEmpty) {
      return const Center(
        child: Text(
          '暂无对话内容',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 16,
          ),
        ),
      );
    }

    // 解析对话内容
    final messages = _parseConversation(chatEntry.content);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '完整对话',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ...messages.map((message) => _buildMessageBubble(message)),
      ],
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.isUser;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: _getSourceColor().withValues(alpha: 0.1),
              child: Icon(
                _getSourceIconData(),
                size: 16,
                color: _getSourceColor(),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isUser ? Colors.blue[500] : Colors.white,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  color: isUser ? Colors.white : Colors.black87,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.blue,
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<ChatMessage> _parseConversation(String content) {
    final lines = content.split('\n');
    final messages = <ChatMessage>[];

    for (final line in lines) {
      if (line.startsWith('用户:')) {
        messages.add(ChatMessage(
          content: line.substring(3).trim(),
          isUser: true,
        ));
      } else if (line.startsWith('高我:')) {
        messages.add(ChatMessage(
          content: line.substring(3).trim(),
          isUser: false,
        ));
      }
    }

    return messages;
  }

  Widget _buildSourceIcon() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        _getSourceIconData(),
        size: 20,
        color: Colors.white,
      ),
    );
  }

  IconData _getSourceIconData() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return Icons.psychology;
      case 'tarot':
        return Icons.auto_awesome;
      default:
        return Icons.chat;
    }
  }

  String _getTitle() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return '灵魂之镜对话';
      case 'tarot':
        return '塔罗解读记录';
      default:
        return '聊天记录';
    }
  }

  String _getSourceText() {
    return _getTitle();
  }

  Color _getSourceColor() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return Colors.purple;
      case 'tarot':
        return Colors.amber;
      default:
        return Colors.blue;
    }
  }

  List<Color> _getGradientColors() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return [Colors.purple[400]!, Colors.purple[600]!];
      case 'tarot':
        return [Colors.amber[400]!, Colors.orange[600]!];
      default:
        return [Colors.blue[400]!, Colors.blue[600]!];
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _shareChat(BuildContext context) {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享功能开发中...')),
    );
  }
}

class ChatMessage {
  final String content;
  final bool isUser;

  ChatMessage({
    required this.content,
    required this.isUser,
  });
}
