import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

class InvitationCodeScreen extends StatefulWidget {
  const InvitationCodeScreen({super.key});

  @override
  State<InvitationCodeScreen> createState() => _InvitationCodeScreenState();
}

class _InvitationCodeScreenState extends State<InvitationCodeScreen> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  final List<String> _debugLogs = [];
  bool _showDebugPanel = false;

  void _addDebugLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _debugLogs.add('$timestamp: $message');
      if (_debugLogs.length > 50) {
        _debugLogs.removeAt(0); // 保持最多50条日志
      }
    });
    print('🐛 DEBUG: $message'); // 同时输出到控制台
  }

  Future<void> _submitCode() async {
    final code = _codeController.text.trim().toUpperCase();
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    _addDebugLog('开始提交邀请码: $code');

    if (code.isEmpty) {
      _addDebugLog('邀请码为空');
      setState(() {
        _errorMessage = languageManager.translate('please_enter_code');
      });
      return;
    }

    if (code.length != 6) {
      _addDebugLog('邀请码长度不正确: ${code.length}');
      setState(() {
        _errorMessage = languageManager.translate('code_should_be_6_chars');
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    _addDebugLog('设置加载状态为true');

    try {
      final invitationService = Provider.of<InvitationService>(context, listen: false);
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      _addDebugLog('获取服务实例成功');

      final result = await invitationService.useInvitationCode(
        code,
        language: languageManager.currentLanguage,
      );
      _addDebugLog('邀请码服务调用完成: success=${result.success}, message=${result.message}');

      if (result.success) {
        _addDebugLog('邀请码使用成功，显示成功对话框');
        _showSuccessDialog(result.message);
      } else {
        _addDebugLog('邀请码使用失败: ${result.message}');
        setState(() {
          _errorMessage = result.message;
        });
      }
    } catch (e) {
      _addDebugLog('邀请码使用异常: $e');
      setState(() {
        _errorMessage = languageManager.translate('network_error_retry');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _addDebugLog('设置加载状态为false');
    }
  }

  void _showSuccessDialog(String message) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    _addDebugLog('显示成功对话框: $message');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 50,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              languageManager.translate('redemption_successful'),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: FigmaTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                color: FigmaTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _addDebugLog('成功对话框按钮被点击');
                  try {
                    Navigator.of(context).pop(); // 只关闭对话框，不退出界面
                    _addDebugLog('成功对话框关闭成功');
                    // 清空输入框，允许继续输入
                    _codeController.clear();
                    setState(() {
                      _errorMessage = null;
                    });
                    _addDebugLog('输入框已清空，可以继续输入');
                  } catch (e) {
                    _addDebugLog('成功对话框关闭失败: $e');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: FigmaTheme.primaryPink,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  languageManager.translate('awesome'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // 毛玻璃背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(-1.0, -1.0),
                end: Alignment(1.0, 1.0),
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                  Color(0xFFE6E6FA),
                  Color(0xFF87CEEB),
                ],
              ),
            ),
          ),
                  const SizedBox(height: 20),

                  // 顶部图标 - 与登录页面相同
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/cloud_background.png', // 使用与登录页面相同的PNG图片
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // 如果图片加载失败，回退到原来的图标
                          return Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withOpacity(0.9),
                            ),
                            child: const Icon(
                              Icons.auto_awesome,
                              size: 60,
                              color: FigmaTheme.primaryPink,
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // 标题和说明
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 20,
                    ),
                    child: Column(
                      children: [
                        Text(
                          languageManager.translate('enter_invitation_code'),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: FigmaTheme.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          languageManager.translate('invitation_code_description'),
                          style: const TextStyle(
                            fontSize: 16,
                            color: FigmaTheme.textSecondary,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // 我的邀请码区域
                  Consumer<InvitationService>(
                    builder: (context, invitationService, child) {
                      if (invitationService.myInvitationCode != null) {
                        return _buildMyInvitationCodeSection(invitationService, languageManager);
                      }
                      return const SizedBox.shrink();
                    },
                  ),

                  const SizedBox(height: 30),

                  // 邀请码输入框
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: FigmaTheme.primaryPink.withOpacity(0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: FigmaTheme.createGlassDecoration(
                        opacity: 0.9,
                        radius: 20,
                      ),
                      child: Column(
                        children: [
                          // 输入框
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: _errorMessage != null
                                  ? Colors.red
                                  : FigmaTheme.primaryPink.withOpacity(0.3),
                                width: 2,
                              ),
                              color: Colors.white.withOpacity(0.9),
                            ),
                            child: TextField(
                              controller: _codeController,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 8,
                                color: FigmaTheme.textPrimary,
                              ),
                              inputFormatters: [
                                UpperCaseTextFormatter(),
                                LengthLimitingTextInputFormatter(6),
                                FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
                              ],
                              decoration: InputDecoration(
                                hintText: 'XXXXXX',
                                hintStyle: TextStyle(
                                  color: FigmaTheme.textSecondary.withOpacity(0.5),
                                  letterSpacing: 8,
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 20,
                                ),
                              ),
                              onChanged: (value) {
                                setState(() {
                                  _errorMessage = null;
                                });
                              },
                              onSubmitted: (_) => _submitCode(),
                            ),
                          ),

                          if (_errorMessage != null) ...[
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.red.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.red.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _errorMessage!,
                                      style: const TextStyle(
                                        color: Colors.red,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],

                          const SizedBox(height: 24),

                          // 提交按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _submitCode,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: FigmaTheme.primaryPink,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 8,
                                shadowColor: FigmaTheme.primaryPink.withOpacity(0.3),
                              ),
                              child: _isLoading
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : Text(
                                    languageManager.translate('use_invitation_code'),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 使用规则
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.7,
                      radius: 16,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.info_outline,
                              color: FigmaTheme.primaryPink,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              languageManager.translate('usage_rules'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: FigmaTheme.textPrimary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          languageManager.translate('invitation_code_rules'),
                          style: const TextStyle(
                            fontSize: 14,
                            color: FigmaTheme.textSecondary,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 顶部导航栏 - 确保在最上层
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                decoration: FigmaTheme.createGlassDecoration(
                  opacity: 0.9,
                  radius: 0,
                ),
                child: Stack(
                  children: [
                    // 返回按钮 - 左侧固定位置
                    Positioned(
                      left: 0,
                      top: 6,
                      child: GestureDetector(
                        onTap: () {
                          _addDebugLog('🔙 返回按钮被点击 (GestureDetector)');
                          try {
                            Navigator.of(context).pop();
                            _addDebugLog('✅ Navigator.pop() 执行成功');
                          } catch (e) {
                            _addDebugLog('❌ Navigator.pop() 执行失败: $e');
                          }
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          alignment: Alignment.center,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: FigmaTheme.textPrimary,
                            size: 24,
                          ),
                        ),
                      ),
                    ),

                    // 标题 - 考虑左侧按钮宽度的真正居中
                    Positioned(
                      left: 48, // 左侧按钮宽度
                      right: 48, // 右侧预留相同宽度以保持平衡
                      top: 6,
                      height: 48,
                      child: Center(
                        child: Text(
                          languageManager.translate('invitation_code'),
                          style: const TextStyle(
                            color: FigmaTheme.textPrimary,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    // 调试按钮 - 右侧固定位置
                    Positioned(
                      right: 0,
                      top: 6,
                      child: GestureDetector(
                        onTap: () {
                          _addDebugLog('🐛 调试面板按钮被点击 (GestureDetector)');
                          setState(() {
                            _showDebugPanel = !_showDebugPanel;
                            _addDebugLog('🔄 调试面板状态切换为: $_showDebugPanel');
                          });
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          alignment: Alignment.center,
                          child: Icon(
                            Icons.bug_report,
                            color: _showDebugPanel ? FigmaTheme.primaryPink : FigmaTheme.textSecondary,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // 调试面板
          if (_showDebugPanel) {
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  border: Border.all(color: Colors.grey[700]!),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.bug_report,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            '调试日志',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16),
                              onTap: () {
                                setState(() {
                                  _debugLogs.clear();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: const Icon(
                                  Icons.clear_all,
                                  color: Colors.white70,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16),
                              onTap: () {
                                setState(() {
                                  _showDebugPanel = false;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white70,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _debugLogs.length,
                        itemBuilder: (context, index) {
                          final log = _debugLogs[index];
                          Color textColor = Colors.white70;
                          if (log.contains('错误') || log.contains('失败')) {
                            textColor = Colors.red;
                          } else if (log.contains('成功')) {
                            textColor = Colors.green;
                          } else if (log.contains('点击')) {
                            textColor = Colors.blue;
                          }

                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 1),
                            child: Text(
                              log,
                              style: TextStyle(
                                color: textColor,
                                fontSize: 11,
                                fontFamily: 'monospace',
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            )
          },
          ),

          // 主要内容
          Positioned(
            top: 120, // 给导航栏留出足够空间
            left: 0,
            right: 0,
            bottom: 0,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const SizedBox(height: 20),

                  // 标题和说明
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 20,
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                FigmaTheme.primaryPink.withOpacity(0.3),
                                FigmaTheme.primaryPink.withOpacity(0.1),
                              ],
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: FigmaTheme.primaryPink.withOpacity(0.2),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              'assets/images/cloud_background.png', // 使用登录页面的PNG图片
                              width: 50,
                              height: 50,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                // 如果图片加载失败，回退到原来的图标
                                return const Icon(
                                  Icons.card_giftcard,
                                  size: 50,
                                  color: FigmaTheme.primaryPink,
                                );
                              },
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        Text(
                          languageManager.translate('enter_invitation_code'),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: FigmaTheme.textPrimary,
                          ),
                        ),

                        const SizedBox(height: 12),

                        Text(
                          languageManager.translate('invitation_code_description'),
                          style: const TextStyle(
                            fontSize: 16,
                            color: FigmaTheme.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // 我的邀请码区域
                  Consumer<InvitationService>(
                    builder: (context, invitationService, child) {
                      if (invitationService.myInvitationCode != null) {
                        return _buildMyInvitationCodeSection(invitationService, languageManager);
                      }
                      return const SizedBox.shrink();
                    },
                  ),

                  const SizedBox(height: 30),

                  // 邀请码输入框
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 16,
                    ),
                    child: Column(
                      children: [
                        Text(
                          languageManager.translate('invitation_code'),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: FigmaTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _errorMessage != null
                                ? Colors.red
                                : FigmaTheme.primaryPink.withOpacity(0.3),
                              width: 2,
                            ),
                            color: Colors.white.withOpacity(0.9),
                          ),
                          child: TextField(
                            controller: _codeController,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 8,
                              color: FigmaTheme.textPrimary,
                            ),
                            decoration: InputDecoration(
                              hintText: languageManager.translate('invitation_code_placeholder'),
                              hintStyle: const TextStyle(
                                fontSize: 16,
                                color: FigmaTheme.textMuted,
                                letterSpacing: 2,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.transparent,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 20,
                              ),
                            ),
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(6),
                              FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                              UpperCaseTextFormatter(),
                            ],
                            onSubmitted: (_) => _submitCode(),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 错误信息
                  if (_errorMessage != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      _errorMessage!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  ],

                  const SizedBox(height: 40),

                  // 提交按钮
                  Container(
                    width: double.infinity,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          FigmaTheme.primaryPink,
                          FigmaTheme.primaryPink.withOpacity(0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: FigmaTheme.primaryPink.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: _isLoading ? null : _submitCode,
                        child: Container(
                          alignment: Alignment.center,
                          child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                languageManager.translate('redeem_invitation_code'),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // 底部提示
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.7,
                      radius: 16,
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: FigmaTheme.primaryPink,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              languageManager.translate('usage_instructions'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: FigmaTheme.textPrimary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          languageManager.translate('invitation_code_rules'),
                          style: const TextStyle(
                            fontSize: 14,
                            color: FigmaTheme.textSecondary,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    )
  }

  /// 构建我的邀请码区域
  Widget _buildMyInvitationCodeSection(InvitationService invitationService, LanguageManager languageManager) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: FigmaTheme.createGlassDecoration(
        opacity: 0.8,
        radius: 20,
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.share,
                color: FigmaTheme.primaryPink,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('my_invitation_code'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: FigmaTheme.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 邀请码显示
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  FigmaTheme.primaryPink.withOpacity(0.1),
                  FigmaTheme.primaryPink.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: FigmaTheme.primaryPink.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitationService.myInvitationCode ?? '',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 4,
                          color: FigmaTheme.primaryPink,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        languageManager.translate('share_with_friends'),
                        style: const TextStyle(
                          fontSize: 12,
                          color: FigmaTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () => _copyInvitationCode(invitationService.myInvitationCode!, languageManager),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: const Icon(
                        Icons.copy,
                        color: FigmaTheme.primaryPink,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 邀请统计
          FutureBuilder<InvitationStats?>(
            future: invitationService.getInvitationStats(),
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data != null) {
                final stats = snapshot.data!;
                return Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        languageManager.translate('invited_friends'),
                        stats.successfulInvitations.toString(),
                        Icons.people,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        languageManager.translate('reward_days'),
                        '${stats.totalRewardDays}${languageManager.currentLanguage.startsWith('zh') ? '天' : ' days'}',
                        Icons.card_giftcard,
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: FigmaTheme.primaryPink,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: FigmaTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: FigmaTheme.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 复制邀请码
  void _copyInvitationCode(String code, LanguageManager languageManager) {
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageManager.translate('invitation_code_copied')),
        backgroundColor: FigmaTheme.primaryPink,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}

/// 自动转换为大写的输入格式化器
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}