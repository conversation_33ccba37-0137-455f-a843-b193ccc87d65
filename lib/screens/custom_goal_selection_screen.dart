import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/models/manifestation_goal.dart';
import 'package:ai_tarot_reading/services/manifestation_goal_service.dart';
import 'package:ai_tarot_reading/screens/custom_affirmation_screen.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:flutter_animate/flutter_animate.dart';

class CustomGoalSelectionScreen extends StatefulWidget {
  const CustomGoalSelectionScreen({super.key});

  @override
  State<CustomGoalSelectionScreen> createState() => _CustomGoalSelectionScreenState();
}

class _CustomGoalSelectionScreenState extends State<CustomGoalSelectionScreen> {
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);
    
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF667EEA),
              Color(0xFF764BA2),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // 顶部导航栏
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        languageManager.translate('select_custom_goal'),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 搜索框 - 圆角透明样式
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    // 更透明的毛玻璃效果
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(28), // 更圆的圆角
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.4),
                      width: 1.5,
                    ),
                    boxShadow: [
                      // 添加柔和的阴影效果
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(28), // 确保内容遵循圆角
                    child: TextField(
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      cursorColor: Colors.white,
                      decoration: InputDecoration(
                        hintText: languageManager.translate('search_goals'),
                        hintStyle: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 16,
                        ),
                        prefixIcon: Container(
                          padding: const EdgeInsets.all(12),
                          child: Icon(
                            Icons.search,
                            color: Colors.white.withValues(alpha: 0.8),
                            size: 22,
                          ),
                        ),
                        // 添加清除按钮
                        suffixIcon: _searchQuery.isNotEmpty
                            ? Container(
                                padding: const EdgeInsets.all(12),
                                child: GestureDetector(
                                  onTap: () => setState(() => _searchQuery = ''),
                                  child: Icon(
                                    Icons.clear,
                                    color: Colors.white.withValues(alpha: 0.7),
                                    size: 20,
                                  ),
                                ),
                              )
                            : null,
                        border: InputBorder.none, // 移除默认边框
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        filled: false, // 不使用填充色，使用外层容器的背景
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 目标列表
              Expanded(
                child: Consumer<ManifestationGoalService>(
                  builder: (context, manifestationService, child) {
                    final goals = manifestationService.searchGoals(_searchQuery)
                        .where((goal) => goal.isAffirmationGenerated && goal.affirmation != null)
                        .toList();

                    if (goals.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.white.withValues(alpha: 0.5),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              languageManager.translate('no_goals_with_affirmations'),
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white.withValues(alpha: 0.7),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: goals.length,
                      itemBuilder: (context, index) {
                        final goal = goals[index];
                        return _buildGoalCard(goal, languageManager);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoalCard(ManifestationGoal goal, LanguageManager languageManager) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _selectGoal(goal),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 状态图标
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(goal.status.colorValue).withValues(alpha: 0.2),
                  ),
                  child: Center(
                    child: Text(
                      goal.status.icon,
                      style: const TextStyle(fontSize: 18),
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // 目标信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        goal.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${languageManager.translate('has_affirmations')} • ${_getAffirmationCount(goal.affirmation!)}${languageManager.translate('count_unit')}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 箭头图标
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.2);
  }

  int _getAffirmationCount(String affirmation) {
    return affirmation.split('|||').length;
  }

  void _selectGoal(ManifestationGoal goal) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomAffirmationScreen(goal: goal),
      ),
    );
  }
}
