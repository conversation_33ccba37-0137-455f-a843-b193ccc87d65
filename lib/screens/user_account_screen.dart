import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';

import 'package:ai_tarot_reading/theme/app_theme.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/utils/language_manager_extension.dart';
import 'package:ai_tarot_reading/utils/apple_signin_manager.dart';
import 'package:ai_tarot_reading/widgets/liquid_glass_dialog.dart';

import 'package:ai_tarot_reading/services/supabase_auth_service.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/services/card_preference_service.dart';
import 'package:ai_tarot_reading/services/background_service.dart';
import 'package:ai_tarot_reading/services/app_background_service.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:ai_tarot_reading/services/notification_service.dart';
import 'package:ai_tarot_reading/screens/invitation_code_screen_fixed.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';

import 'package:ai_tarot_reading/screens/flying_posters_screen.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';

import 'dart:io';

class UserAccountScreen extends StatefulWidget {
  const UserAccountScreen({super.key});

  @override
  State<UserAccountScreen> createState() => _UserAccountScreenState();
}

class _UserAccountScreenState extends State<UserAccountScreen> {
  bool _notificationsEnabled = true; // 通知开关状态

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  /// 加载通知设置
  Future<void> _loadNotificationSettings() async {
    try {
      final settings = await NotificationService().getCurrentSettings();
      setState(() {
        _notificationsEnabled = settings['enabled'] ?? true;
      });
    } catch (e) {
      print('❌ 加载通知设置失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context);
    final languageManager = Provider.of<LanguageManager>(context);
    
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent, // 透明背景，使用HomeScreen的背景
      body: SafeArea(
        child: Column(
          children: [
            // 🍎 现代化顶部导航栏 - 毛玻璃样式
            Container(
              height: 60,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                // 倒数第三层样式：更透明的毛玻璃效果
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(23),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const SizedBox(width: 16),
                  const Icon(
                    Icons.person,
                    color: Colors.black,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child:                   Text(
                    TranslationHelper.safeTranslate(languageManager, 'personal_center'),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Consumer<SubscriptionService>(
                    builder: (context, subscriptionService, child) {
                      if (subscriptionService.isSubscribed) {
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.workspace_premium,
                                color: Colors.amber,
                                size: 16,
                              ),
                              SizedBox(width: 4),
                              Text(
                                'VIP',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                  const SizedBox(width: 16),
                ],
              ),
            ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.5, end: 0),

            // 主要内容
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(20, 0, 20, 10), // 减少底部内边距
                child: Column(
                  children: [
                    // 👤 精美用户头像区域
                    _buildModernProfileHeader(context, languageManager).animate().fadeIn(delay: 200.ms, duration: 800.ms).scale(
                      begin: const Offset(0.8, 0.8),
                      end: const Offset(1.0, 1.0),
                    ),

                    const SizedBox(height: 32),

                    // 💎 会员状态卡片
                    _buildMembershipStatusCard(languageManager).animate().fadeIn(delay: 300.ms, duration: 600.ms).slideY(begin: 0.3, end: 0),
                    
                    const SizedBox(height: 24),

                    // 📊 精美统计卡片
                    _buildModernStatsSection(appState, languageManager).animate().fadeIn(delay: 400.ms, duration: 600.ms).slideY(begin: 0.3, end: 0),
                    
                    const SizedBox(height: 24),
                    
                    // ⚙️ 设置区域
                    _buildModernSettingsSection(context, languageManager).animate().fadeIn(delay: 600.ms, duration: 600.ms).slideY(begin: 0.3, end: 0),
                    
                    const SizedBox(height: 24),
                    
                    // 🔧 系统功能区域
                    _buildModernSystemSection(context, languageManager).animate().fadeIn(delay: 800.ms, duration: 600.ms).slideY(begin: 0.3, end: 0),
                    
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 👤 用户头像区域
  Widget _buildModernProfileHeader(BuildContext context, LanguageManager languageManager) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
        child: Column(
          children: [
          // 用户头像
          Stack(
            children: [
              Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: appState.customAvatarPath == null
                          ? LinearGradient(colors: appState.avatarGradient)
                          : null,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: appState.customAvatarPath != null
                        ? ClipOval(
                            child: Image.file(
                              File(appState.customAvatarPath!),
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                // 如果图片加载失败，显示默认头像
                                return Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(colors: appState.avatarGradient),
                                  ),
                                  child: Icon(
                                    appState.avatarIcon,
                                    size: 50,
                                    color: Colors.white,
                                  ),
                                );
                              },
                            ),
                          )
                        : Icon(
                            appState.avatarIcon,
                            size: 50,
                            color: Colors.white,
                          ),
                  );
                },
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
            ),
          ],
        ),
                  child: Icon(
                    Icons.camera_alt,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 用户名
          Consumer<AppStateProvider>(
            builder: (context, appState, child) {
              return Text(
                appState.username,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              );
            },
          ),
          
          const SizedBox(height: 8),
          
          // 用户等级标签
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
          children: [
                const Icon(
                  Icons.auto_awesome,
                  color: Color(0xFF667eea),
                  size: 16,
                ),
                const SizedBox(width: 6),
            Text(
                                      TranslationHelper.safeTranslate(languageManager, 'mystic_explorer'),
              style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 编辑按钮
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: TextButton.icon(
              onPressed: () => _showEditProfileDialog(context),
              icon: const Icon(Icons.edit, color: Colors.black, size: 18),
              label: Text(
                languageManager.translate('edit_profile'),
                style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 💎 会员状态卡片
  Widget _buildMembershipStatusCard(LanguageManager languageManager) {
    return Consumer<SubscriptionService>(
      builder: (context, subscriptionService, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            // 会员卡片特殊样式
            gradient: subscriptionService.isSubscribed 
                ? const LinearGradient(
                    colors: [Color(0xFF6B46C1), Color(0xFF8B5CF6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    colors: [Colors.grey.shade400, Colors.grey.shade500],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(23),
            boxShadow: [
              BoxShadow(
                color: (subscriptionService.isSubscribed 
                    ? const Color(0xFF6B46C1) 
                    : Colors.grey.shade400).withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部标题行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        subscriptionService.isSubscribed 
                            ? Icons.workspace_premium 
                            : Icons.lock_outline,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        subscriptionService.isSubscribed
                            ? languageManager.translate(subscriptionService.getTierTranslationKey())
                            : languageManager.translate('free_member'),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  if (subscriptionService.isSubscribed)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'VIP',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // 🔧 修复：根据会员状态显示对应内容
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            // 根据会员状态显示对应标题
                            subscriptionService.isSubscribed
                                ? languageManager.translate(subscriptionService.getTierTranslationKey())
                                : languageManager.translate('free_member'),
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            // 根据会员状态显示对应描述
                            subscriptionService.isSubscribed
                                ? (subscriptionService.isPremium
                                    ? languageManager.translate('premium_member_benefits')
                                    : languageManager.translate('basic_member_benefits'))
                                : languageManager.translate('limited_features'),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            softWrap: true,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      // 根据会员状态显示对应图标
                      subscriptionService.isSubscribed
                          ? Icons.workspace_premium
                          : Icons.auto_awesome,
                      color: Colors.white.withValues(alpha: 0.8),
                      size: 32,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              

              
              // 升级按钮或会员特权显示
              if (!subscriptionService.isSubscribed) ...[
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFFFFD700), // 金色
                        Color(0xFFFFA500), // 橙金色
                      ],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withValues(alpha: 0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SubscriptionScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.upgrade, size: 20),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            languageManager.translate('upgrade_membership'),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        subscriptionService.isPremium
                            ? languageManager.translate('premium_member_benefits')
                            : languageManager.translate('basic_member_benefits'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  // 📈 现代化统计区域
  Widget _buildModernStatsSection(AppStateProvider appState, LanguageManager languageManager) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.analytics,
                  color: Colors.black,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('my_data'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 使用 FutureBuilder 加载真实的后端统计数据
          FutureBuilder<Map<String, int>>(
            future: SupabaseDataService().getUserStats(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                );
              }

              final stats = snapshot.data ?? {};

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 总显化练习次数卡片
                  _buildModernStatItem(
                    icon: Icons.self_improvement,
                    title: languageManager.translate('total_manifestation_practices'),
                    value: stats['totalManifestationPractices']?.toString() ?? '0',
                    color: const Color(0xFF9C27B0),
                    isFullWidth: true,
                  ),

                  const SizedBox(height: 16),

                  // 分类别统计标题
                  Text(
                    languageManager.translate('category_statistics'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // 分类统计网格 - 第一行
                  Row(
                    children: [
                      Expanded(
                        child: _buildModernStatItem(
                          icon: Icons.attach_money,
                          title: languageManager.translate('wealth_practices'),
                          value: stats['wealthPractices']?.toString() ?? '0',
                          color: const Color(0xFFFFD700),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildModernStatItem(
                          icon: Icons.work,
                          title: languageManager.translate('career_practices'),
                          value: stats['careerPractices']?.toString() ?? '0',
                          color: const Color(0xFF4CAF50),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildModernStatItem(
                          icon: Icons.face,
                          title: languageManager.translate('beauty_practices'),
                          value: stats['beautyPractices']?.toString() ?? '0',
                          color: const Color(0xFFE91E63),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // 分类统计网格 - 第二行
                  Row(
                    children: [
                      Expanded(
                        child: _buildModernStatItem(
                          icon: Icons.star,
                          title: languageManager.translate('fame_practices'),
                          value: stats['famePractices']?.toString() ?? '0',
                          color: const Color(0xFFFF9800),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildModernStatItem(
                          icon: Icons.favorite,
                          title: languageManager.translate('love_practices'),
                          value: stats['lovePractices']?.toString() ?? '0',
                          color: const Color(0xFFFF5722),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // 空白占位，保持对齐
                      const Expanded(child: SizedBox()),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // 📊 现代化统计项目
  Widget _buildModernStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    bool isFullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12), // Reduced padding to give more space for text
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isFullWidth
        ? Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )
        : Column(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(height: 6), // Reduced spacing to optimize layout
              Text(
                value,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12, // Restored original font size since text is shorter
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
                maxLines: 1, // Ensure single line
                overflow: TextOverflow.ellipsis, // Handle overflow gracefully
              ),
            ],
          ),
    );
  }

  // 📝 显示编辑资料对话框
  void _showEditProfileDialog(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final TextEditingController nameController = TextEditingController(text: appState.username);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
            color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
            children: [
              Container(
                      padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                        color: const Color(0xFF667eea).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                ),
                      child: const Icon(
                        Icons.edit,
                        color: Color(0xFF667eea),
                        size: 20,
              ),
                    ),
                    const SizedBox(width: 12),
              Text(
                      TranslationHelper.safeTranslate(languageManager, 'edit_profile'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                ),
                    ),
                  ],
              ),
              const SizedBox(height: 20),
                
                // 头像选择区域
                Center(
                  child: GestureDetector(
                    onTap: () => _showAvatarSelectionDialog(context),
                    child: Stack(
                      children: [
                        Consumer<AppStateProvider>(
                          builder: (context, appState, child) {
                            return Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: appState.customAvatarPath == null
                                    ? LinearGradient(colors: appState.avatarGradient)
                                    : null,
                              ),
                              child: appState.customAvatarPath != null
                                  ? ClipOval(
                                      child: Image.file(
                                        File(appState.customAvatarPath!),
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) {
                                          // 如果图片加载失败，显示默认头像
                                          return Container(
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              gradient: LinearGradient(colors: appState.avatarGradient),
                                            ),
                                            child: Icon(
                                              appState.avatarIcon,
                                              size: 40,
                                              color: Colors.white,
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : Icon(
                                      appState.avatarIcon,
                                      size: 40,
                                      color: Colors.white,
                                    ),
                            );
                          },
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              color: const Color(0xFF667eea),
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              size: 14,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: languageManager.translate('username'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF667eea)),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          languageManager.translate('cancel'),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF6B7280),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          // 保存用户名
                          final newUsername = nameController.text.trim();
                          if (newUsername.isNotEmpty) {
                            appState.setUsername(newUsername);
                          }

                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  const Icon(Icons.check_circle, color: Colors.white),
                                  const SizedBox(width: 8),
                                  Text(languageManager.translate('username_updated')),
                                ],
                              ),
                              backgroundColor: const Color(0xFF4CAF50),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF667eea),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          languageManager.translate('save'),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 🍎 苹果液体玻璃风格iOS登录弹窗
  void _showIOSLogin(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (BuildContext context) {
        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            // 液体玻璃模态框效果
            color: Colors.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 30,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 模态框顶部指示器
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 36,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Apple图标和标题
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.apple,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        TranslationHelper.safeTranslate(languageManager, 'apple_id_login'),
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.black.withValues(alpha: 0.9),
                          letterSpacing: -0.4,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // 描述文本
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text(
                                              TranslationHelper.safeTranslate(languageManager, 'apple_id_login_description'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.black.withValues(alpha: 0.7),
                        height: 1.4,
                        letterSpacing: -0.2,
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // 按钮区域
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Row(
                      children: [
                        // 取消按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              Navigator.pop(context);
                            },
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.05),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  width: 0.5,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  languageManager.translate('cancel'),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black.withValues(alpha: 0.8),
                                    letterSpacing: -0.2,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(width: 12),

                        // 登录按钮
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              Navigator.pop(context);
                              _performIOSLogin(context);
                            },
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: BorderRadius.circular(25),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.apple,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    TranslationHelper.safeTranslate(languageManager, 'login'),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                      letterSpacing: -0.2,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // 🍎 执行iOS登录
  Future<void> _performIOSLogin(BuildContext context) async {
    try {
      // 检查Apple登录是否可用
      if (!await AppleSignInManager.isAppleSignInAvailable()) {
        AppleSignInManager.showSignInErrorDialog(
          context,
          '此设备不支持Apple ID登录功能',
        );
        return;
      }

      // 使用Supabase认证服务进行Apple登录
      final authService = Provider.of<SupabaseAuthService>(context, listen: false);
      final response = await authService.signInWithApple();

      if (response.user != null) {
        // 登录成功，显示成功消息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('Apple登录成功！欢迎 ${response.user!.userMetadata?['full_name'] ?? response.user!.email}'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        AppleSignInManager.showSignInErrorDialog(
          context,
          '登录失败，请稍后重试',
        );
      }
    } catch (e) {
      AppleSignInManager.showSignInErrorDialog(
        context,
        '登录过程中发生错误: $e',
      );
    }
  }

  // 🔔 切换通知设置
  void _toggleNotifications(BuildContext context, bool value) async {
    try {
      // 🔧 将languageManager移到方法开始处，确保在整个方法中都可用
      final languageManager = Provider.of<LanguageManager>(context, listen: false);

      if (value) {
        // 开启通知
        final granted = await NotificationService().requestPermissions();

        if (granted) {
          // 设置默认的每日推送（上午9点）
          final appState = Provider.of<AppStateProvider>(context, listen: false);

          await NotificationService().scheduleDailyAffirmationNotification(
            hour: 9,
            minute: 0,
            manifestationGoal: appState.currentManifestationGoal ?? ManifestationGoal.wealth,
            languageCode: languageManager.currentLanguage,
          );

          setState(() {
            _notificationsEnabled = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(languageManager.translate('notification_enabled_message')),
                ],
              ),
              backgroundColor: const Color(0xFF4CAF50),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(languageManager.translate('notification_permission_denied')),
                ],
              ),
              backgroundColor: const Color(0xFFE53E3E),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          );
        }
      } else {
        // 关闭通知
        await NotificationService().disableDailyNotification();

        setState(() {
          _notificationsEnabled = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.notifications_off, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('notification_disabled_message')),
              ],
            ),
            backgroundColor: const Color(0xFF6B7280),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('设置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 🗑️ 显示清空历史记录对话框 - 液体玻璃风格
  void _showClearHistoryDialog(BuildContext context, LanguageManager languageManager) {
    LiquidGlassDialogHelper.show(
      context: context,
      title: languageManager.translate('clear_history'),
      icon: const Icon(
        Icons.warning,
        color: Color(0xFFFF9800),
        size: 24,
      ),
      content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
              color: const Color(0xFFFF9800).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFFF9800).withOpacity(0.3),
                width: 1,
              ),
                  ),
            child: Row(
              children: [
                const Icon(
                    Icons.warning,
                  color: Color(0xFFFF9800),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                  languageManager.translate('clear_history_warning'),
                  style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
              onPressed: () => Navigator.pop(context),
          child: Text(languageManager.translate('cancel')),
                          ),
        TextButton(
                        onPressed: () async {
                Navigator.pop(context);
                          final appState = Provider.of<AppStateProvider>(context, listen: false);
                          appState.clearAllReadings();
                          
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Row(
                                children: [
                                  Icon(Icons.check_circle, color: Colors.white),
                                  SizedBox(width: 8),
                                  Text('历史记录已清空'),
                                ],
                              ),
                              backgroundColor: const Color(0xFF4CAF50),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            ),
                          );
              },
          child: Text(languageManager.translate('confirm_clear')),
                    ),
                  ],
    );
  }

  // ❌ 显示删除账号对话框 - 液体玻璃风格
  void _showDeleteAccountDialog(BuildContext context, LanguageManager languageManager) {
    LiquidGlassDialogHelper.show(
      context: context,
      title: languageManager.translate('delete_account'),
      icon: const Icon(
        Icons.error,
        color: Color(0xFFE74C3C),
        size: 24,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFE74C3C).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFE74C3C).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.warning,
                  color: Color(0xFFE74C3C),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    languageManager.translate('delete_account_warning'),
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(languageManager.translate('cancel')),
            ),
        TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showFinalDeleteConfirmation(context, languageManager);
              },
              child: Text(languageManager.translate('continue_delete')),
            ),
          ],
    );
  }

  // ❌ 最终删除确认 - 液体玻璃风格
  void _showFinalDeleteConfirmation(BuildContext context, LanguageManager languageManager) {
    final TextEditingController confirmController = TextEditingController();

    LiquidGlassDialogHelper.show(
      context: context,
      title: '最终确认',
      icon: const Icon(
        Icons.security,
        color: Color(0xFFE74C3C),
        size: 24,
      ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFE74C3C).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFE74C3C).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Color(0xFFE74C3C),
                  size: 20,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '请输入 "删除我的账号" 来确认删除：',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
              const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: TextField(
                controller: confirmController,
                decoration: const InputDecoration(
                  labelText: '确认文本',
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
        TextButton(
              onPressed: () {
                if (confirmController.text == '删除我的账号') {
                  Navigator.pop(context);
                  _deleteAccount(context);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('确认文本不正确'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('确认删除'),
            ),
          ],
    );
  }

  // ❌ 执行删除账号
  Future<void> _deleteAccount(BuildContext context) async {
    try {
      final authService = Provider.of<SupabaseAuthService>(context, listen: false);
      await authService.deleteAccount();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('账号删除成功'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('账号删除失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 🔐 显示登录界面 - 跳转到羊皮纸动画
  void _showLoginScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FlyingPostersScreen(),
      ),
    );
  }

  // 🎨 显示头像选择对话框
  void _showAvatarSelectionDialog(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context, listen: false);
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    final avatarOptions = [
      {'icon': Icons.person, 'colors': [const Color(0xFFFFD700), const Color(0xFFFF8C00)]},
      {'icon': Icons.face, 'colors': [const Color(0xFF667eea), const Color(0xFF764ba2)]},
      {'icon': Icons.pets, 'colors': [const Color(0xFFFF6B6B), const Color(0xFFEE5A24)]},
      {'icon': Icons.star, 'colors': [const Color(0xFF4ECDC4), const Color(0xFF44A08D)]},
      {'icon': Icons.favorite, 'colors': [const Color(0xFFFF9A9E), const Color(0xFFFECAB1)]},
      {'icon': Icons.auto_awesome, 'colors': [const Color(0xFFA8EDEA), const Color(0xFFFED6E3)]},
      {'icon': Icons.psychology, 'colors': [const Color(0xFF667eea), const Color(0xFF764ba2)]},
      {'icon': Icons.self_improvement, 'colors': [const Color(0xFF9C27B0), const Color(0xFFE91E63)]},
    ];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF667eea).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.face,
                        color: Color(0xFF667eea),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      languageManager.translate('change_avatar'),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // 头像选项网格
                GridView.builder(
                  shrinkWrap: true,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: avatarOptions.length,
                  itemBuilder: (context, index) {
                    final option = avatarOptions[index];
                    final isSelected = appState.avatarIcon == option['icon'] as IconData;

                    return GestureDetector(
                      onTap: () {
                        appState.setAvatar(
                          option['icon'] as IconData,
                          option['colors'] as List<Color>,
                        );
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                const Icon(Icons.check_circle, color: Colors.white),
                                const SizedBox(width: 8),
                                Text(languageManager.translate('avatar_updated')),
                              ],
                            ),
                            backgroundColor: const Color(0xFF4CAF50),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                        );
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: option['colors'] as List<Color>,
                          ),
                          border: isSelected ? Border.all(color: const Color(0xFF667eea), width: 3) : null,
                        ),
                        child: Icon(
                          option['icon'] as IconData,
                          size: 30,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 20),

                // 从相册选择按钮
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: ElevatedButton.icon(
                    onPressed: () => _pickAvatarFromGallery(context),
                    icon: const Icon(Icons.photo_library, size: 20),
                    label: Text(languageManager.translate('choose_from_gallery')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF667eea),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),

                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    languageManager.translate('cancel'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 📷 从相册选择头像
  Future<void> _pickAvatarFromGallery(BuildContext context) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
      maxWidth: 300,
      maxHeight: 300,
    );

    if (image != null) {
      final appState = Provider.of<AppStateProvider>(context, listen: false);
      final languageManager = Provider.of<LanguageManager>(context, listen: false);

      // 保存头像图片路径
      await appState.setCustomAvatarPath(image.path);

      Navigator.pop(context); // 关闭头像选择对话框

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(languageManager.translate('avatar_updated')),
            ],
          ),
          backgroundColor: const Color(0xFF4CAF50),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  // 🎁 显示邀请码弹窗
  void _navigateToInvitationCode(BuildContext context, InvitationService invitationService) {
    // 总是显示邀请码输入界面，允许用户输入多个邀请码
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InvitationCodeScreenFixed(),
      ),
    );
  }

  // 🎁 显示邀请码输入弹窗
  void _showInvitationCodeInputDialog(BuildContext context, InvitationService invitationService) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final TextEditingController codeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    // 标题
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF6B46C1).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.people_alt,
                            color: Color(0xFF6B46C1),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            languageManager.translate('invite_friends'),
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2D3748),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 描述
                    Text(
                      languageManager.translate('invite_friends_get_7_days'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 20),

                    // 我的邀请码部分
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6B46C1).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF6B46C1).withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            languageManager.translate('my_invitation_code'),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF6B46C1),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: const Color(0xFF6B46C1).withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              invitationService.myInvitationCode ?? 'LOADING',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 4,
                                color: Color(0xFF6B46C1),
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: () {
                                    _copyInvitationCode(context, invitationService.myInvitationCode ?? '', languageManager);
                                  },
                                  icon: const Icon(Icons.copy, size: 16),
                                  label: Text(languageManager.translate('copy_invitation_code')),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF6B46C1),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: () {
                                    _shareInvitationCode(context, invitationService.myInvitationCode ?? '', languageManager);
                                  },
                                  icon: const Icon(Icons.share, size: 16),
                                  label: Text(languageManager.translate('share_with_friends')),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // 分隔线
                    Row(
                      children: [
                        Expanded(child: Divider(color: Colors.grey[300])),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            'OR',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(child: Divider(color: Colors.grey[300])),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 输入好友邀请码标题
                    Text(
                      languageManager.translate('enter_friend_invitation_code'),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 输入框
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF6B46C1).withOpacity(0.3),
                          width: 2,
                        ),
                        color: Colors.grey[50],
                      ),
                      child: TextField(
                        controller: codeController,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 4,
                          color: Color(0xFF2D3748),
                        ),
                        decoration: InputDecoration(
                          hintText: languageManager.translate('invitation_code_placeholder'),
                          hintStyle: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[400],
                            letterSpacing: 2,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          fillColor: Colors.transparent,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(6),
                          FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                          UpperCaseTextFormatter(),
                        ],
                        onChanged: (value) {
                          // 输入变化时的处理
                        },
                        onSubmitted: (_) => _submitInvitationCode(
                          dialogContext,
                          codeController,
                          invitationService,
                          languageManager,
                          setState,
                        ),
                      ),
                    ),


                    const SizedBox(height: 24),

                    // 按钮
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(dialogContext),
                            child: Text(
                              languageManager.translate('cancel'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF6B7280),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => _submitInvitationCode(
                              dialogContext,
                              codeController,
                              invitationService,
                              languageManager,
                              setState,
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF6B46C1),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              languageManager.translate('redeem_invitation_code'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 使用说明
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.blue.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.lightbulb_outline,
                                color: Colors.blue[600],
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                languageManager.translate('usage_instructions'),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue[600],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            languageManager.translate('invite_friends_rules'),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[600],
                              height: 1.3,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 🎁 提交邀请码
  Future<void> _submitInvitationCode(
    BuildContext dialogContext,
    TextEditingController codeController,
    InvitationService invitationService,
    LanguageManager languageManager,
    StateSetter setState,
  ) async {
    final code = codeController.text.trim();

    if (code.isEmpty) {
      return;
    }

    if (code.length != 6) {
      return;
    }

    try {
      final result = await invitationService.useInvitationCode(code);

      if (result.success) {
        Navigator.pop(dialogContext);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('invitation_code_success')),
              ],
            ),
            backgroundColor: const Color(0xFF4CAF50),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('invitation_code_invalid')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text(languageManager.translate('invitation_code_error')),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  // 🎁 复制邀请码
  void _copyInvitationCode(BuildContext context, String code, LanguageManager languageManager) {
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(languageManager.translate('invitation_code_copied')),
          ],
        ),
        backgroundColor: const Color(0xFF4CAF50),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  // 🎁 分享邀请码
  void _shareInvitationCode(BuildContext context, String code, LanguageManager languageManager) {
    // 这里可以集成分享功能，暂时显示一个提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.share, color: Colors.white),
            const SizedBox(width: 8),
            Text('${languageManager.translate('share_with_friends')}: $code'),
          ],
        ),
        backgroundColor: const Color(0xFF2196F3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // 🎁 显示我的邀请码
  void _showMyInvitationCode(BuildContext context, InvitationService invitationService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Row(
          children: [
            Icon(
              Icons.share,
              color: FigmaTheme.primaryPink,
              size: 28,
            ),
            SizedBox(width: 8),
            Text(
              '我的邀请码',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: FigmaTheme.primaryPink.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: FigmaTheme.primaryPink.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    invitationService.myInvitationCode ?? 'LOADING',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 4,
                      color: FigmaTheme.primaryPink,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '分享给朋友，双方都能获得奖励',
                    style: TextStyle(
                      fontSize: 14,
                      color: FigmaTheme.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现分享功能
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('分享功能即将上线')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: FigmaTheme.primaryPink,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('分享'),
          ),
        ],
      ),
    );
  }

  // 👤 苹果液体玻璃风格账号选项
  void _showAccountOptions(BuildContext context, SupabaseAuthService authService) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (BuildContext context) {
        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            // 液体玻璃模态框效果
            color: Colors.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 30,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 模态框顶部指示器
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 36,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 标题
                  Text(
                    TranslationHelper.safeTranslate(languageManager, 'account_management'),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black.withValues(alpha: 0.9),
                      letterSpacing: -0.3,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // 用户信息卡片
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 0.5,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: const Color(0xFF007AFF).withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.person_rounded,
                            color: Color(0xFF007AFF),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                authService.getDisplayName(),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black.withValues(alpha: 0.9),
                                  letterSpacing: -0.2,
                                ),
                              ),
                              if (authService.userEmail != null) ...[
                                const SizedBox(height: 2),
                                Text(
                                  authService.userEmail!,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.black.withValues(alpha: 0.6),
                                    letterSpacing: -0.1,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // 登出按钮
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: () async {
                          HapticFeedback.lightImpact();
                          Navigator.pop(context);
                          await authService.signOut();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  const Icon(Icons.check_circle, color: Colors.white),
                                  const SizedBox(width: 8),
                                  Text(languageManager.translate('logged_out')),
                                ],
                              ),
                              backgroundColor: const Color(0xFF007AFF),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: Colors.red.withValues(alpha: 0.15),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: const Icon(
                                  Icons.logout_rounded,
                                  color: Colors.red,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                TranslationHelper.safeTranslate(languageManager, 'logout'),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red.withValues(alpha: 0.9),
                                  letterSpacing: -0.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // ⚙️ 现代化设置区域
  Widget _buildModernSettingsSection(BuildContext context, LanguageManager languageManager) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.settings,
                  color: Colors.black,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('preference_settings'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 通知设置
          _buildModernSettingItem(
            icon: Icons.notifications_outlined,
            title: languageManager.translate('notification_settings'),
            subtitle: _notificationsEnabled
              ? languageManager.translate('push_notifications_enabled')
              : languageManager.translate('push_notifications_disabled'),
            trailing: Switch(
              value: _notificationsEnabled,
              onChanged: (value) => _toggleNotifications(context, value),
              activeColor: const Color(0xFFFFD700),
              inactiveThumbColor: Colors.white.withOpacity(0.5),
              inactiveTrackColor: Colors.white.withOpacity(0.2),
            ),
          ),

          const SizedBox(height: 12),
          
          const SizedBox(height: 12),
          
          // 语言设置
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              final currentLangInfo = languageManager.getCurrentLanguageInfo();
              return _buildModernSettingItem(
                icon: Icons.language,
                title: languageManager.translate('language_setting'),
                subtitle: '${currentLangInfo['icon']} ${currentLangInfo['name']}',
                trailing: const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 16,
                ),
                onTap: () => _showLanguageSelector(context),
              );
            },
          ),
          
          const SizedBox(height: 12),
          
          // 会员设置
          Consumer<SubscriptionService>(
            builder: (context, subscriptionService, child) {
              return _buildModernSettingItem(
                icon: subscriptionService.isSubscribed
                    ? Icons.workspace_premium
                    : Icons.diamond_outlined,
                title: languageManager.translate('premium_member'),
                subtitle: subscriptionService.isSubscribed
                    ? languageManager.translate('vip_member')
                    : languageManager.translate('upgrade_enjoy_more'),
                trailing: subscriptionService.isSubscribed
                    ? Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.amber.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          languageManager.translate('vip'),
                          style: const TextStyle(
                            color: Colors.amber,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('upgrade'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      ),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionScreen(),
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 12),
          
          // 背景更换功能（会员功能）
          Consumer<SubscriptionService>(
            builder: (context, subscriptionService, child) {
              return _buildModernSettingItem(
                icon: Icons.image_outlined,
                title: languageManager.translate('change_background'),
                subtitle: subscriptionService.isSubscribed
                    ? languageManager.translate('customize_app_background')
                    : languageManager.translate('member_exclusive_feature'),
                trailing: subscriptionService.isSubscribed
                    ? const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.black,
                        size: 16,
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          languageManager.translate('vip'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                onTap: () => subscriptionService.isSubscribed
                    ? _showAppBackgroundSelector(context)
                    : _showBackgroundUpgradeDialog(context),
              );
            },
          ),
          
          const SizedBox(height: 12),
          
          // 卡牌选择设置
          Consumer<CardPreferenceService>(
            builder: (context, cardService, child) {
              return _buildModernSettingItem(
                icon: Icons.style_outlined,
                title: languageManager.translate('card_style'),
                subtitle: languageManager.translate('customize_tarot_appearance'),
                trailing: const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 16,
                ),
                onTap: () => _showCardStyleSelector(context),
              );
            },
          ),
          
          const SizedBox(height: 12),
          
          // 高斯模糊度调节功能 - 已隐藏
          // _buildModernSettingItem(
          //   icon: Icons.blur_on_outlined,
          //   title: languageManager.translate('glass_transparency'),
          //   subtitle: languageManager.translate('adjust_blur_effect'),
          //   trailing: const Icon(
          //     Icons.arrow_forward_ios,
          //     color: Colors.black,
          //     size: 16,
          //   ),
          //   onTap: () => _showBlurSettingsDialog(context),
          // ),

          // const SizedBox(height: 12),



          // 邀请好友功能
          Consumer<InvitationService>(
            builder: (context, invitationService, child) {
              return _buildModernSettingItem(
                icon: Icons.people_alt,
                title: languageManager.translate('invite_friends'),
                subtitle: languageManager.translate('invite_friends_get_7_days'),
                trailing: const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 16,
                ),
                onTap: () => _navigateToInvitationCode(context, invitationService),
              );
            },
          ),

          const SizedBox(height: 12),

          // 账号登录
          Consumer<SupabaseAuthService>(
            builder: (context, authService, child) {
              return _buildModernSettingItem(
                icon: authService.isSignedIn ? Icons.account_circle : Icons.login,
                title: languageManager.translate('account_login'),
                subtitle: authService.isSignedIn
                    ? languageManager.translate('logged_in')
                    : languageManager.translate('not_logged_in'),
                trailing: const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 16,
                ),
                onTap: () => authService.isSignedIn
                    ? _showAccountOptions(context, authService)
                    : _showLoginScreen(context),
              );
            },
          ),
        ],
      ),
    );
  }

  // 🔧 系统功能区域
  Widget _buildModernSystemSection(BuildContext context, LanguageManager languageManager) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Color(0xFF667eea),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('about_app'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF333333),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 应用信息
          _buildModernSettingItem(
            icon: Icons.info,
            title: languageManager.translate('app_version'),
            subtitle: '1.0.0',
          ),

          const SizedBox(height: 12),

          _buildModernSettingItem(
            icon: Icons.privacy_tip_outlined,
            title: languageManager.translate('privacy_policy'),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Colors.white,
              size: 16,
            ),
            onTap: () => _launchURL('https://www.apple.com/legal/privacy/'),
          ),

          const SizedBox(height: 12),

          _buildModernSettingItem(
            icon: Icons.description_outlined,
            title: languageManager.translate('terms_of_service'),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Colors.white,
              size: 16,
            ),
            onTap: () => _launchURL('https://www.apple.com/legal/internet-services/itunes/dev/stdeula/'),
          ),

          const SizedBox(height: 12),

          _buildModernSettingItem(
            icon: Icons.help_outline,
            title: languageManager.translate('help_support'),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF333333),
              size: 16,
            ),
            onTap: () => _launchURL('https://lambent-tulumba-92e5c9.netlify.app/support'),
          ),

          const SizedBox(height: 12),



          
          const SizedBox(height: 16),
          
          // 危险操作区域
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.red.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                _buildModernSettingItem(
                  icon: Icons.clear_all,
                  title: languageManager.translate('clear_history'),
                  subtitle: languageManager.translate('will_delete_all_reading_history'),
                  trailing: const Icon(
                    Icons.warning,
                    color: Colors.red,
                    size: 20,
                  ),
                  onTap: () => _showClearHistoryDialog(context, languageManager),
                ),

                const SizedBox(height: 8),

                _buildModernSettingItem(
                  icon: Icons.delete_forever,
                  title: languageManager.translate('delete_account'),
                  subtitle: languageManager.translate('delete_all_data'),
                  trailing: const Icon(
                    Icons.warning,
                    color: Colors.red,
                    size: 20,
                  ),
                  onTap: () => _showDeleteAccountDialog(context, languageManager),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 🎨 现代化设置项目
  Widget _buildModernSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.black,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.black.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[
                  const SizedBox(width: 8),
                  trailing,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 📊 计算平均评分
  String _getAverageRating(List readings) {
    if (readings.isEmpty) return '0.0';

    double totalRating = 0;
    int ratedCount = 0;

    for (var reading in readings) {
      if (reading.accuracy != null && reading.usefulness != null && reading.satisfaction != null) {
        totalRating += (reading.accuracy! + reading.usefulness! + reading.satisfaction!) / 3;
        ratedCount++;
      }
    }

    if (ratedCount == 0) return '0.0';
    return (totalRating / ratedCount).toStringAsFixed(1);
  }

  // 🌍 苹果液体玻璃风格语言选择器
  void _showLanguageSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (BuildContext context) {
        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            // 液体玻璃模态框效果
            color: Colors.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 30,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 模态框顶部指示器
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        width: 36,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // 标题
                      Text(
                        TranslationHelper.safeTranslate(languageManager, 'language_selection_title'),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black.withValues(alpha: 0.9),
                          letterSpacing: -0.3,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // 可滚动的语言选项列表
                      Flexible(
                        child: SingleChildScrollView(
                          child: Column(
                            children: LanguageManager.supportedLanguages.entries.map((entry) {
                              final langCode = entry.key;
                              final langInfo = entry.value;
                              final isSelected = languageManager.currentLanguage == langCode;

                              return _buildAppleStyleLanguageOption(
                                context,
                                langInfo['code']!,
                                langInfo['name']!,
                                langCode,
                                isSelected,
                                languageManager,
                              );
                            }).toList(),
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  // 🌍 苹果风格语言选项
  Widget _buildAppleStyleLanguageOption(
    BuildContext context,
    String code,
    String name,
    String languageCode,
    bool isSelected,
    LanguageManager languageManager,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () async {
            if (languageCode != languageManager.currentLanguage) {
              HapticFeedback.lightImpact();
              final appState = Provider.of<AppStateProvider>(context, listen: false);
              await languageManager.switchLanguage(languageCode, appStateProvider: appState);

              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      Text('${languageManager.translate('language_changed')}: $name'),
                    ],
                  ),
                  backgroundColor: const Color(0xFF007AFF),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  duration: const Duration(seconds: 2),
                ),
              );
            } else {
              Navigator.pop(context);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF007AFF).withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              border: isSelected ? Border.all(
                color: const Color(0xFF007AFF).withValues(alpha: 0.3),
                width: 1,
              ) : null,
            ),
            child: Row(
              children: [
                // 语言代码圆点
                Container(
                  width: 40,
                  height: 40,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF007AFF).withValues(alpha: 0.15)
                        : Colors.black.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF007AFF).withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.1),
                      width: 0.5,
                    ),
                  ),
                  child: Text(
                    code,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? const Color(0xFF007AFF)
                          : Colors.black.withValues(alpha: 0.7),
                      letterSpacing: -0.2,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // 语言名称
                Expanded(
                  child: Text(
                    name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: Colors.black.withValues(alpha: 0.9),
                      letterSpacing: -0.2,
                    ),
                  ),
                ),
                // 选中指示器
                if (isSelected)
                  Icon(
                    Icons.check_circle_rounded,
                    color: const Color(0xFF007AFF),
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 🌍 构建语言选项 (保留原版本作为备用)
  Widget _buildLanguageOption(
    BuildContext context,
    String code,
    String name,
    String languageCode,
    bool isSelected,
    LanguageManager languageManager,
  ) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isSelected ? Colors.purple.shade100 : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          code,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.purple.shade600 : Colors.grey.shade600,
          ),
        ),
      ),
      title: Text(
        name,
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Colors.purple.shade600 : Colors.black87,
        ),
      ),
      trailing: isSelected
          ? Icon(
              Icons.check_circle,
              color: Colors.purple.shade600,
            )
          : null,
      onTap: () async {
        final appState = Provider.of<AppStateProvider>(context, listen: false);
        await languageManager.switchLanguage(languageCode, appStateProvider: appState);

        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${languageManager.translate('language_changed')}: $name'),
            backgroundColor: Colors.purple.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      },
    );
  }

  // 🖼️ 显示APP背景选择器（会员功能） - 控制整个APP的背景
  void _showAppBackgroundSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Consumer<AppBackgroundService>(
          builder: (context, appBackgroundService, child) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // 顶部拖拽指示器
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    '选择背景图片',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // 背景选项
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: GridView.count(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        children: [
                          // 默认背景
                          _buildBackgroundOption(
                            context,
                            'assets/images/tarot_mascot.png',
                            '默认APP背景',
                            appBackgroundService.currentType == AppBackgroundType.default_mascot,
                            () => _setDefaultAppBackground(context),
                          ),
                          
                          // 自定义背景
                          _buildBackgroundOption(
                            context,
                            appBackgroundService.customBackgroundPath,
                            '自定义APP图片',
                            appBackgroundService.currentType == AppBackgroundType.custom,
                            () => _pickCustomAppBackground(context),
                            isCustom: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // 🖼️ 构建背景选项
  Widget _buildBackgroundOption(
    BuildContext context,
    String? imagePath,
    String title,
    bool isSelected,
    VoidCallback onTap, {
    bool isCustom = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.purple : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.purple.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: Stack(
            children: [
              // 背景图片或占位符
              Positioned.fill(
                child: isCustom
                    ? Container(
                        color: Colors.grey.shade100,
                        child: const Icon(
                          Icons.add_photo_alternate,
                          size: 40,
                          color: Colors.grey,
                        ),
                      )
                    : imagePath != null
                        ? Image.asset(
                            imagePath,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey.shade100,
                                child: const Icon(
                                  Icons.image,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                              );
                            },
                          )
                        : Container(
                            color: Colors.grey.shade100,
                          ),
              ),
              
              // 底部标题
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                  ),
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              
              // 选中指示器
              if (isSelected)
                const Positioned(
                  top: 8,
                  right: 8,
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.purple,
                    size: 24,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 📱 选择自定义背景
  Future<void> _pickCustomBackground(BuildContext context) async {
    final backgroundService = Provider.of<BackgroundService>(context, listen: false);
    
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80, // 压缩图片质量
    );
    
    if (image != null) {
      try {
        await backgroundService.setCustomBackground(image.path);
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ 自定义背景已设置'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ 背景设置失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 🏠 设置默认背景（显化正念用）
  Future<void> _setDefaultBackground(BuildContext context) async {
    final backgroundService = Provider.of<BackgroundService>(context, listen: false);
    
    try {
      await backgroundService.setDefaultBackground();
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ 已恢复默认背景'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ 背景设置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 🏠 设置默认APP背景 (tarot_mascot.png)
  Future<void> _setDefaultAppBackground(BuildContext context) async {
    final appBackgroundService = Provider.of<AppBackgroundService>(context, listen: false);
    
    try {
      await appBackgroundService.setDefaultBackground();
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ 已恢复默认APP背景'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ APP背景设置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 📱 选择自定义APP背景
  Future<void> _pickCustomAppBackground(BuildContext context) async {
    final appBackgroundService = Provider.of<AppBackgroundService>(context, listen: false);
    
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80, // 压缩图片质量
    );
    
    if (image != null) {
      try {
        await appBackgroundService.setCustomBackground(image.path);
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ 自定义APP背景已设置'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ APP背景设置失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  // 🎯 显示背景升级对话框
  void _showBackgroundUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Consumer<LanguageManager>(
        builder: (context, languageManager, child) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                const Icon(
                  Icons.image,
                  color: Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(languageManager.translate('member_features')),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${languageManager.translate('change_manifestation_background')}${languageManager.translate('member_exclusive_feature')}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  languageManager.translate('upgrade_to_paid_member'),
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 8),
                Text(
                  '• ${languageManager.translate('custom_manifestation_background')}\n• ${languageManager.translate('personalized_manifestation_experience')}\n• ${languageManager.translate('more_exclusive_features')}\n• ${languageManager.translate('daily_multiple_ai_readings')}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  languageManager.translate('later'),
                  style: const TextStyle(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SubscriptionScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6B46C1),
                  foregroundColor: Colors.white,
                ),
                child: Text(languageManager.translate('upgrade_now')),
              ),
            ],
          );
        },
      ),
    );
  }

  // 🌫️ 显示毛玻璃模糊度调节对话框
  void _showBlurSettingsDialog(BuildContext context) {
    final blurSettings = Provider.of<BlurSettingsService>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) {
        return Consumer<BlurSettingsService>(
          builder: (context, blurService, child) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              title: const Row(
                children: [
                  Icon(
                    Icons.blur_on,
                    color: Colors.blue,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text('毛玻璃透明度'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    '调节界面毛玻璃效果的模糊度',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 20),
                  
                  // 预览容器 - 使用实际的模糊效果
                  Container(
                    height: 100,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFE8F4FD),
                          Color(0xFFF8E8FF),
                          Color(0xFFFFE8F8),
                        ],
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Stack(
                        children: [
                          // 实际模糊效果预览
                          Positioned.fill(
                            child: BackdropFilter(
                              filter: blurService.getImageFilter(), // 使用实际模糊度
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.15),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.4),
                                    width: 1.5,
                                  ),
                                ),
                                child: const Center(
                                  child: Text(
                                    '实时预览效果',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 滑动条
                  Row(
                    children: [
                      const Text('清晰', style: TextStyle(fontSize: 12)),
                      Expanded(
                        child: Slider(
                          value: blurService.blurIntensity,
                          min: 0.0,
                          max: 30.0,
                          divisions: 30,
                          label: '${blurService.blurIntensity.round()}',
                          onChanged: (value) {
                            blurService.setBlurIntensity(value);
                          },
                        ),
                      ),
                      const Text('模糊', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  Text(
                    '当前值: ${blurService.blurIntensity.round()}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 快捷设置按钮
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => blurService.setBlurIntensity(5.0),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.blue),
                          ),
                          child: const Text('轻微', style: TextStyle(fontSize: 12)),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => blurService.setBlurIntensity(15.0),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.blue),
                          ),
                          child: const Text('中等', style: TextStyle(fontSize: 12)),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => blurService.setBlurIntensity(25.0),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.blue),
                          ),
                          child: const Text('强烈', style: TextStyle(fontSize: 12)),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('关闭'),
                ),
                ElevatedButton(
                  onPressed: () => blurService.resetToDefault(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6B46C1),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('重置'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 🎴 显示卡牌样式选择器对话框
  void _showCardStyleSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return Consumer<CardPreferenceService>(
          builder: (context, cardService, child) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              title: Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Row(
                    children: [
                      const Icon(
                        Icons.style,
                        color: Colors.purple,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        languageManager.translate('choose_card_style'),
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  );
                },
              ),
              content: SizedBox(
                width: double.maxFinite,
                height: 300, // 减少对话框高度
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Consumer<LanguageManager>(
                      builder: (context, languageManager, child) {
                        return Text(
                          languageManager.translate('choose_tarot_back_style'),
                          style: const TextStyle(fontSize: 12),
                        );
                      },
                    ),
                    const SizedBox(height: 12), // 减少间距
                    
                    // 卡牌样式网格 - 优化图片大小一致性
                    Expanded(
                      child: GridView.builder(
                        shrinkWrap: true,
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          childAspectRatio: 0.75, // 调整为更专业的比例
                        ),
                        itemCount: CardPreferenceService.availableCardBacks.length,
                        itemBuilder: (context, index) {
                          final cardData = CardPreferenceService.availableCardBacks[index];
                          final isSelected = cardService.selectedCardBack == cardData['image'];
                          
                          return GestureDetector(
                            onTap: () {
                              cardService.setCardBack(cardData['image']!);
                              Navigator.pop(context);
                              final languageManager = Provider.of<LanguageManager>(context, listen: false);
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    '${languageManager.translate('selected')} ${languageManager.translate(cardData['key']!)} ${languageManager.translate('card_style')}'
                                  ),
                                  backgroundColor: Colors.green,
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isSelected ? Colors.purple : Colors.grey.withValues(alpha: 0.3),
                                  width: isSelected ? 3 : 1,
                                ),
                                boxShadow: isSelected ? [
                                  BoxShadow(
                                    color: Colors.purple.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                  ),
                                ] : [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  // 卡牌预览图 - 优化大小一致性
                                  Expanded(
                                    flex: 4,
                                    child: Container(
                                      margin: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.15),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: AspectRatio(
                                          aspectRatio: 2/3, // 标准塔罗牌比例
                                          child: Image.asset(
                                            'assets/images/${cardData['image']}',
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) {
                                              return Container(
                                                color: Colors.grey[300],
                                                child: const Center(
                                                  child: Icon(
                                                    Icons.image_not_supported,
                                                    size: 24,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  // 样式名称 - 优化显示
                                  Container(
                                    height: 40, // 固定高度确保一致性
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: isSelected ? Colors.purple.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.05),
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(12),
                                        bottomRight: Radius.circular(12),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        if (isSelected) ...[
                                          const Icon(
                                            Icons.check_circle,
                                            color: Colors.purple,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                        ],
                                        Flexible(
                                          child: Consumer<LanguageManager>(
                                            builder: (context, languageManager, child) {
                                              return Text(
                                                languageManager.translate(cardData['key']!),
                                                style: TextStyle(
                                                  fontSize: 11,
                                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                                                  color: isSelected ? Colors.purple : Colors.black87,
                                                ),
                                                textAlign: TextAlign.center,
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    cardService.resetToDefault();
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('已重置为默认卡牌样式'),
                        backgroundColor: Colors.blue,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6B46C1),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('重置'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 🔗 启动URL
  void _launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('无法启动URL: $url');
      }
    } catch (e) {
      debugPrint('启动URL时出错: $e');
    }
  }
}

/// 自动转换为大写的输入格式化器
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}