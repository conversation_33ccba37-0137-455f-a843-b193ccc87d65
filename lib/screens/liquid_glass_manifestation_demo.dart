import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ai_tarot_reading/models/manifestation_goal.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'dart:ui';

class LiquidGlassManifestationDemo extends StatefulWidget {
  const LiquidGlassManifestationDemo({super.key});

  @override
  State<LiquidGlassManifestationDemo> createState() => _LiquidGlassManifestationDemoState();
}

class _LiquidGlassManifestationDemoState extends State<LiquidGlassManifestationDemo> {
  // 存储每个卡片的状态
  final Map<String, Map<String, dynamic>> _affirmationStates = {};

  // 模拟数据
  final List<ManifestationGoal> _demoGoals = [
    ManifestationGoal(
      id: 'demo1',
      title: '提升自信心',
      description: '通过每日肯定语练习，建立内在的自信和力量',
      status: ManifestationStatus.manifesting,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      affirmation: '我是一个充满自信的人，我相信自己的能力|||我值得拥有美好的生活和成功|||我的内在力量无限强大|||我每天都在变得更加自信|||我勇敢面对挑战并从中成长',
      isAffirmationGenerated: true,
      syncStatus: SyncStatus.synced,
    ),
    ManifestationGoal(
      id: 'demo2',
      title: '吸引理想工作',
      description: '专注于吸引符合我价值观和技能的理想职业机会',
      status: ManifestationStatus.pending,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      affirmation: '我正在吸引完美的工作机会|||我的技能和才华被认可和重视|||我在理想的环境中工作并获得丰厚回报',
      isAffirmationGenerated: true,
      syncStatus: SyncStatus.synced,
    ),
    ManifestationGoal(
      id: 'demo3',
      title: '改善人际关系',
      description: '建立更深层次的连接，吸引积极正面的人际关系',
      status: ManifestationStatus.manifested,
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      affirmation: '我吸引真诚友善的人进入我的生活',
      isAffirmationGenerated: true,
      syncStatus: SyncStatus.synced,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          // 背景渐变效果
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF667eea).withValues(alpha: 0.1),
              const Color(0xFF764ba2).withValues(alpha: 0.1),
              const Color(0xFFf093fb).withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 自定义AppBar
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(18),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 0.5,
                          ),
                        ),
                        child: Icon(
                          Icons.arrow_back_ios_new_rounded,
                          color: Colors.black.withValues(alpha: 0.8),
                          size: 18,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '液态玻璃显化语卡片',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black.withValues(alpha: 0.9),
                        letterSpacing: -0.4,
                      ),
                    ),
                  ],
                ),
              ),
              
              // 卡片列表
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  itemCount: _demoGoals.length,
                  itemBuilder: (context, index) {
                    final goal = _demoGoals[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: _buildLiquidGlassManifestationCard(goal),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLiquidGlassManifestationCard(ManifestationGoal goal) {
    return Container(
      decoration: BoxDecoration(
        // 液态玻璃卡片效果
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题和状态
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        goal.title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black.withValues(alpha: 0.9),
                          letterSpacing: -0.4,
                        ),
                      ),
                    ),
                    _buildLiquidGlassStatusSelector(goal),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 描述
                Text(
                  goal.description ?? '',
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.black.withValues(alpha: 0.7),
                    height: 1.4,
                    letterSpacing: -0.2,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // 肯定语卡片
                if (goal.isAffirmationGenerated && goal.affirmation != null)
                  _buildLiquidGlassAffirmationsWidget(goal),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLiquidGlassStatusSelector(ManifestationGoal goal) {
    return GestureDetector(
      onTap: () => _showLiquidGlassStatusModal(goal),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Color(goal.status.colorValue),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Color(goal.status.colorValue).withValues(alpha: 0.4),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Text(
              _getStatusText(goal.status),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.black.withValues(alpha: 0.8),
                letterSpacing: -0.1,
              ),
            ),
            const SizedBox(width: 6),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              color: Colors.black.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _getStatusText(ManifestationStatus status) {
    switch (status) {
      case ManifestationStatus.pending:
        return '待显化';
      case ManifestationStatus.manifesting:
        return '显化中';
      case ManifestationStatus.manifested:
        return '已显化';
    }
  }

  Widget _buildLiquidGlassAffirmationsWidget(ManifestationGoal goal) {
    final affirmations = (goal.affirmation ?? '').split('|||');
    final hasMultiple = affirmations.length > 1;
    
    final stateKey = goal.id;
    int currentIndex = _affirmationStates[stateKey]?['currentIndex'] ?? 0;
    bool isExpanded = _affirmationStates[stateKey]?['isExpanded'] ?? false;

    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.15),
              width: 0.5,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 简洁标题栏 - 只有向下图标
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      onTap: () {
                        HapticFeedback.lightImpact();
                        setState(() {
                          isExpanded = !isExpanded;
                          _affirmationStates[stateKey] = {
                            'currentIndex': currentIndex,
                            'isExpanded': isExpanded,
                          };
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        child: Row(
                          children: [
                            Container(
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                color: const Color(0xFF007AFF).withValues(alpha: 0.8),
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return Text(
                                  languageManager.translate('affirmations'),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 15,
                                    color: Colors.black.withValues(alpha: 0.85),
                                    letterSpacing: -0.2,
                                  ),
                                );
                              },
                            ),
                            if (hasMultiple) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF007AFF).withValues(alpha: 0.15),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '${affirmations.length}',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF007AFF),
                                  ),
                                ),
                              ),
                            ],
                            const Spacer(),
                            AnimatedRotation(
                              turns: isExpanded ? 0.5 : 0,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOutCubic,
                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.keyboard_arrow_down_rounded,
                                  color: Colors.black.withValues(alpha: 0.6),
                                  size: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  // 内容区域
                  if (isExpanded) ...[
                    Container(
                      height: 0.5,
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.transparent,
                            Colors.white.withValues(alpha: 0.2),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // 肯定语显示
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.12),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.2),
                                width: 0.5,
                              ),
                            ),
                            child: Text(
                              affirmations[currentIndex].trim(),
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black.withValues(alpha: 0.9),
                                height: 1.5,
                                fontWeight: FontWeight.w400,
                                letterSpacing: -0.1,
                              ),
                            ),
                          ),
                          
                          // 导航控制
                          if (hasMultiple) ...[
                            const SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // 上一条按钮
                                _buildNavButton(
                                  icon: Icons.chevron_left_rounded,
                                  enabled: currentIndex > 0,
                                  onTap: () {
                                    if (currentIndex > 0) {
                                      HapticFeedback.lightImpact();
                                      setState(() {
                                        currentIndex--;
                                        _affirmationStates[stateKey] = {
                                          'currentIndex': currentIndex,
                                          'isExpanded': isExpanded,
                                        };
                                      });
                                    }
                                  },
                                ),
                                
                                const SizedBox(width: 20),
                                
                                // 页码指示器
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF007AFF).withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: const Color(0xFF007AFF).withValues(alpha: 0.2),
                                      width: 0.5,
                                    ),
                                  ),
                                  child: Text(
                                    '${currentIndex + 1} / ${affirmations.length}',
                                    style: const TextStyle(
                                      fontSize: 13,
                                      color: Color(0xFF007AFF),
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: -0.1,
                                    ),
                                  ),
                                ),
                                
                                const SizedBox(width: 20),
                                
                                // 下一条按钮
                                _buildNavButton(
                                  icon: Icons.chevron_right_rounded,
                                  enabled: currentIndex < affirmations.length - 1,
                                  onTap: () {
                                    if (currentIndex < affirmations.length - 1) {
                                      HapticFeedback.lightImpact();
                                      setState(() {
                                        currentIndex++;
                                        _affirmationStates[stateKey] = {
                                          'currentIndex': currentIndex,
                                          'isExpanded': isExpanded,
                                        };
                                      });
                                    }
                                  },
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required bool enabled,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: enabled 
              ? Colors.white.withValues(alpha: 0.8)
              : Colors.white.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 0.5,
          ),
          boxShadow: enabled ? [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Icon(
          icon,
          color: enabled 
              ? const Color(0xFF007AFF) 
              : Colors.grey.withValues(alpha: 0.5),
          size: 20,
        ),
      ),
    );
  }

  void _showLiquidGlassStatusModal(ManifestationGoal goal) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (context) {
        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 30,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 36,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('select_status'),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black.withValues(alpha: 0.9),
                          letterSpacing: -0.3,
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(height: 20),
                  
                  ...ManifestationStatus.values.map((status) {
                    final isSelected = goal.status == status;
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(16),
                          onTap: () {
                            HapticFeedback.lightImpact();
                            Navigator.pop(context);
                            // 演示页面，不实际修改状态
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                            decoration: BoxDecoration(
                              color: isSelected 
                                  ? Color(status.colorValue).withValues(alpha: 0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(16),
                              border: isSelected ? Border.all(
                                color: Color(status.colorValue).withValues(alpha: 0.3),
                                width: 1,
                              ) : null,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: Color(status.colorValue),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(status.colorValue).withValues(alpha: 0.3),
                                        blurRadius: 4,
                                        spreadRadius: 1,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    _getStatusText(status),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                      color: Colors.black.withValues(alpha: 0.9),
                                      letterSpacing: -0.2,
                                    ),
                                  ),
                                ),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle_rounded,
                                    color: Color(status.colorValue),
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
