import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 邀请码调试界面
class DebugInvitationScreen extends StatefulWidget {
  const DebugInvitationScreen({super.key});

  @override
  State<DebugInvitationScreen> createState() => _DebugInvitationScreenState();
}

class _DebugInvitationScreenState extends State<DebugInvitationScreen> {
  final _codeController = TextEditingController();
  final _logs = <String>[];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addLog('🚀 调试界面初始化');
    _checkUserStatus();
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
    print(message); // 同时输出到控制台
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  Future<void> _checkUserStatus() async {
    final user = Supabase.instance.client.auth.currentUser;
    if (user != null) {
      _addLog('👤 当前用户: ${user.email}');
      _addLog('🆔 用户ID: ${user.id}');
    } else {
      _addLog('❌ 用户未登录');
    }
  }

  Future<void> _testDirectDatabaseQuery() async {
    _addLog('🔍 测试直接数据库查询...');
    
    try {
      final response = await Supabase.instance.client
          .from('invitation_codes')
          .select('code, description, is_active, max_uses, current_uses')
          .limit(10);
      
      _addLog('✅ 数据库查询成功');
      _addLog('📊 查询结果: ${response.length} 条记录');
      
      for (final code in response) {
        _addLog('  - ${code['code']}: ${code['description']} (active: ${code['is_active']})');
      }
    } catch (e) {
      _addLog('❌ 数据库查询失败: $e');
    }
  }

  Future<void> _testEdgeFunction() async {
    if (_codeController.text.trim().isEmpty) {
      _addLog('❌ 请输入邀请码');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    _addLog('🚀 测试 Edge Function...');
    
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      _addLog('❌ 用户未登录，无法测试');
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      final requestBody = {
        'p_code': _codeController.text.trim().toUpperCase(),
        'p_user_id': user.id,
        'p_ip_address': null,
        'p_user_agent': 'Debug Tool',
      };

      _addLog('📤 请求参数: $requestBody');

      final response = await Supabase.instance.client.functions.invoke(
        'use-invitation-code',
        body: requestBody,
      );

      _addLog('📡 Edge Function 响应:');
      _addLog('  - Status: ${response.status}');
      _addLog('  - Data: ${response.data}');

      if (response.data != null) {
        final result = response.data as Map<String, dynamic>;
        if (result['success'] == true) {
          _addLog('✅ 邀请码验证成功！');
          _addLog('🎁 奖励: ${result['description']}');
        } else {
          _addLog('❌ 邀请码验证失败');
          _addLog('📝 错误: ${result['message']}');
          _addLog('🔍 错误代码: ${result['error_code']}');
          if (result['debug'] != null) {
            _addLog('🐛 调试信息: ${result['debug']}');
          }
        }
      }
    } catch (e) {
      _addLog('❌ Edge Function 调用失败: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testInvitationService() async {
    if (_codeController.text.trim().isEmpty) {
      _addLog('❌ 请输入邀请码');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    _addLog('🧪 测试 InvitationService...');

    try {
      final invitationService = Provider.of<InvitationService>(context, listen: false);
      final result = await invitationService.useInvitationCode(_codeController.text.trim());

      _addLog('📋 InvitationService 结果:');
      _addLog('  - 成功: ${result.success}');
      _addLog('  - 消息: ${result.message}');
      _addLog('  - 奖励类型: ${result.rewardType}');
    } catch (e) {
      _addLog('❌ InvitationService 测试失败: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black87,
      appBar: AppBar(
        title: const Text('邀请码调试工具'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear_all),
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 输入区域
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[900],
            child: Column(
              children: [
                TextField(
                  controller: _codeController,
                  decoration: const InputDecoration(
                    labelText: '邀请码',
                    hintText: '输入邀请码 (如: HELLO1)',
                    border: OutlineInputBorder(),
                    labelStyle: TextStyle(color: Colors.white70),
                    hintStyle: TextStyle(color: Colors.white54),
                  ),
                  style: const TextStyle(color: Colors.white),
                  textCapitalization: TextCapitalization.characters,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testDirectDatabaseQuery,
                        child: const Text('测试数据库'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testEdgeFunction,
                        child: const Text('测试 Edge Function'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testInvitationService,
                        child: const Text('测试服务'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 日志区域
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        '调试日志',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (_isLoading)
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        border: Border.all(color: Colors.grey[700]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: _logs.map((log) {
                            Color textColor = Colors.white70;
                            if (log.contains('❌')) textColor = Colors.red;
                            if (log.contains('✅')) textColor = Colors.green;
                            if (log.contains('🔍') || log.contains('📊')) textColor = Colors.blue;
                            if (log.contains('⚠️')) textColor = Colors.orange;

                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: SelectableText(
                                log,
                                style: TextStyle(
                                  color: textColor,
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }
}
