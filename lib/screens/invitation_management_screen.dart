import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

// 邀请码管理界面
class InvitationManagementScreen extends StatefulWidget {
  const InvitationManagementScreen({super.key});

  @override
  State<InvitationManagementScreen> createState() => _InvitationManagementScreenState();
}

class _InvitationManagementScreenState extends State<InvitationManagementScreen> {
  InvitationStats? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    try {
      final invitationService = Provider.of<InvitationService>(context, listen: false);
      final stats = await invitationService.getInvitationStats();
      setState(() {
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _copyInvitationCode(String code) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageManager.translate('invitation_code_copied')),
        backgroundColor: FigmaTheme.primaryPink,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _shareInvitationCode(String code) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    final shareTemplate = languageManager.translate('invitation_share_template');
    final text = shareTemplate.replaceAll('{code}', code);

    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageManager.translate('invitation_text_copied')),
        backgroundColor: FigmaTheme.primaryPink,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // 渐变背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(-1.0, -1.0),
                end: Alignment(1.0, 1.0),
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                  Color(0xFFE6E6FA),
                  Color(0xFF87CEEB),
                ],
              ),
            ),
          ),

          // 顶部导航栏
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                decoration: FigmaTheme.createGlassDecoration(
                  opacity: 0.9,
                  radius: 0,
                ),
                child: Row(
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24),
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          width: 48,
                          height: 48,
                          alignment: Alignment.center,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: FigmaTheme.textPrimary,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        languageManager.translate('invitation_management'),
                        style: const TextStyle(
                          color: FigmaTheme.textPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48),
                  ],
                ),
              ),
            ),
          ),

          // 主要内容
          Positioned.fill(
            top: 120,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // 我的邀请码卡片
                  Consumer<InvitationService>(
                    builder: (context, invitationService, child) {
                      return _buildMyInvitationCodeCard(invitationService);
                    },
                  ),

                  const SizedBox(height: 20),

                  // 邀请统计卡片
                  _buildInvitationStatsCard(),

                  const SizedBox(height: 20),

                  // 邀请规则说明
                  _buildInvitationRulesCard(),

                  const SizedBox(height: 20),

                  // 分享按钮
                  Consumer<InvitationService>(
                    builder: (context, invitationService, child) {
                      return _buildShareButton(invitationService);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMyInvitationCodeCard(InvitationService invitationService) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: FigmaTheme.createGlassDecoration(
        opacity: 0.8,
        radius: 20,
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.card_giftcard,
                color: FigmaTheme.primaryPink,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('my_invitation_code'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: FigmaTheme.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // 邀请码显示
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  FigmaTheme.primaryPink.withOpacity(0.1),
                  FigmaTheme.primaryPink.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: FigmaTheme.primaryPink.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Text(
                  invitationService.myInvitationCode ?? 'LOADING',
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 6,
                    color: FigmaTheme.primaryPink,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  languageManager.translate('invitation_reward_description'),
                  style: const TextStyle(
                    fontSize: 14,
                    color: FigmaTheme.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 复制按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                if (invitationService.myInvitationCode != null) {
                  _copyInvitationCode(invitationService.myInvitationCode!);
                }
              },
              icon: const Icon(Icons.copy, size: 20),
              label: Text(languageManager.translate('copy_invitation_code')),
              style: ElevatedButton.styleFrom(
                backgroundColor: FigmaTheme.primaryPink,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildInvitationStatsCard() {
    final languageManager = Provider.of<LanguageManager>(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: FigmaTheme.createGlassDecoration(
        opacity: 0.8,
        radius: 20,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.analytics,
                color: FigmaTheme.primaryPink,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('invitation_stats'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: FigmaTheme.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(
                color: FigmaTheme.primaryPink,
              ),
            )
          else if (_stats != null)
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    languageManager.translate('total_invitations'),
                    _stats!.totalInvitations.toString(),
                    Icons.people,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    languageManager.translate('successful_invitations'),
                    _stats!.successfulInvitations.toString(),
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    languageManager.translate('reward_days'),
                    '${_stats!.totalRewardDays}${languageManager.currentLanguage.startsWith('zh') ? '天' : ' days'}',
                    Icons.card_giftcard,
                  ),
                ),
              ],
            ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: FigmaTheme.primaryPink,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: FigmaTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: FigmaTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildInvitationRulesCard() {
    final languageManager = Provider.of<LanguageManager>(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: FigmaTheme.createGlassDecoration(
        opacity: 0.8,
        radius: 20,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: FigmaTheme.primaryPink,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('invitation_rules'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: FigmaTheme.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildRuleItem('🌟', languageManager.currentLanguage.startsWith('zh')
            ? '新用户使用您的邀请码，双方都获得7天会员'
            : 'New users use your invitation code, both get 7-day membership'),
          _buildRuleItem('✨', languageManager.currentLanguage.startsWith('zh')
            ? '会员期间可享受AI智能显化指导'
            : 'AI-powered manifestation guidance during membership'),
          _buildRuleItem('🎯', languageManager.currentLanguage.startsWith('zh')
            ? '专属个人成长目标管理和肯定语生成'
            : 'Exclusive personal growth goal management and affirmation generation'),
          _buildRuleItem('💫', languageManager.currentLanguage.startsWith('zh')
            ? '邀请越多，一起成长的伙伴越多'
            : 'More invitations, more growth companions together'),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 400.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildRuleItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: FigmaTheme.textSecondary,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareButton(InvitationService invitationService) {
    final languageManager = Provider.of<LanguageManager>(context);

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          if (invitationService.myInvitationCode != null) {
            _shareInvitationCode(invitationService.myInvitationCode!);
          }
        },
        icon: const Icon(Icons.share, size: 24),
        label: Text(
          languageManager.translate('share_invitation_code'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: FigmaTheme.primaryPink,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          shadowColor: FigmaTheme.primaryPink.withOpacity(0.3),
        ),
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 600.ms).scale(begin: const Offset(0.8, 0.8));
  }
}
