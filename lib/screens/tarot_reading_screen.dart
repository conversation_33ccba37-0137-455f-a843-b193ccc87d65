import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/widgets/hexagram_selector.dart';
import 'package:ai_tarot_reading/widgets/card_selection_view.dart';
import 'package:ai_tarot_reading/widgets/ai_reading_view.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/services/app_background_service.dart';

class TarotReadingScreen extends StatefulWidget {
  const TarotReadingScreen({super.key});

  @override
  State<TarotReadingScreen> createState() => _TarotReadingScreenState();
}

class _TarotReadingScreenState extends State<TarotReadingScreen> {
  final TextEditingController _questionController = TextEditingController();

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context);
    final languageManager = Provider.of<LanguageManager>(context);

    // 调试信息
    print('🔍 TarotReadingScreen build:');
    print('  - readingStage: ${appState.readingStage}');
    print('  - currentQuestion: "${appState.currentQuestion}"');
    print('  - currentQuestion.isEmpty: ${appState.currentQuestion.isEmpty}');

    // 如果是初始状态，显示主页面
    if (appState.readingStage == ReadingStage.askQuestion &&
        appState.currentQuestion.isEmpty) {
      print('  ✅ 显示主页面 (_buildMainHomePage)');
      return _buildMainHomePage(appState, languageManager);
    }

    print('  ❌ 显示其他页面 (非主页面)');
    print('  - 条件1 (readingStage == askQuestion): ${appState.readingStage == ReadingStage.askQuestion}');
    print('  - 条件2 (currentQuestion.isEmpty): ${appState.currentQuestion.isEmpty}');

    return Scaffold(
      appBar: AppBar(
        title: Text(languageManager.translate('tarot_reading')),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (appState.readingStage != ReadingStage.askQuestion)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text(languageManager.translate('restart')),
                    content: Text(languageManager.translate('confirm_restart')),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(languageManager.translate('cancel')),
                      ),
                      TextButton(
                        onPressed: () {
                          appState.resetReadingProcess();
                          _questionController.clear();
                          Navigator.pop(context);
                        },
                        child: Text(languageManager.translate('reset')),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: _buildCurrentStageView(appState),
    );
  }

  Widget _buildMainHomePage(AppStateProvider appState, LanguageManager languageManager) {
    return Scaffold(
      backgroundColor: Colors.transparent, // 透明背景，使用HomeScreen的背景
      body: Stack(
        children: [
          // 主要内容
          Consumer<SubscriptionService>(
            builder: (context, subscriptionService, child) {
              // 调试信息
              print('🔍 SubscriptionService 状态:');
              print('  - remainingFreeUsage: ${subscriptionService.remainingFreeUsage}');
              print('  - isSubscribed: ${subscriptionService.isSubscribed}');
              print('  - canUseToday: ${subscriptionService.canUseToday}');
              print('  - usageLimit: ${subscriptionService.usageLimit}');

              return SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Column(
                    children: [
                      // 顶部空间
                      const SizedBox(height: 40),

                      // 吉祥物图片区域
                      SizedBox(
                        width: 300,
                        height: 300,
                        child: Consumer<AppBackgroundService>(
                          builder: (context, appBackgroundService, child) {
                            return Image.asset(
                              appBackgroundService.currentBackgroundPath,
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                // 如果图片加载失败，显示一个占位符
                                return Container(
                                  width: 300,
                                  height: 300,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: RadialGradient(
                                      colors: [
                                        const Color(0xFF746DFF).withValues(alpha: 0.3),
                                        const Color(0xFFB9B5FB).withValues(alpha: 0.2),
                                        Colors.transparent,
                                      ],
                                    ),
                                  ),
                                  child: Center(
                                    child: Container(
                                      width: 120,
                                      height: 120,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: const Color(0xFF746DFF),
                                        boxShadow: [
                                          BoxShadow(
                                            color: const Color(0xFF746DFF).withValues(alpha: 0.4),
                                            blurRadius: 20,
                                            offset: const Offset(0, 8),
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.auto_awesome,
                                        size: 60,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ).animate(
                          onPlay: (controller) => controller.repeat(reverse: true),
                        ).moveY(
                          begin: 0,
                          end: -10,
                          duration: 2000.ms,
                          curve: Curves.easeInOut,
                        ),
                      ),

                      const SizedBox(height: 40),

                      // 激励文字
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Text(
                          languageManager.translate('embrace_future_message'),
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: 20, // 调整字体大小以适应不同屏幕
                            height: 1.4,
                            color: Color(0xFF5D5E61),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 60),

                      // 开始占卜按钮
                      Container(
                        width: double.infinity,
                        height: 60,
                        margin: const EdgeInsets.symmetric(horizontal: 40),
                        decoration: BoxDecoration(
                          color: const Color(0xFF746DFF),
                          border: Border.all(
                            color: const Color(0xFFB9B5FB),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF746DFF).withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(30),
                            onTap: () {
                              _handleStartDivination(context, subscriptionService, appState);
                            },
                            child: Container(
                              alignment: Alignment.center,
                              child: Text(
                                languageManager.translate('start_divination'),
                                style: const TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: 20,
                                  color: Color(0xFFDEDCFE),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.5, end: 0),

                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              );
            },
          ),

          // 右上角剩余次数显示 - 强制显示，不依赖SubscriptionService
          Positioned(
            top: 150, // 临时调整位置测试是否显示
            right: 15,
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(25),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF8B5CF6),
                      Color(0xFF6366F1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(color: Colors.white, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF8B5CF6).withOpacity(0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.star, // 强制显示星星图标
                      color: Colors.white,
                      size: 18,
                    ),
                    SizedBox(width: 6),
                    Text(
                      '测试显示：剩余3次', // 强制显示测试文本
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ).animate().fadeIn(duration: 1000.ms).slideX(begin: 1.0, end: 0),
        ],
      ),
    );
  }

  // 获取使用次数显示文本
  String _getUsageDisplayText(SubscriptionService subscriptionService) {
    if (subscriptionService.isPremium) {
      return '高级会员 ${subscriptionService.remainingFreeUsage}/${subscriptionService.usageLimit}';
    } else if (subscriptionService.isBasic) {
      return '基础会员 ${subscriptionService.remainingFreeUsage}/${subscriptionService.usageLimit}';
    } else {
      // 免费用户
      return '剩余次数：${subscriptionService.remainingFreeUsage}';
    }
  }

  // 获取使用次数图标
  IconData _getUsageIcon(SubscriptionService subscriptionService) {
    if (subscriptionService.isPremium) {
      return Icons.diamond; // 高级会员用钻石图标
    } else if (subscriptionService.isBasic) {
      return Icons.workspace_premium; // 基础会员用VIP图标
    } else {
      return Icons.auto_awesome; // 免费用户用星星图标
    }
  }

  // 处理开始占卜逻辑，包含会员检查
  void _handleStartDivination(BuildContext context, SubscriptionService subscriptionService, AppStateProvider appState) {
    // 检查是否还有剩余次数
    if (!subscriptionService.canUseToday) {
      _showUsageLimitDialog(context, subscriptionService);
      return;
    }

    // 有剩余次数，显示问题输入对话框
    _showQuestionDialog(appState, subscriptionService);
  }

  // 显示使用次数限制对话框
  void _showUsageLimitDialog(BuildContext context, SubscriptionService subscriptionService) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 28,
            ),
            const SizedBox(width: 8),
            Text(
              languageManager.translate('usage_limit_reached'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              subscriptionService.isWeeklyLimit
                ? '${languageManager.translate('weekly_limit_message')} ${subscriptionService.usageLimit} ${languageManager.translate('times_divination_service')}'
                : languageManager.translate('daily_usage_exhausted'),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageManager.translate('upgrade_member_benefits'),
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  const SizedBox(height: 8),
                  Text(languageManager.translate('basic_member_daily_1'), style: const TextStyle(fontSize: 13)),
                  Text(languageManager.translate('premium_member_daily_5'), style: const TextStyle(fontSize: 13)),
                  Text(languageManager.translate('unlimited_access_features'), style: const TextStyle(fontSize: 13)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageManager.translate('maybe_later')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 导航到订阅页面
              // Navigator.push(context, MaterialPageRoute(builder: (context) => SubscriptionScreen()));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF746DFF),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(languageManager.translate('upgrade_now')),
          ),
        ],
      ),
    );
  }

  void _showQuestionDialog(AppStateProvider appState, SubscriptionService subscriptionService) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          languageManager.translate('what_would_you_like_to_know'),
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _questionController,
              decoration: InputDecoration(
                hintText: languageManager.translate('enter_your_question_placeholder'),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
              maxLines: 3,
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              _questionController.clear();
              Navigator.pop(context);
            },
            child: Text(languageManager.translate('cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              if (_questionController.text.trim().isNotEmpty) {
                appState.setCurrentQuestion(_questionController.text.trim());
                appState.setSelectedSpreadType(SpreadType.none);
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF746DFF),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(languageManager.translate('continue')),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentStageView(AppStateProvider appState) {
    switch (appState.readingStage) {
      case ReadingStage.askQuestion:
        return _buildAskQuestionView(appState);
      case ReadingStage.selectSpread:
        return _buildSelectSpreadView(appState);
      case ReadingStage.selectCards:
      case ReadingStage.confirmSelection:
        return CardSelectionView(
          spreadType: appState.selectedSpreadType,
          selectedCards: appState.selectedCards,
          onCardSelected: appState.selectCard,
          onConfirm: appState.confirmCardSelection,
          onReset: appState.resetCardSelection,
          isConfirmationStage: appState.readingStage == ReadingStage.confirmSelection,
        );
      case ReadingStage.aiReading:
        print('🎯 TarotReadingScreen: 创建 AiReadingView');
        print('📖 当前解读ID: ${appState.currentReading!.id}');
        return AiReadingView(
          reading: appState.currentReading!,
          onSave: appState.saveReading,
          onFollowUpQuestion: (question, response) {
            appState.addFollowUpQuestion(question, response);
          },
        );
    }
  }

  Widget _buildAskQuestionView(AppStateProvider appState) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFE8F4FD), // 浅蓝色
            Color(0xFFF8E8FF), // 浅紫色
            Color(0xFFFFE8F8), // 浅粉色
          ],
        ),
      ),
      child: Stack(
        children: [
          // 云朵背景效果
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: const Alignment(0.3, -0.5),
                  radius: 1.5,
                  colors: [
                    Colors.white.withValues(alpha: 0.8),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: const Alignment(-0.4, 0.2),
                  radius: 1.2,
                  colors: [
                    Colors.white.withValues(alpha: 0.6),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // 主要内容
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // 顶部空间
                  const SizedBox(height: 40),
                  // 吉祥物区域
                  Expanded(
                    flex: 2,
                    child: Center(
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              const Color(0xFF746DFF).withValues(alpha: 0.2),
                              const Color(0xFFB9B5FB).withValues(alpha: 0.1),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            // 魔法光环效果
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: const Color(0xFF746DFF).withValues(alpha: 0.3),
                                    width: 2,
                                  ),
                                ),
                              ).animate(
                                onPlay: (controller) => controller.repeat(),
                              ).scale(
                                begin: const Offset(0.8, 0.8),
                                end: const Offset(1.2, 1.2),
                                duration: 3000.ms,
                                curve: Curves.easeInOut,
                              ).fadeIn(duration: 1500.ms).then().fadeOut(duration: 1500.ms),
                            ),
                            // 中心图标
                            Center(
                              child: Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: const Color(0xFF746DFF),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0xFF746DFF).withValues(alpha: 0.4),
                                      blurRadius: 20,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.auto_awesome,
                                  size: 40,
                                  color: Colors.white,
                                ),
                              ).animate(
                                onPlay: (controller) => controller.repeat(reverse: true),
                              ).moveY(
                                begin: 0,
                                end: -8,
                                duration: 2000.ms,
                                curve: Curves.easeInOut,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // 问题输入区域
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const Text(
                          '您想了解什么？',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D3748),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return TextField(
                                controller: _questionController,
                                decoration: InputDecoration(
                                  hintText: languageManager.translate('question_input_hint'),
                                  hintStyle: TextStyle(
                                    color: Colors.grey[500],
                                    fontSize: 16,
                                  ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.white.withValues(alpha: 0.9),
                              contentPadding: const EdgeInsets.all(20),
                            ),
                            maxLines: 3,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF2D3748),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 32),
                        // 使用您提供的按钮样式
                        SizedBox(
                          width: double.infinity,
                          height: 87,
                          child: Stack(
                            children: [
                              // 按钮背景
                              Positioned.fill(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF746DFF),
                                    border: Border.all(
                                      color: const Color(0xFFB9B5FB),
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(33),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0xFF746DFF).withValues(alpha: 0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 8),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // 按钮文字
                              Positioned.fill(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(33),
                                    onTap: _questionController.text.trim().isNotEmpty
                                        ? () {
                                            appState.setCurrentQuestion(_questionController.text.trim());
                                            appState.setSelectedSpreadType(SpreadType.none);
                                          }
                                        : null,
                                    child: const Center(
                                      child: Text(
                                        '继续',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w500,
                                          fontSize: 28.2,
                                          height: 1.2,
                                          color: Color(0xFFDEDCFE),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.5, end: 0),

                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectSpreadView(AppStateProvider appState) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFE8F4FD), // 浅蓝色
            Color(0xFFF8E8FF), // 浅紫色
            Color(0xFFFFE8F8), // 浅粉色
          ],
        ),
      ),
      child: Stack(
        children: [
          // 云朵背景效果
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: const Alignment(0.3, -0.5),
                  radius: 1.5,
                  colors: [
                    Colors.white.withValues(alpha: 0.8),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: const Alignment(-0.4, 0.2),
                  radius: 1.2,
                  colors: [
                    Colors.white.withValues(alpha: 0.6),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          // 主要内容
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    '您的问题:',
                    style: TextStyle(
                      fontSize: 16,
                      color: const Color(0xFF2D3748).withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Text(
                      appState.currentQuestion,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    '选择您的牌阵',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  const HexagramSelector(),
                  const SizedBox(height: 32),
                  // 使用相同的按钮样式
                  Container(
                    width: double.infinity,
                    height: 87,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child: Stack(
                      children: [
                        // 按钮背景
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              color: const Color(0xFF746DFF),
                              border: Border.all(
                                color: const Color(0xFFB9B5FB),
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(33),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0xFF746DFF).withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // 按钮文字
                        Positioned.fill(
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(33),
                              onTap: appState.selectedSpreadType != SpreadType.none
                                  ? () => appState.startCardSelection()
                                  : null,
                              child: const Center(
                                child: Text(
                                  '抽取卡牌',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w500,
                                    fontSize: 28.2,
                                    height: 1.2,
                                    color: Color(0xFFDEDCFE),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.5, end: 0),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
