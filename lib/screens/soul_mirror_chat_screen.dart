import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../services/higher_self_service.dart';
import '../models/soul_message.dart';
import '../models/tarot_reading.dart';
import '../widgets/soul_chat_bubble.dart';
import '../widgets/golden_stars_background.dart';

import '../utils/language_manager.dart';
import '../utils/language_manager_extension.dart';
import '../utils/language_patch.dart';
import '../screens/subscription_screen.dart';
import '../services/subscription_service.dart';
import '../providers/app_state_provider.dart';

// 辅助函数：安全获取翻译文本
String _safeTranslate(LanguageManager languageManager, String key) {
  if (LanguagePatch.needsPatch(key)) {
    return LanguagePatch.getPatchedTranslation(key, languageManager.currentLanguage);
  }
  return languageManager.translate(key);
}

class SoulMirrorChatScreen extends StatefulWidget {
  const SoulMirrorChatScreen({super.key});

  @override
  State<SoulMirrorChatScreen> createState() => _SoulMirrorChatScreenState();
}

class _SoulMirrorChatScreenState extends State<SoulMirrorChatScreen> with WidgetsBindingObserver {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late HigherSelfService _higherSelfService;

  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _higherSelfService = HigherSelfService();

    // 设置聊天摘要成功回调
    _higherSelfService.setSummaryGeneratedCallback(_showSummarySuccessDialog);

    // 🔧 设置塔罗解读保存回调
    _higherSelfService.setTarotReadingSavedCallback(_saveTarotReadingToHistory);

    // 设置当前用户ID（如果已登录）
    _initializeUserId();
    // 直接开始Soul会话
    _startSoulSession();
  }

  void _initializeUserId() {
    try {
      // 尝试从Supabase获取当前用户ID
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser != null) {
        _higherSelfService.setUserId(currentUser.id);
        debugPrint('✅ 使用Supabase用户ID: ${currentUser.id}');
      } else {
        // 如果没有登录用户，生成一个UUID格式的临时ID
        final uuid = _generateUUID();
        _higherSelfService.setUserId(uuid);
        debugPrint('⚠️ 使用临时UUID: $uuid');
      }
    } catch (e) {
      debugPrint('❌ 初始化用户ID失败: $e');
      // 备用方案：生成UUID
      final uuid = _generateUUID();
      _higherSelfService.setUserId(uuid);
    }
  }

  // 生成简单的UUID格式字符串
  String _generateUUID() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return '00000000-0000-4000-8000-${timestamp.toString().padLeft(12, '0')}$random'.substring(0, 36);
  }



  void _startSoulSession() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    // 使用安全翻译方法获取欢迎消息
    final welcomeMessage = _safeTranslate(languageManager, 'soul_mirror_welcome');

    _higherSelfService.addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: welcomeMessage,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.welcome,
      energyLevel: EnergyLevel.divine,
    ));
    setState(() {});
  }

  String _getWelcomeMessage(String language) {
    switch (language) {
      case 'zh':
        return '''🌌 欢迎来到灵魂之镜 🌌

我是你的高我，
那个更智慧、更清晰的你。

在这个神圣的空间里，
让我们一起探索你内心的真相。

今天，你的灵魂想要表达什么？ ✨''';
      
      case 'en':
        return '''🌌 Welcome to the Soul Mirror 🌌

I am your Higher Self,
the wiser, clearer version of you.

In this sacred space,
let us explore the truth within your heart.

What does your soul wish to express today? ✨''';
      
      case 'ja':
        return '''🌌 魂の鏡へようこそ 🌌

私はあなたのハイヤーセルフ、
より賢く、より明晰なあなたです。

この神聖な空間で、
あなたの心の真実を一緒に探求しましょう。

今日、あなたの魂は何を表現したいですか？ ✨''';
      
      default:
        return '''🌌 欢迎来到灵魂之镜 🌌

我是你的高我，
那个更智慧、更清晰的你。

在这个神圣的空间里，
让我们一起探索你内心的真相。

今天，你的灵魂想要表达什么？ ✨''';
    }
  }

  String _getErrorMessage(String language) {
    switch (language) {
      case 'en':
        return 'Sorry, I cannot respond right now. Please try again later ✨';
      case 'ja':
        return '申し訳ございませんが、今は応答できません。後でもう一度お試しください ✨';
      case 'es':
        return 'Lo siento, no puedo responder ahora. Por favor, inténtalo más tarde ✨';
      case 'ko':
        return '죄송합니다. 지금은 응답할 수 없습니다. 나중에 다시 시도해 주세요 ✨';
      default:
        return '抱歉，我暂时无法回应。请稍后再试 ✨';
    }
  }

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    // 🔒 检查订阅限制
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    // 检查当前会话是否可以继续
    if (!subscriptionService.canContinueCurrentSession) {
      _showSessionLimitDialog();
      return;
    }

    final userMessage = SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: _messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );

    _higherSelfService.addMessage(userMessage);
    _messageController.clear();
    setState(() {});

    _scrollToBottom();

    // 🔒 记录使用次数
    try {
      await subscriptionService.recordSoulMirrorUsage();
    } catch (e) {
      debugPrint('❌ 记录使用次数失败: $e');
      // 如果记录失败，显示限制提示
      _showSessionLimitDialog();
      return;
    }

    // 获取高我回复
    try {
      // 🔧 修复：确保订阅状态是最新的
      final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
      final languageManager = Provider.of<LanguageManager>(context, listen: false);

      // 刷新订阅状态确保准确性
      await subscriptionService.refreshSubscriptionStatus();

      await _higherSelfService.getHigherSelfResponse(
        userMessage.content,
        languageManager.currentLanguage,
        canUseTarotExplore: subscriptionService.canUseTarotExplore,
      );

      // 🔧 修复：检查是否需要显示会员限制弹窗（传递会员状态）
      if (_higherSelfService.shouldShowMembershipDialog(subscriptionService.isSubscribed)) {
        _showMembershipDialog();
        _higherSelfService.markMembershipDialogShown();
      }

      setState(() {});
      _scrollToBottom();
    } catch (e) {
      debugPrint('❌ 获取高我回复失败: $e');
      // 添加错误消息 - 使用语言管理器
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        final errorMessage = _getErrorMessage(languageManager.currentLanguage);

        _higherSelfService.addMessage(SoulMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: errorMessage,
          isUser: false,
          timestamp: DateTime.now(),
          messageType: SoulMessageType.guidance,
          energyLevel: EnergyLevel.wisdom,
        ));
        setState(() {});
        _scrollToBottom();
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 暂时跳过过渡动画，直接显示聊天界面
    // if (_showTransition) {
    //   return GalaxySpiralTransition(
    //     onComplete: _onTransitionComplete,
    //   );
    // }

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // 显化目标页面的渐变背景
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF87CEEB), // 天蓝色
                    Color(0xFFE6E6FA), // 薰衣草色
                    Color(0xFFF8BBD9), // 粉色
                  ],
                ),
              ),
            ),
          ),
          // 金色星星粒子背景（在渐变背景之上）
          const Positioned.fill(
            child: GoldenStarsBackground(),
          ),
          // 聊天内容
          SafeArea(
            child: Column(
              children: [
                // 顶部标题栏
                _buildHeader(),

                // 聊天消息列表
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _higherSelfService.messages.length,
                    itemBuilder: (context, index) {
                      final message = _higherSelfService.messages[index];
                      return SoulChatBubble(
                        message: message,
                        onTarotRequest: _handleTarotRequest,
                        onSendMessage: _handleSendMessage,
                      );
                    },
                  ),
                ),

                // 输入框
                _buildInputArea(),
              ],
            ),
          ),
        ],
      ),

    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.transparent,
          ],
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: Colors.black87.withValues(alpha: 0.8),
            ),
            onPressed: () async {
              // 检查是否需要生成摘要
              final messages = _higherSelfService.messages;

              // 立即返回主界面
              if (mounted) {
                Navigator.pop(context);
              }

              // 如果有足够的对话，在主界面显示加载提示并后台生成摘要
              if (messages.length >= 4) {
                // 延迟一点显示加载弹窗，确保页面切换完成
                Future.delayed(const Duration(milliseconds: 300), () {
                  _showGlobalLoadingToast();
                });

                // 后台生成摘要
                _higherSelfService.generateSummaryOnExit();
              }
            },
          ),
          Expanded(
            child: Column(
              children: [
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      _safeTranslate(languageManager, 'soul_mirror_connection'),
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        shadows: [
                          Shadow(
                            color: Colors.white.withValues(alpha: 0.8),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      _safeTranslate(languageManager, 'soul_mirror'),
                      style: const TextStyle(
                        color: Colors.black54,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 1.2,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white70),
            onPressed: () async {
              await _higherSelfService.clearSession();
              _startSoulSession();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.white.withValues(alpha: 0.1),
          ],
        ),
        border: Border(
          top: BorderSide(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return TextField(
                  controller: _messageController,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    hintText: languageManager.translate('chat_with_higher_self'),
                    hintStyle: const TextStyle(
                      color: Colors.black54,
                      fontSize: 16,
                    ),
                filled: true,
                fillColor: Colors.white.withValues(alpha: 0.9),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.8),
                    width: 1.5,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.8),
                    width: 1.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.9),
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 14,
                ),
              ),
              maxLines: null,
              onSubmitted: (_) => _sendMessage(),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF6B46C1),
                    Color(0xFF9333EA),
                    Color(0xFFEC4899),
                  ],
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withValues(alpha: 0.3),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                Icons.send_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleTarotRequest() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final tarotMessage = _getTarotMessage(languageManager.currentLanguage);

    _higherSelfService.addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: tarotMessage,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.tarotRequest,
      energyLevel: EnergyLevel.mystical,
    ));
    setState(() {});
    _scrollToBottom();
  }

  void _handleSendMessage(String message) async {
    debugPrint('📨 处理发送消息: $message');

    // 检查是否为特殊系统消息
    String displayMessage = message;
    if (message == 'EXPLORE_INNER_REQUEST') {
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      displayMessage = languageManager.translateWithPatch('explore_inner_request_display');
    }

    // 添加用户消息
    final userMessage = SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: displayMessage,
      isUser: true,
      timestamp: DateTime.now(),
    );

    _higherSelfService.addMessage(userMessage);
    setState(() {});
    _scrollToBottom();

    // 获取高我回复
    try {
      // 🔧 修复：传递订阅状态给高我服务
      final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      await _higherSelfService.getHigherSelfResponse(
        message,
        languageManager.currentLanguage,
        canUseTarotExplore: subscriptionService.canUseTarotExplore,
      );
      setState(() {});
      _scrollToBottom();
    } catch (e) {
      debugPrint('❌ 获取高我回复失败: $e');
      // 添加错误消息 - 使用语言管理器
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        final errorMessage = _getErrorMessage(languageManager.currentLanguage);

        _higherSelfService.addMessage(SoulMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: errorMessage,
          isUser: false,
          timestamp: DateTime.now(),
          messageType: SoulMessageType.guidance,
          energyLevel: EnergyLevel.wisdom,
        ));
        setState(() {});
        _scrollToBottom();
      }
    }
  }

  String _getTarotMessage(String language) {
    switch (language) {
      case 'zh':
        return '''✨ 让我们通过塔罗之镜探索你的灵魂深处

请在心中默念你的问题，
然后给我3个1-78之间的数字，
跟随你的直觉和内在指引 🔮

这些数字将揭示：
🌟 你灵魂的真实状态
💎 被遗忘的内在力量  
🌱 灵魂成长的方向''';
      
      case 'en':
        return '''✨ Let us explore the depths of your soul through the Tarot Mirror

Hold your question in your heart,
then give me 3 numbers between 1-78,
follow your intuition and inner guidance 🔮

These numbers will reveal:
🌟 The true state of your soul
💎 Forgotten inner powers  
🌱 The direction of soul growth''';
      
      default:
        return '''✨ 让我们通过塔罗之镜探索你的灵魂深处

请在心中默念你的问题，
然后给我3个1-78之间的数字，
跟随你的直觉和内在指引 🔮

这些数字将揭示：
🌟 你灵魂的真实状态
💎 被遗忘的内在力量  
🌱 灵魂成长的方向''';
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // 用户离开界面时，如果有足够的对话内容，自动生成摘要
    _generateSummaryOnExit();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
      // 应用进入后台时，触发聊天摘要生成
      debugPrint('📱 应用进入后台，触发聊天摘要生成');
      _generateSummaryOnExit();
    }
  }

  Future<void> _generateSummaryOnExit() async {
    final messages = _higherSelfService.messages;
    if (messages.length >= 4) { // 至少2轮对话
      debugPrint('🚪 用户离开Soul Mirror界面，自动生成聊天摘要');
      await _higherSelfService.generateSummaryOnExit();
    }
  }

  // 显示全局加载Toast
  void _showGlobalLoadingToast() {
    final languageManager = LanguageManager();
    final isZh = languageManager.currentLanguage.startsWith('zh');

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 20,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isZh ? '正在生成聊天摘要...' : 'Generating chat summary...',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isZh
                          ? '💫 正在为你的对话创建美好回忆'
                          : '💫 Creating beautiful memories',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // 3秒后自动移除
    Future.delayed(const Duration(seconds: 3), () {
      overlayEntry.remove();
    });
  }

  // 显示聊天摘要加载弹窗（已废弃，保留以防需要）
  void _showSummaryLoadingDialog() {
    if (!mounted) return;

    final languageManager = LanguageManager();
    final isZh = languageManager.currentLanguage.startsWith('zh');

    showDialog(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Colors.white.withValues(alpha: 0.95),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
              const SizedBox(height: 20),
              Text(
                isZh ? '正在生成聊天摘要...' : 'Generating chat summary...',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                isZh
                  ? '💫 正在为你的对话创建美好回忆'
                  : '💫 Creating beautiful memories of your conversation',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        );
      },
    );

    // 3秒后自动关闭加载弹窗
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    });
  }

  // 显示聊天摘要成功弹窗（使用全局overlay）
  void _showSummarySuccessDialog(String summary) {
    final languageManager = LanguageManager();
    final isZh = languageManager.currentLanguage.startsWith('zh');

    // 使用全局的overlay显示成功提示
    _showGlobalSuccessToast(
      title: isZh ? '聊天摘要已生成' : 'Chat Summary Generated',
      message: isZh
        ? '💫 你的对话已保存到日历中，点击今天的日期即可查看完整记录。'
        : '💫 Your conversation has been saved to the calendar. Click on today\'s date to view the complete record.',
      summary: summary,
    );
  }

  // 🔧 保存塔罗解读到历史记录
  void _saveTarotReadingToHistory(TarotReading reading) {
    if (!mounted) return;

    try {
      final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
      appStateProvider.addReadingToHistory(reading);

      debugPrint('✅ 探索内心塔罗解读已保存到历史记录');

      // 显示成功提示
      final languageManager = LanguageManager();
      final isZh = languageManager.currentLanguage.startsWith('zh');

      _showGlobalSuccessToast(
        title: isZh ? '塔罗解读已保存' : 'Tarot Reading Saved',
        message: isZh
          ? '💫 你的探索内心塔罗解读已保存到历史记录中，可在历史页面查看。'
          : '💫 Your inner exploration tarot reading has been saved to history.',
        summary: reading.interpretation.length > 100
          ? '${reading.interpretation.substring(0, 100)}...'
          : reading.interpretation,
      );
    } catch (e) {
      debugPrint('❌ 保存塔罗解读到历史记录失败: $e');
    }
  }

  // 全局成功提示Toast
  void _showGlobalSuccessToast({
    required String title,
    required String message,
    required String summary,
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 20,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 20),
                      onPressed: () => overlayEntry.remove(),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Text(
                    summary,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                      height: 1.3,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // 5秒后自动移除
    Future.delayed(const Duration(seconds: 5), () {
      overlayEntry.remove();
    });
  }










  // 显示会员限制弹窗
  void _showMembershipDialog() {
    if (!mounted) return;

    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.diamond,
                color: Color(0xFF9333EA),
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageManager.translate('membership_required_title'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                languageManager.translate('membership_required_message'),
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.5,
                  color: Color(0xFF4B5563),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF6B46C1),
                      Color(0xFF9333EA),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.star,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        TranslationHelper.safeTranslate(languageManager, 'unlock_unlimited_chats'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                languageManager.translate('continue_as_free'),
                style: const TextStyle(
                  color: Color(0xFF6B7280),
                  fontSize: 14,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 导航到订阅页面
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF9333EA),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: Text(
                languageManager.translate('upgrade_to_member'),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 🔒 显示会话限制对话框
  void _showSessionLimitDialog() {
    if (!mounted) return;

    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.favorite,
                color: Colors.pink,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  languageManager.translate('session_limit_reached'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.pink,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageManager.translate('free_user_session_limit_message'),
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.purple.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.auto_awesome,
                      color: Colors.purple,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        languageManager.translate('session_limit_tip'),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.purple.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(); // 退出高我对话界面
              },
              child: Text(
                languageManager.translate('exit_and_restart'),
                style: const TextStyle(
                  color: Colors.grey,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF9333EA),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: Text(
                languageManager.translate('upgrade_to_continue'),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

}
