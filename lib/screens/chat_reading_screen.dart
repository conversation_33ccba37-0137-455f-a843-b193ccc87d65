import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/services/simple_ai_tarot_service.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/models/tarot_reading.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';


class ChatReadingScreen extends StatefulWidget {
  final String question;
  final List<TarotCard> selectedCards; // 改为完整的塔罗牌对象列表
  final String spreadType;

  const ChatReadingScreen({
    super.key,
    required this.question,
    required this.selectedCards,
    required this.spreadType,
  });

  @override
  State<ChatReadingScreen> createState() => _ChatReadingScreenState();
}

class _ChatReadingScreenState extends State<ChatReadingScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _hasUsagePermission = true; // 🔒 新增：跟踪用户是否有使用权限







  @override
  void initState() {
    super.initState();
    _initializeChat();
    _checkInitialPermission(); // 🔒 检查初始权限状态
  }

  // 🔒 新增：检查初始权限状态
  void _checkInitialPermission() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
      setState(() {
        _hasUsagePermission = subscriptionService.canUseToday;
      });
      _addLog('🔍 初始权限检查: $_hasUsagePermission');
    });
  }

  // 🔒 新增：检查并更新权限状态
  void _checkAndUpdatePermission() {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
    final newPermission = subscriptionService.canUseToday;

    _addLog('🔍 权限状态检查: 当前=$_hasUsagePermission, 新状态=$newPermission');

    if (_hasUsagePermission != newPermission) {
      setState(() {
        _hasUsagePermission = newPermission;
      });
      _addLog('🔄 权限状态已更新: $_hasUsagePermission');

      // 如果获得了权限，显示欢迎消息
      if (newPermission) {
        _addWelcomeBackMessage();
      }
    }
  }

  // 🔒 新增：显示欢迎回来消息
  void _addWelcomeBackMessage() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    setState(() {
      _messages.add(ChatMessage(
        text: languageManager.translate('welcome_back_with_subscription'),
        isUser: false,
        timestamp: DateTime.now(),
      ));
    });
    _scrollToBottom();
  }

  // 简化的日志方法（仅用于调试，生产环境可删除）
  void _addLog(String message) {
    // 生产环境中可以注释掉这行
    // print(message);
  }

  void _initializeChat() {
    // 直接使用传入的塔罗牌对象（已包含逆位信息）
    final selectedTarotCards = widget.selectedCards;

    // 添加初始的AI消息
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    setState(() {
      _messages.add(ChatMessage(
        text: languageManager.translate('ai_greeting_cards_drawn').replaceAll('{count}', selectedTarotCards.length.toString()),
        isUser: false,
        timestamp: DateTime.now(),
      ));
    });

    // 显示抽中的塔罗牌
    if (selectedTarotCards.isNotEmpty) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          setState(() {
            _messages.add(ChatMessage(
              text: _buildCardDisplayMessage(selectedTarotCards),
              isUser: false,
              timestamp: DateTime.now(),
              cards: selectedTarotCards,
            ));
          });
        }
      });
    }

    // 检查付费状态并开始AI解读
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _checkSubscriptionAndStartReading();
      }
    });
  }

  // 新增：检查订阅状态并决定是否开始AI解读
  void _checkSubscriptionAndStartReading() async {
    _addLog('🔍 开始检查订阅状态...');
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    _addLog('🔍 订阅状态: isSubscribed=${subscriptionService.isSubscribed}');
    _addLog('🔍 今日可用: canUseToday=${subscriptionService.canUseToday}');
    _addLog('🔍 当前等级: ${subscriptionService.currentTier}');

    // 检查是否可以使用AI解读功能
    if (!subscriptionService.canUseToday) {
      _addLog('❌ 今日使用次数已用完，显示订阅提示');
      _showSubscriptionPrompt(subscriptionService);
    } else {
      // 记录使用次数
      _addLog('🔄 开始记录AI解读使用次数...');
      try {
        await subscriptionService.recordUsage();
        _addLog('✅ 使用次数记录成功，开始AI解读...');
        await _addAIResponse(); // 添加 await
      } catch (e) {
        _addLog('❌ 使用次数记录失败: $e');
        _showSubscriptionPrompt(subscriptionService);
      }
    }
  }

  // 修复：根据会员状态显示不同的提示
  void _showSubscriptionPrompt(SubscriptionService subscriptionService) {
    String message;
    bool shouldShowUpgradeDialog = false;

    if (subscriptionService.isPremium) {
      // Premium会员：今日次数用完，明天再来
      message = '''🔮 **今日解读次数已用完**

您今天已经使用了 ${subscriptionService.usageLimit} 次AI解读（Premium会员每日限额）。

✨ **明天再来吧**
明天00:00后，您的解读次数会自动重置，可以继续享受AI塔罗解读服务。

💫 **感谢您的支持**
作为Premium会员，您每天可以享受5次高质量的AI解读。''';
    } else if (subscriptionService.isBasic) {
      // Basic会员：今日次数用完，可以升级或明天再来
      message = '''🔮 **今日解读次数已用完**

您今天已经使用了 ${subscriptionService.usageLimit} 次AI解读（Basic会员每日限额）。

🌟 **两个选择**
1. 明天00:00后继续使用（每天1次）
2. 升级到Premium会员，每天享受5次解读

💡 **升级Premium的好处**
• 每天5次AI解读
• 所有高级功能
• 优先客服支持''';
      shouldShowUpgradeDialog = true;
    } else {
      // 免费用户：本周次数用完，需要升级
      message = '''🔮 **本周免费解读次数已用完**

您本周已经使用了 ${subscriptionService.usageLimit} 次免费AI解读。

🌟 **升级享受更多**
• Basic会员：每天1次解读
• Premium会员：每天5次解读

✨ **立即升级，马上体验更多解读次数！**''';
      shouldShowUpgradeDialog = true;
    }

    setState(() {
      _messages.add(ChatMessage(
        text: message,
        isUser: false,
        timestamp: DateTime.now(),
        isSubscriptionPrompt: shouldShowUpgradeDialog,
      ));
    });

    // 只有非Premium用户才弹出升级页面
    if (shouldShowUpgradeDialog) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          _showSubscriptionDialog();
        }
      });
    }
  }

  // 新增：显示付费对话框
  void _showSubscriptionDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 图标
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6B46C1).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Color(0xFF6B46C1),
                    size: 32,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // 标题
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      languageManager.translate('unlock_ai_tarot_reading'),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    );
                  },
                ),

                const SizedBox(height: 8),

                // 描述
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      languageManager.translate('get_professional_ai_reading'),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    );
                  },
                ),
                
                const SizedBox(height: 24),
                
                // 按钮
                Row(
                  children: [
                    Expanded(
                      child: Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              languageManager.translate('later'),
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 2,
                      child: Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return ElevatedButton(
                            onPressed: () async {
                              Navigator.pop(context);
                              // 🔒 导航到订阅页面并等待返回
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const SubscriptionScreen(),
                                ),
                              );

                              // 🔒 用户从订阅页面返回后，重新检查权限状态
                              if (mounted) {
                                _checkAndUpdatePermission();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF6B46C1),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              languageManager.translate('upgrade_now'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _buildCardDisplayMessage(List<TarotCard> cards) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    if (cards.length == 1) {
      final card = cards.first;
      final position = card.isReversed
          ? '（${languageManager.translate('reversed')}）'
          : '（${languageManager.translate('upright')}）';
      // 使用语言管理器获取翻译后的卡牌名称和描述
      final translatedName = card.getTranslatedName(languageManager.translate);
      final translatedDescription = card.getTranslatedDescription(languageManager.translate);
      return '🎴 ${languageManager.translate('cards_drawn_are')}：\n\n$translatedName$position\n$translatedDescription';
    } else {
      String message = '🎴 ${languageManager.translate('cards_drawn_are')}：\n\n';
      for (int i = 0; i < cards.length; i++) {
        final card = cards[i];
        final position = card.isReversed
            ? '（${languageManager.translate('reversed')}）'
            : '（${languageManager.translate('upright')}）';
        // 使用语言管理器获取翻译后的卡牌名称和描述
        final translatedName = card.getTranslatedName(languageManager.translate);
        final translatedDescription = card.getTranslatedDescription(languageManager.translate);
        message += '${i + 1}. $translatedName$position\n$translatedDescription\n\n';
      }
      return message;
    }
  }

  Future<void> _addAIResponse() async {
    print('🚀 _addAIResponse() 方法被调用');
    if (!mounted) return;

    setState(() {
      _isTyping = true;
    });

    try {
      // 获取选中的塔罗牌
      print('🃏 选中的卡牌: ${widget.selectedCards.map((c) => '${c.name}${c.isReversed ? "(逆位)" : ""}').toList()}');
      final selectedTarotCards = widget.selectedCards;
      print('🃏 解析后的卡牌: ${selectedTarotCards.map((c) => c.name).toList()}');

      // 如果没有卡牌，使用随机卡牌作为fallback
      if (selectedTarotCards.isEmpty) {
        print('⚠️ 没有找到选中的卡牌，使用随机卡牌');
        selectedTarotCards.addAll(TarotCardsData.getRandomCards(1));
      }

      // 获取用户在个人页面设置的语言（而不是设备语言）
      String userLanguage = 'en'; // 默认英文
      try {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        final currentLanguageCode = languageManager.currentLanguage;

        // 根据用户在个人页面设置的语言来决定AI解读语言
        switch (currentLanguageCode) {
          case 'zh-CN':
          case 'zh-TW':
            userLanguage = 'zh';
            break;
          case 'ja-JP':
            userLanguage = 'ja';
            break;
          case 'ko-KR':
            userLanguage = 'ko';
            break;
          case 'es-ES':
            userLanguage = 'es';
            break;
          case 'en-US':
          default:
            userLanguage = 'en';
            break;
        }

        print('🔍 语言调试信息:');
        print('  - 用户设置的语言: $currentLanguageCode');
        print('  - 转换后的AI语言: $userLanguage');
        print('  - 语言来源: LanguageManager (个人页面设置)');
      } catch (e) {
        print('无法获取语言设置，使用默认英文: $e');
      }
      print('🌐 传递给AI的语言: $userLanguage');
      
      // 添加加载状态消息
      final aiMessageIndex = _messages.length;
      setState(() {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        _messages.add(ChatMessage(
          text: languageManager.translate('ai_reading_in_progress'),
          isUser: false,
          timestamp: DateTime.now(),
        ));
        _isTyping = false;
      });

      // 简化版AI塔罗解读
      _addLog('🔄 开始调用AI解读服务...');
      _addLog('❓ 问题: ${widget.question}');
      _addLog('🃏 卡牌: ${selectedTarotCards.map((c) => '${c.name}${c.isReversed ? "(逆位)" : ""}').join(", ")}');
      _addLog('🌐 语言: $userLanguage');

      final reading = await SimpleAITarotService.getInitialReading(
        question: widget.question,
        cards: selectedTarotCards,
        userLanguage: userLanguage,
      );

      _addLog('✅ AI解读生成完成');
      _addLog('📖 解读长度: ${reading.length}字');
      _addLog('📖 解读预览: ${reading.length > 100 ? "${reading.substring(0, 100)}..." : reading}');

      // 更新AI解读消息
      if (mounted) {
        setState(() {
          _messages[aiMessageIndex] = ChatMessage(
            text: reading,
            isUser: false,
            timestamp: _messages[aiMessageIndex].timestamp,
          );
        });

        _scrollToBottom();

        // 添加自动追问（延迟2秒后出现）
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            _addAutomaticFollowUpQuestion(userLanguage);
          }
        });
      }

      // 解读完成后保存到历史记录
      if (reading.isNotEmpty) {
        try {
          final supabaseService = SupabaseDataService();

          // 检查用户是否已登录
          if (!supabaseService.isAuthenticated) {
            print('⚠️ 用户未登录，无法保存到后端数据库');
            // TODO: 提示用户登录或保存到本地
            return;
          }

          // 创建解读记录对象
          final readingId = const Uuid().v4();
          final newReading = TarotReading(
            id: readingId,
            question: widget.question,
            spreadType: _parseSpreadType(widget.spreadType),
            cards: selectedTarotCards,
            interpretation: reading,
            date: DateTime.now(),
            followUpQuestions: [],
            followUpResponses: [],
          );

          _addLog('🔄 开始保存解读到数据库...');
          _addLog('📝 解读ID: $readingId');
          _addLog('❓ 问题: ${widget.question}');
          _addLog('🃏 卡牌数量: ${selectedTarotCards.length}');
          _addLog('📖 保存的解读长度: ${reading.length}字');
          _addLog('📖 保存的解读内容: ${reading.length > 50 ? "${reading.substring(0, 50)}..." : reading}');

          // 保存到数据库
          final saveData = {
            'id': readingId,
            'question': widget.question,
            'spread_type': _getSpreadTypeString(_parseSpreadType(widget.spreadType)),
            'cards': selectedTarotCards.map((card) => {
              'id': card.id,
              'name': card.name,
              'description': card.description,
              'meaning': card.meaning,
              'keywords': card.keywords,
              'imageUrl': card.imageUrl,
              'isMajorArcana': card.isMajorArcana,
              'isReversed': card.isReversed,
            }).toList(),
            'interpretation': reading,
            'reading_type': 'chat_reading',
            'cards_drawn': selectedTarotCards.length,
          };

          _addLog('💾 准备保存的数据:');
          _addLog('   ID: ${saveData['id']}');
          _addLog('   问题: ${saveData['question']}');
          _addLog('   卡牌数量: ${(saveData['cards'] as List).length}');
          _addLog('   解读内容: ${saveData['interpretation']}');

          _addLog('🔄 开始调用saveTarotReading...');
          await supabaseService.saveTarotReading(saveData);
          _addLog('✅ saveTarotReading调用成功');

          // 立即更新本地状态
          if (mounted) {
            final appState = Provider.of<AppStateProvider>(context, listen: false);
            appState.addReadingToHistory(newReading);
            _addLog('✅ 本地状态更新成功');
          }

          _addLog('✅ 解读历史保存成功并更新本地状态');
        } catch (e) {
          print('❌ 保存解读历史失败: $e');
          print('📊 错误详情: ${e.toString()}');
        }
      }
    } catch (e) {
      print('⚠️ AI解读生成失败: $e');
      if (mounted) {
        setState(() {
          if (_messages.isNotEmpty && !_messages.last.isUser) {
            _messages.removeLast();
          }
          _isTyping = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(languageManager.translate('ai_reading_generation_failed'));
              },
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 辅助方法：解析牌阵类型
  SpreadType _parseSpreadType(String spreadType) {
    switch (spreadType) {
      case '单张牌':
      case '单张牌阵':
        return SpreadType.single;
      case '三张牌':
      case '三张牌阵（经典）':
        return SpreadType.three;
      case '凯尔特十字':
      case '凯尔特十字牌阵':
        return SpreadType.celtic;
      default:
        return SpreadType.single;
    }
  }

  // 辅助方法：获取牌阵类型字符串
  String _getSpreadTypeString(SpreadType spreadType) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (spreadType) {
      case SpreadType.single:
        return languageManager.translate('single_card');
      case SpreadType.three:
        return languageManager.translate('three_card_classic');
      case SpreadType.celtic:
        return languageManager.translate('celtic_cross');
      default:
        return languageManager.translate('single_card');
    }
  }

  // 获取用户消息历史
  List<String> _getUserMessageHistory() {
    return _messages
        .where((message) => message.isUser)
        .map((message) => message.text)
        .toList();
  }

  String _generateTarotReading() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    // 这里可以集成真正的AI API
    final cardText = widget.selectedCards.length == 1
        ? languageManager.translate('this_card')
        : languageManager.translate('these_cards').replaceAll('{count}', widget.selectedCards.length.toString());

    return '''${languageManager.translate('based_on_question_and_cards').replaceAll('{question}', widget.question).replaceAll('{cards}', cardText)}

🔮 **${languageManager.translate('current_situation')}**
${languageManager.translate('current_situation_reading').replaceAll('{cards}', cardText)}

✨ **${languageManager.translate('guidance_advice')}**
${languageManager.translate('guidance_advice_reading')}

🌟 **${languageManager.translate('future_outlook')}**
${languageManager.translate('future_outlook_reading')}

💫 **${languageManager.translate('tarot_advice')}**
${_getSpecificAdvice()}

${languageManager.translate('any_further_questions')}''';
  }

  String _getSpecificAdvice() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (widget.spreadType) {
      case '单张牌':
        return languageManager.translate('single_card_advice');
      case '三张牌':
        return languageManager.translate('three_card_advice');
      case '凯尔特十字':
        return languageManager.translate('celtic_cross_advice');
      default:
        return languageManager.translate('general_spread_advice');
    }
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    final message = _messageController.text.trim();

    // 🔒 关键修复：在发送消息前检查订阅状态
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    _addLog('🔍 发送消息前检查订阅状态...');
    _addLog('🔍 canUseToday: ${subscriptionService.canUseToday}');
    _addLog('🔍 当前等级: ${subscriptionService.currentTier}');

    // 如果用户没有使用权限，阻止发送消息并显示订阅提示
    if (!subscriptionService.canUseToday) {
      _addLog('❌ 用户无使用权限，阻止发送消息');
      setState(() {
        _hasUsagePermission = false; // 🔒 更新权限状态
      });
      _messageController.clear(); // 清空输入框
      _showSubscriptionPrompt(subscriptionService);
      return; // 🔒 关键：直接返回，不添加用户消息
    }

    // 🔒 确保权限状态是最新的
    setState(() {
      _hasUsagePermission = true;
    });

    // 只有有权限的用户才能发送消息
    setState(() {
      _messages.add(ChatMessage(
        text: message,
        isUser: true,
        timestamp: DateTime.now(),
      ));
    });

    _addLog('👤 用户发送消息，当前消息总数: ${_messages.length}');

    _messageController.clear();
    _scrollToBottom();

    // 对于后续问题，也需要检查付费状态
    _checkSubscriptionAndAddFollowUp(message);
  }

  // 新增：添加自动追问
  void _addAutomaticFollowUpQuestion(String userLanguage) {
    // 根据用户选择的语言生成不同的追问
    String followUpQuestion;

    switch (userLanguage) {
      case 'zh':
        followUpQuestion = '亲爱的，我想更深入地了解你的情况...这个问题对你来说最困扰的是什么方面呢？';
        break;
      case 'ja':
        followUpQuestion = 'あなたの状況をもっと深く理解したいの...この問題で一番困っているのはどの部分？';
        break;
      case 'ko':
        followUpQuestion = '자기야, 상황을 더 깊이 이해하고 싶어...이 문제에서 가장 힘든 부분이 뭐야?';
        break;
      case 'fr':
        followUpQuestion = 'Chérie, j\'aimerais mieux comprendre ta situation... Quel aspect de ce problème te préoccupe le plus?';
        break;
      case 'es':
        followUpQuestion = 'Cariño, me gustaría entender mejor tu situación... ¿Qué aspecto de este problema te preocupa más?';
        break;
      case 'en':
      default:
        followUpQuestion = 'Honey, I\'d love to understand your situation better... What aspect of this question troubles you the most?';
        break;
    }

    setState(() {
      _messages.add(ChatMessage(
        text: followUpQuestion,
        isUser: false,
        timestamp: DateTime.now(),
        isFollowUpQuestion: true, // 标记为追问
      ));
    });

    _scrollToBottom();
  }

  // 修复：检查订阅状态并添加后续回复
  void _checkSubscriptionAndAddFollowUp(String userMessage) async {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    _addLog('🔍 检查订阅状态，canUseToday: ${subscriptionService.canUseToday}');

    // 所有用户的追问都需要检查次数限制
    if (!subscriptionService.canUseToday) {
      _addLog('❌ 订阅检查失败，显示订阅提示');
      _showSubscriptionPrompt(subscriptionService);
      return;
    }

    _addLog('✅ 订阅检查通过，开始AI追问');
    // 追问不消耗AI解读次数，直接进行回复
    _addFollowUpResponse(userMessage);
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && _scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }



  void _addFollowUpResponse(String userMessage) async {
    if (!mounted) return;

    _addLog('🔄 开始处理追问: $userMessage');

    setState(() {
      // 不设置 _isTyping = true，避免双重加载指示器
      // 只添加我们自己的加载消息（使用语言管理器）
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      _messages.add(ChatMessage(
        text: languageManager.translate('ai_reading_in_progress'),
        isUser: false,
        timestamp: DateTime.now(),
        isTyping: true,
      ));
    });

    // 记录加载消息的索引（在添加后）
    final loadingMessageIndex = _messages.length - 1;
    _addLog('📍 加载消息索引: $loadingMessageIndex');
    _addLog('💬 添加加载消息，当前消息总数: ${_messages.length}');
    _scrollToBottom();

    try {
      // 获取选中的塔罗牌
      final selectedTarotCards = widget.selectedCards;

      // 获取之前的AI解读作为上下文（排除加载消息和卡牌显示消息）
      final previousReading = _messages
          .where((msg) => !msg.isUser &&
                         !msg.text.contains('🔮') && // 排除加载消息
                         !msg.text.contains('您抽取的塔罗牌是') && // 排除卡牌显示
                         msg.text.length > 50) // 只要长的解读内容
          .map((msg) => msg.text)
          .join('\n\n');

      _addLog('📖 获取到的历史解读长度: ${previousReading.length}字');
      _addLog('📖 历史解读预览: ${previousReading.length > 100 ? "${previousReading.substring(0, 100)}..." : previousReading}');

      // 移除空消息添加逻辑，使用我们已有的加载消息机制
      // 不再添加额外的空消息，避免空白气泡

      // selectedTarotCards已经在第561行定义了
      // 如果没有卡牌，使用随机卡牌作为fallback
      if (selectedTarotCards.isEmpty) {
        selectedTarotCards.addAll(TarotCardsData.getRandomCards(1));
      }

      // 获取用户在个人页面设置的语言（而不是设备语言）
      String userLanguage = 'en'; // 默认英文
      try {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        final currentLanguageCode = languageManager.currentLanguage;

        // 根据用户在个人页面设置的语言来决定AI解读语言
        switch (currentLanguageCode) {
          case 'zh-CN':
          case 'zh-TW':
            userLanguage = 'zh';
            break;
          case 'ja-JP':
            userLanguage = 'ja';
            break;
          case 'ko-KR':
            userLanguage = 'ko';
            break;
          case 'es-ES':
            userLanguage = 'es';
            break;
          case 'en-US':
          default:
            userLanguage = 'en';
            break;
        }

        print('🔍 追问语言调试信息:');
        print('  - 用户设置的语言: $currentLanguageCode');
        print('  - 转换后的AI语言: $userLanguage');
        print('  - 语言来源: LanguageManager (个人页面设置)');
      } catch (e) {
        print('无法获取语言设置，使用默认英文: $e');
      }
      final conversationHistory = _getUserMessageHistory();

      _addLog('💬 追问解读 - 用户消息: $userMessage');
      _addLog('💬 追问解读 - 卡牌: ${selectedTarotCards.map((c) => '${c.name}${c.isReversed ? "(逆位)" : ""}').toList()}');
      _addLog('💬 对话历史: ${conversationHistory.join(" | ")}');
      _addLog('📖 传递的历史解读: ${previousReading.isEmpty ? "无" : "有(${previousReading.length}字)"}');

      // 简化版追问解读
      _addLog('🔄 调用AI追问解读服务...');
      final followUpReading = await SimpleAITarotService.getFollowUpReading(
        userMessage: userMessage,
        previousReading: previousReading,
        cards: selectedTarotCards,
        userLanguage: userLanguage,
        conversationHistory: conversationHistory,
      );

      _addLog('✅ AI追问解读成功，长度: ${followUpReading.length}字');

      // 更安全的方法：查找最后一个加载消息并更新
      _addLog('🔄 准备更新消息，原索引: $loadingMessageIndex，当前消息数: ${_messages.length}');

      if (mounted) {
        setState(() {
          // 确保 _isTyping 为 false
          _isTyping = false;

          // 查找最后一个isTyping=true的消息
          int actualLoadingIndex = -1;
          for (int i = _messages.length - 1; i >= 0; i--) {
            if (_messages[i].isTyping == true) {
              actualLoadingIndex = i;
              break;
            }
          }

          if (actualLoadingIndex != -1) {
            _addLog('🔍 找到加载消息，实际索引: $actualLoadingIndex');
            _addLog('🔍 更新前消息内容: ${_messages[actualLoadingIndex].text}');

            // 更新加载消息为AI回复
            _messages[actualLoadingIndex] = ChatMessage(
              text: followUpReading,
              isUser: false,
              timestamp: _messages[actualLoadingIndex].timestamp,
            );

            _addLog('✅ 消息更新完成，当前消息数: ${_messages.length}');
            _addLog('🔍 更新后消息内容: ${followUpReading.length > 50 ? followUpReading.substring(0, 50) : followUpReading}...');
          } else {
            _addLog('❌ 未找到加载消息，直接添加新消息');
            // 如果找不到加载消息，直接添加新消息
            _messages.add(ChatMessage(
              text: followUpReading,
              isUser: false,
              timestamp: DateTime.now(),
            ));
          }
        });

        // 检查所有消息的详细信息
        _addLog('📋 所有消息详情:');
        for (int i = 0; i < _messages.length; i++) {
          final msg = _messages[i];
          final preview = msg.text.length > 30 ? '${msg.text.substring(0, 30)}...' : msg.text;
          _addLog('📋 消息$i: ${msg.isUser ? '👤用户' : '🤖AI'} - "$preview" (isTyping: ${msg.isTyping})');
        }

        final aiMessages = _messages.where((msg) => !msg.isUser).toList();
        _addLog('🤖 当前AI消息总数: ${aiMessages.length}');

        _scrollToBottom();
        _addLog('💾 准备保存追问对话到后端...');

        // 真正保存追问对话到后端
        _saveFollowUpToBackend(userMessage, followUpReading);
      }
    } catch (e) {
      _addLog('❌ AI追问解读失败: $e');
      // 如果AI API调用失败，使用简单回复
      if (mounted) {
        setState(() {
          _isTyping = false;
          final languageManager = Provider.of<LanguageManager>(context, listen: false);

          // 查找最后一个isTyping=true的消息
          int actualLoadingIndex = -1;
          for (int i = _messages.length - 1; i >= 0; i--) {
            if (_messages[i].isTyping == true) {
              actualLoadingIndex = i;
              break;
            }
          }

          if (actualLoadingIndex != -1) {
            // 更新加载消息为错误回复
            _messages[actualLoadingIndex] = ChatMessage(
              text: languageManager.translate('followup_response_fallback'),
              isUser: false,
              timestamp: _messages[actualLoadingIndex].timestamp,
            );
          } else {
            // 如果找不到加载消息，直接添加错误回复
            _messages.add(ChatMessage(
              text: languageManager.translate('followup_response_fallback'),
              isUser: false,
              timestamp: DateTime.now(),
            ));
          }
        });
        _scrollToBottom();
        _addLog('🔄 使用备用回复替代');
      }
    }
  }

  // 保存追问对话到后端
  void _saveFollowUpToBackend(String question, String response) async {
    try {
      _addLog('🔄 开始保存追问到数据库...');

      // 获取 AppStateProvider
      final appState = Provider.of<AppStateProvider>(context, listen: false);

      // 检查用户登录状态
      final supabaseService = SupabaseDataService();
      final isAuthenticated = supabaseService.isAuthenticated;
      _addLog('🔐 用户登录状态: $isAuthenticated');

      if (!isAuthenticated) {
        _addLog('❌ 用户未登录，无法保存到后端');
        return;
      }

      // 查找或创建解读记录
      _addLog('🔄 查找现有解读记录...');

      // 尝试从历史记录中找到匹配的解读记录
      String? existingReadingId = await _findExistingReading();

      if (existingReadingId != null) {
        _addLog('📋 找到现有解读记录: $existingReadingId');
        // 更新现有记录的追问对话
        await _updateExistingReading(existingReadingId, question, response);
      } else {
        _addLog('📋 未找到现有记录，创建新的解读记录');

        // 获取之前的AI解读作为初始解读内容
        final initialReading = _messages
            .where((msg) => !msg.isUser &&
                           !msg.text.contains('🔮') && // 排除加载消息
                           !msg.text.contains('您抽取的塔罗牌是') && // 排除卡牌显示
                           msg.text.length > 50) // 只要长的解读内容
            .map((msg) => msg.text)
            .join('\n\n');

        _addLog('📖 获取到的初始解读长度: ${initialReading.length}字');

        // 创建新的解读记录，传入真实的初始解读内容
        await _createNewReading(question, response, initialInterpretation: initialReading);
      }

      // 刷新历史记录显示
      _addLog('🔄 刷新历史记录显示...');
      final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
      await appStateProvider.refreshReadingHistory();
      _addLog('✅ 历史记录刷新完成');

      // 等待一下让保存操作完成
      await Future.delayed(const Duration(milliseconds: 500));

      _addLog('✅ 追问对话保存成功');

    } catch (e) {
      _addLog('❌ 追问对话保存失败: $e');
      print('❌ 保存追问对话到后端失败: $e');
    }
  }

  // 查找现有的解读记录
  Future<String?> _findExistingReading() async {
    try {
      final supabaseService = SupabaseDataService();

      // 根据问题和卡牌查找最近的解读记录
      final readings = await supabaseService.getTarotReadings();

      // 查找匹配当前问题和卡牌的记录（最近24小时内）
      final now = DateTime.now();
      for (final reading in readings) {
        final readingTime = DateTime.parse(reading['created_at']);
        final timeDiff = now.difference(readingTime).inHours;

        // 如果是24小时内的记录，且问题匹配
        if (timeDiff <= 24 && reading['question'] == widget.question) {
          _addLog('🔍 找到匹配的解读记录，时间差: $timeDiff小时');
          return reading['id'];
        }
      }

      return null;
    } catch (e) {
      _addLog('❌ 查找现有解读记录失败: $e');
      return null;
    }
  }

  // 更新现有解读记录
  Future<void> _updateExistingReading(String readingId, String question, String response) async {
    try {
      final supabaseService = SupabaseDataService();

      // 获取现有记录
      final readings = await supabaseService.getTarotReadings();
      final existingData = readings.firstWhere(
        (reading) => reading['id'] == readingId,
        orElse: () => {},
      );

      if (existingData.isEmpty) {
        _addLog('❌ 无法获取现有解读记录');
        return;
      }

      // 更新追问数组
      final existingQuestions = List<String>.from(existingData['follow_up_questions'] ?? []);
      final existingResponses = List<String>.from(existingData['follow_up_responses'] ?? []);

      existingQuestions.add(question);
      existingResponses.add(response);

      _addLog('📝 更新追问数组，新增问题: $question');
      _addLog('📝 当前追问总数: ${existingQuestions.length}');

      // 更新记录
      await supabaseService.updateTarotReading(readingId, {
        'follow_up_questions': existingQuestions,
        'follow_up_responses': existingResponses,
      });

      _addLog('✅ 现有解读记录更新成功');
    } catch (e) {
      _addLog('❌ 更新现有解读记录失败: $e');
    }
  }

  // 创建新的解读记录
  Future<void> _createNewReading(String question, String response, {String? initialInterpretation}) async {
    try {
      final selectedTarotCards = widget.selectedCards;

      const uuid = Uuid();
      final readingId = uuid.v4();

      // 构建保存数据
      final readingData = {
        'id': readingId,
        'question': widget.question,
        'spread_type': 'three',
        'cards': selectedTarotCards.map((card) => {
          'id': card.id,
          'name': card.name,
          'isReversed': card.isReversed,
        }).toList(),
        'interpretation': initialInterpretation ?? '初始解读内容暂未保存',
        'follow_up_questions': [question],
        'follow_up_responses': [response],
        'cards_drawn': selectedTarotCards.length,
        'reading_type': 'chat',
        'ai_model': 'deepseek',
      };

      final supabaseService = SupabaseDataService();
      await supabaseService.saveTarotReading(readingData);

      _addLog('✅ 新解读记录创建成功: $readingId');
    } catch (e) {
      _addLog('❌ 创建新解读记录失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      body: Stack(
        children: [
          // 🖼️ 吉祥物图片背景层 - 与主页一致
          Positioned.fill(
            child: Container(
              color: Colors.white, // 白色底色作为衔接
              child: Image.asset(
                'assets/images/tarot_mascot.png',
                fit: BoxFit.cover, // 撑大覆盖整个屏幕
                errorBuilder: (context, error, stackTrace) {
                  // 如果图片加载失败，回退到原来的渐变背景
                  return Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment(-1.0, -1.0),
                        end: Alignment(1.0, 1.0),
                        stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                        colors: [
                          Color(0xFF87CEEB),
                          Color(0xFFE6E6FA),
                          Color(0xFFF8BBD9),
                          Color(0xFFE6E6FA),
                          Color(0xFF87CEEB),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // ☁️ 云朵背景图片层 - 重叠在吉祥物背景上
          Positioned.fill(
            child: Image.asset(
              'assets/images/cloud_background.png',
              fit: BoxFit.cover,
              opacity: const AlwaysStoppedAnimation(0.4), // 更透明一些，不影响聊天内容阅读
              errorBuilder: (context, error, stackTrace) {
                // 如果云朵图片加载失败，显示透明容器
                return Container(color: Colors.transparent);
              },
            ),
          ),

          // ☁️ 云朵装饰
          Positioned.fill(
            child: Stack(
              children: [
                Positioned(
                  top: 100,
                  left: 30,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.2),
                          blurRadius: 6,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 200,
                  right: 50,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.25),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.15),
                          blurRadius: 5,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 主要内容
          SafeArea(
            child: Column(
              children: [
                // 🍎 苹果风格顶部导航栏
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  decoration: FigmaTheme.createGlassDecoration(
                    opacity: 0.9,
                    radius: 0,
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: FigmaTheme.textPrimary,
                          size: 24,
                        ),
                      ),
                      Expanded(
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('tarot_reading'),
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: FigmaTheme.textPrimary,
                              ),
                              textAlign: TextAlign.center,
                            );
                          },
                        ),
                      ),

                    ],
                  ),
                ),

                // 聊天消息区域
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 24,
                    ),
                    child: Column(
                      children: [


                        // 消息列表
                        Expanded(
                          child: ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _messages.length + (_isTyping ? 1 : 0),
                            itemBuilder: (context, index) {
                              if (index == _messages.length && _isTyping) {
                                return _buildTypingIndicator();
                              }
                              return _buildMessageBubble(_messages[index]);
                            },
                          ),
                        ),

                        // 输入区域
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: FigmaTheme.surfaceWhite.withValues(alpha: 0.5),
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(24),
                              bottomRight: Radius.circular(24),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _messageController,
                                  enabled: _hasUsagePermission, // 🔒 根据权限状态禁用输入框
                                  decoration: InputDecoration(
                                    hintText: _hasUsagePermission
                                        ? Provider.of<LanguageManager>(context).translate('continue_asking')
                                        : Provider.of<LanguageManager>(context).translate('subscription_required_to_continue'), // 🔒 权限提示
                                    hintStyle: TextStyle(
                                      color: _hasUsagePermission ? FigmaTheme.textMuted : Colors.red.withValues(alpha: 0.7),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(25),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor: _hasUsagePermission
                                        ? FigmaTheme.surfaceWhite
                                        : FigmaTheme.surfaceWhite.withValues(alpha: 0.5), // 🔒 禁用时的背景色
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 12,
                                    ),
                                  ),
                                  onSubmitted: (_) => _hasUsagePermission ? _sendMessage() : null, // 🔒 权限检查
                                ),
                              ),
                              const SizedBox(width: 12),
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  gradient: _hasUsagePermission
                                      ? const LinearGradient(
                                          colors: [FigmaTheme.primaryPink, FigmaTheme.accentPurple],
                                        )
                                      : LinearGradient( // 🔒 禁用时的灰色渐变
                                          colors: [Colors.grey.withValues(alpha: 0.5), Colors.grey.withValues(alpha: 0.3)],
                                        ),
                                  borderRadius: BorderRadius.circular(24),
                                ),
                                child: IconButton(
                                  onPressed: _hasUsagePermission ? _sendMessage : null, // 🔒 权限检查
                                  icon: Icon(
                                    _hasUsagePermission ? Icons.send : Icons.lock, // 🔒 根据权限显示不同图标
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: FigmaTheme.primaryPink.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(
                  'assets/images/cloud_background.png', // 使用吉祥物作为AI头像
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // 如果图片加载失败，回退到原来的渐变图标
                    return Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [FigmaTheme.primaryPink, FigmaTheme.accentPurple],
                        ),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: 16,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: message.isUser 
                    ? FigmaTheme.primaryPink.withValues(alpha: 0.1)
                    : (message.isSubscriptionPrompt == true 
                        ? const Color(0xFF6B46C1).withValues(alpha: 0.1)
                        : FigmaTheme.surfaceWhite.withValues(alpha: 0.9)),
                borderRadius: BorderRadius.circular(16),
                border: message.isUser 
                    ? Border.all(color: FigmaTheme.primaryPink.withValues(alpha: 0.3))
                    : (message.isSubscriptionPrompt == true
                        ? Border.all(color: const Color(0xFF6B46C1).withValues(alpha: 0.3))
                        : null),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.text,
                    style: TextStyle(
                      fontSize: 16,
                      color: message.isUser 
                          ? FigmaTheme.primaryPink 
                          : (message.isSubscriptionPrompt == true 
                              ? const Color(0xFF6B46C1) 
                              : FigmaTheme.textPrimary),
                      height: 1.4,
                    ),
                  ),
                  // 显示塔罗牌图片
                  if (message.cards != null && message.cards!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildCardImages(message.cards!),
                  ],
                  // 显示升级按钮
                  if (message.isSubscriptionPrompt == true) ...[
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SubscriptionScreen(),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6B46C1),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('upgrade_now'),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ).animate().fadeIn(duration: 300.ms).slideX(
              begin: message.isUser ? 0.3 : -0.3,
              end: 0,
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: FigmaTheme.primaryPink.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.person,
                color: FigmaTheme.primaryPink,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: FigmaTheme.primaryPink.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.asset(
                'assets/images/cloud_background.png', // 使用吉祥物作为AI头像
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 如果图片加载失败，回退到原来的渐变图标
                  return Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [FigmaTheme.primaryPink, FigmaTheme.accentPurple],
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 16,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: FigmaTheme.surfaceWhite.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                for (int i = 0; i < 3; i++)
                  Container(
                    width: 8,
                    height: 8,
                    margin: EdgeInsets.only(right: i < 2 ? 4 : 0),
                    decoration: BoxDecoration(
                      color: FigmaTheme.textMuted,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ).animate(
                    onPlay: (controller) => controller.repeat(),
                  ).fadeIn(
                    delay: Duration(milliseconds: i * 200),
                    duration: 600.ms,
                  ).then().fadeOut(duration: 600.ms),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardImages(List<TarotCard> cards) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    return Column(
      children: [
        const SizedBox(height: 8),
        // 移除drawn_cards标题，只显示卡牌图片
        // 卡牌展示区域
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: cards.map((card) => Container(
            width: 100,
            height: 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // 卡牌图片
                  Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.identity()
                      ..rotateZ(card.isReversed ? 3.14159 : 0), // 逆位时旋转180度
                    child: Image.asset(
                      card.getCorrectImageUrl(),
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        print('卡牌图片加载失败: ${card.getCorrectImageUrl()}, 错误: $error');
                        return Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF8B5CF6), Color(0xFF3B82F6)],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.auto_awesome,
                                color: Colors.white,
                                size: 24,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                card.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  // 正逆位指示器
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                      decoration: BoxDecoration(
                        color: card.isReversed
                            ? Colors.red.withOpacity(0.9)
                            : Colors.green.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        card.isReversed 
                            ? languageManager.translate('reversed_short') 
                            : languageManager.translate('upright_short'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  // 卡牌名称底部标签
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                      ),
                      child: Text(
                        card.getTranslatedName(languageManager.translate),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 9,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final List<TarotCard>? cards; // 可选的塔罗牌列表
  final bool? isSubscriptionPrompt;
  final bool? isAnalysisInsight; // 是否是分析洞察
  final bool? isFollowUpQuestion; // 是否是追问
  final bool? isTyping; // 是否是加载状态
  // 移除specialist字段，简化消息结构

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.cards,
    this.isSubscriptionPrompt,
    this.isAnalysisInsight,
    this.isFollowUpQuestion,
    this.isTyping,
    // specialist字段已移除
  });
}
