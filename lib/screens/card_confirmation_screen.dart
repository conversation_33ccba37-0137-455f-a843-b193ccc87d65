import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

class CardConfirmationScreen extends StatelessWidget {
  final TarotCard selectedCard;
  final String position;
  final VoidCallback onConfirm;
  final VoidCallback onReselect;

  const CardConfirmationScreen({
    super.key,
    required this.selectedCard,
    required this.position,
    required this.onConfirm,
    required this.onReselect,
  });

  // 翻译牌位名称
  String _translatePosition(String position, LanguageManager languageManager) {
    final positionTranslationMap = {
      // 基础位置
      '当前能量/宇宙提示': 'position_current_energy',
      '过去': 'position_past',
      '现在': 'position_present',
      '未来': 'position_future',
      '当前状况': 'position_current_situation',
      '挑战': 'position_challenge',
      '建议行动': 'position_advice',
      '你': 'you',
      '对方': 'other_person',
      '你怎么看关系': 'how_you_see',
      '对方怎么看': 'how_other_sees',
      '现状': 'current_status',

      // 二选一牌阵
      '选项A现状': 'position_option_a_current',
      '选项A影响': 'position_option_a_influence',
      '选项A结果': 'position_option_a_result',
      '选项B现状': 'position_option_b_current',
      '选项B影响': 'position_option_b_influence',
      '选项B结果': 'position_option_b_result',

      // 六芒星牌阵
      '问题本质': 'position_problem_essence',
      '阻碍': 'position_obstacle',
      '机会': 'position_opportunity',
      '外部环境': 'position_external_environment',
      '最终结果': 'position_final_result',

      // 凯尔特十字牌阵
      '挑战/阻碍': 'position_challenge_obstacle',
      '遥远过去': 'position_distant_past',
      '近期过去': 'position_recent_past',
      '可能的未来': 'position_possible_future',
      '近期未来': 'position_near_future',
      '你的方法': 'position_your_approach',
      '外部影响': 'position_external_influence',
      '希望与恐惧': 'position_hopes_fears',
      '最终结果': 'position_final_outcome',

      // 事业成长和心灵疗愈
      '目前状态': 'position_current_state',
      '内在潜能': 'position_inner_potential',
      '发展建议': 'position_development_advice',
      '内在创伤': 'position_inner_trauma',
      '当下情绪': 'position_current_emotion',
      '内在需求': 'position_inner_need',
      '疗愈路径': 'position_healing_path',
      '转化结果': 'position_transformation_result',

      // 时间相关
      '第一周': 'position_week_1',
      '第二周': 'position_week_2',
      '第三周': 'position_week_3',
      '第四周': 'position_week_4',
      '周一': 'position_monday',
      '周二': 'position_tuesday',
      '周三': 'position_wednesday',
      '周四': 'position_thursday',
      '周五': 'position_friday',
      '周六': 'position_saturday',
      '周日': 'position_sunday',

      // 月份
      '1月': 'position_january',
      '2月': 'position_february',
      '3月': 'position_march',
      '4月': 'position_april',
      '5月': 'position_may',
      '6月': 'position_june',
      '7月': 'position_july',
      '8月': 'position_august',
      '9月': 'position_september',
      '10月': 'position_october',
      '11月': 'position_november',
      '12月': 'position_december',

      // 新月许愿牌阵
      '目标能量': 'position_goal_energy',
      '宇宙指引': 'position_cosmic_guidance',
      '行动建议': 'position_action_advice',
      '可能结果': 'position_possible_result',
    };

    final translationKey = positionTranslationMap[position];
    if (translationKey != null) {
      return languageManager.translate(translationKey);
    }
    return position; // 如果没有找到翻译，返回原文
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 恢复原有的渐变背景
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0, -1.0),
                  end: Alignment(1.0, 1.0),
                  colors: [
                    Color(0xFF87CEEB),
                    Color(0xFFE6E6FA),
                    Color(0xFFF8BBD9),
                    Color(0xFFE6E6FA),
                    Color(0xFF87CEEB),
                  ],
                ),
              ),
            ),
          ),

          // 主要内容
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏 - 透明毛玻璃效果
                Container(
                  height: 60,
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Row(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                            const Spacer(),
                            Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Text(
                                    _translatePosition(position, languageManager),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                );
                              },
                            ),
                            const Spacer(),
                            const SizedBox(width: 48),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // 卡牌显示区域
                Expanded(
                  child: Center(
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // 自适应计算卡牌大小
                        final maxWidth = constraints.maxWidth * 0.7; // 使用70%的可用宽度
                        final maxHeight = constraints.maxHeight * 0.6; // 使用60%的可用高度

                        // 保持塔罗牌的宽高比 (约2:3)
                        const cardAspectRatio = 2.0 / 3.0;

                        double cardWidth, cardHeight;

                        if (maxWidth / maxHeight > cardAspectRatio) {
                          // 高度限制更严格
                          cardHeight = maxHeight;
                          cardWidth = cardHeight * cardAspectRatio;
                        } else {
                          // 宽度限制更严格
                          cardWidth = maxWidth;
                          cardHeight = cardWidth / cardAspectRatio;
                        }

                        // 确保最小尺寸
                        cardWidth = cardWidth.clamp(200.0, 300.0);
                        cardHeight = cardHeight.clamp(300.0, 450.0);

                        return Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 卡牌图片 - 添加毛玻璃效果
                            Container(
                              width: cardWidth,
                              height: cardHeight,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 30,
                                    offset: const Offset(0, 15),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(20),
                                child: Stack(
                                  children: [
                                    // 卡牌图片（支持正位/逆位旋转）
                                    Transform.rotate(
                                      angle: selectedCard.isReversed ? 3.14159 : 0, // 逆位时旋转180度
                                      child: Image.asset(
                                        selectedCard.getCorrectImageUrl(),
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Container(
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(20),
                                              gradient: const LinearGradient(
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                                colors: [
                                                  Color(0xFF6B46C1),
                                                  Color(0xFF9333EA),
                                                ],
                                              ),
                                            ),
                                            child: const Center(
                                              child: Icon(
                                                Icons.auto_awesome,
                                                size: 80,
                                                color: Colors.white,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),

                                    // 毛玻璃覆盖层
                                    Positioned.fill(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(20),
                                          color: Colors.white.withOpacity(0.1),
                                          border: Border.all(
                                            color: Colors.white.withOpacity(0.3),
                                            width: 1,
                                          ),
                                        ),
                                        child: BackdropFilter(
                                          filter: ImageFilter.blur(sigmaX: 0.5, sigmaY: 0.5),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(20),
                                              color: Colors.transparent,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),

                                    // 正位/逆位标识
                                    Positioned(
                                      top: 12,
                                      right: 12,
                                      child: Consumer<LanguageManager>(
                                        builder: (context, languageManager, child) {
                                          return Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: selectedCard.isReversed
                                                  ? Colors.red.withValues(alpha: 0.8)
                                                  : Colors.green.withValues(alpha: 0.8),
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              selectedCard.isReversed
                                                  ? languageManager.translate('reversed_position')
                                                  : languageManager.translate('upright_position'),
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ).animate().scale(
                              duration: 600.ms,
                              curve: Curves.elasticOut,
                              begin: const Offset(0.8, 0.8),
                              end: const Offset(1.0, 1.0),
                            ).fadeIn(duration: 400.ms),

                            const SizedBox(height: 32),

                            // 卡牌名称
                            Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return Text(
                                  selectedCard.getTranslatedName(languageManager.translate),
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.w700,
                                    color: Color(0xFF2D3748),
                                  ),
                                  textAlign: TextAlign.center,
                                );
                              },
                            ).animate().fadeIn(delay: 300.ms, duration: 500.ms),

                            const SizedBox(height: 16),

                            // 卡牌描述
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 40),
                              child: Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    selectedCard.getTranslatedMeaning(languageManager.translate),
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                      height: 1.5,
                                    ),
                                    textAlign: TextAlign.center,
                                  );
                                },
                              ),
                            ).animate().fadeIn(delay: 500.ms, duration: 500.ms),
                          ],
                        );
                      },
                    ),
                  ),
                ),

                // 底部按钮
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      // 确定按钮
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: onConfirm,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF8B5CF6),
                            foregroundColor: Colors.white,
                            elevation: 8,
                            shadowColor: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return Text(
                                languageManager.translate('confirm'),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              );
                            },
                          ),
                        ),
                      ).animate().fadeIn(delay: 700.ms, duration: 500.ms),

                      const SizedBox(height: 16),

                      // 换一张按钮
                      TextButton(
                        onPressed: onReselect,
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('change_card'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF6B7280),
                              ),
                            );
                          },
                        ),
                      ).animate().fadeIn(delay: 800.ms, duration: 500.ms),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
