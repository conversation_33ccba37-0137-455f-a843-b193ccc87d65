
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/subscription_service.dart';
import '../utils/language_manager.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  SubscriptionPeriod _selectedPeriod = SubscriptionPeriod.weekly;
  SubscriptionTier _selectedTier = SubscriptionTier.premium; // 默认选中高级会员

  @override
  void initState() {
    super.initState();
    // 监听订阅状态变化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkSubscriptionStatus();
    });
  }
  
  void _checkSubscriptionStatus() {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
    
    // 如果订阅成功，显示成功消息并关闭页面
    if (subscriptionService.isSubscribed && subscriptionService.errorMessage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🎉 订阅成功！欢迎成为会员'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
      
      // 延迟关闭页面
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          Navigator.pop(context);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🔍 SubscriptionScreen build() called');

    // 确保如果选中了基础会员，自动切换到高级会员
    if (_selectedTier == SubscriptionTier.basic) {
      _selectedTier = SubscriptionTier.premium;
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      body: Stack(
        children: [
          // 🖼️ 吉祥物图片背景层 - 与历史回溯界面一致
          Positioned.fill(
            child: Container(
              // 透明毛玻璃底色
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                backgroundBlendMode: BlendMode.overlay,
              ),
              child: Image.asset(
                'assets/images/tarot_mascot.png',
                fit: BoxFit.cover, // 撑大覆盖整个屏幕
                errorBuilder: (context, error, stackTrace) {
                  // 如果图片加载失败，回退到原来的渐变背景
                  return Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment(-1.0, -1.0), // 135deg
                        end: Alignment(1.0, 1.0),
                        stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                        colors: [
                          Color(0xFF87CEEB), // #87CEEB 0%
                          Color(0xFFE6E6FA), // #E6E6FA 25%
                          Color(0xFFF8BBD9), // #F8BBD9 50%
                          Color(0xFFE6E6FA), // #E6E6FA 75%
                          Color(0xFF87CEEB), // #87CEEB 100%
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // 主要内容层
          SafeArea(
            child: Column(
          children: [
            // 🍎 现代化顶部导航栏 - 使用液态玻璃效果
            Container(
              height: 60,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(23),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.black, size: 24),
                    onPressed: () {
                      print('🔍 Close button pressed');
                      Navigator.pop(context);
                    },
                  ),
                  Expanded(
                    child: Consumer<LanguageManager>(
                      builder: (context, languageManager, child) {
                        return Text(
                          languageManager.translate('subscription_plans'),
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 48), // 平衡关闭按钮的空间
                ],
              ),
            ),

            // 主要内容
            Expanded(
              child: Consumer<SubscriptionService>(
                builder: (context, subscriptionService, child) {
                  print('🔍 SubscriptionService Consumer builder called');
                  print('🔍 isSubscribed: ${subscriptionService.isSubscribed}');
                  // 🔧 修复：所有用户都显示完整的订阅界面
                  // 已订阅用户可以看到当前状态和其他选项
                  print('🔍 Building full subscription view for all users');
                  return _buildSubscriptionView(context, subscriptionService);
                },
              ),
            ),
          ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscribedView(SubscriptionService subscriptionService) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        // 根据当前会员等级显示相应的权益描述
        String benefitsText;
        String titleText;

        if (subscriptionService.currentTier == SubscriptionTier.premium) {
          titleText = languageManager.translate('premium_member_benefits');
          benefitsText = languageManager.translate('premium_membership_benefits');
        } else if (subscriptionService.currentTier == SubscriptionTier.basic) {
          titleText = languageManager.translate('basic_member_benefits');
          benefitsText = languageManager.translate('basic_membership_benefits');
        } else {
          titleText = languageManager.translate('free_member');
          benefitsText = languageManager.translate('free_membership_benefits');
        }

        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.auto_awesome,
                color: Colors.black,
                size: 80,
              ),
              const SizedBox(height: 20),
              Text(
                titleText,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  // 透明毛玻璃效果
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  benefitsText,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSubscriptionView(BuildContext context, SubscriptionService subscriptionService) {
    print('🔍 _buildSubscriptionView called');
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // 🌫️ 大型毛玻璃容器 - 包含标题、周期选择器和会员卡片
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    // 高斯模糊毛玻璃效果 - 类似个人页面
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(23),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 25,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 标题区域
                      _buildHeaderSection(),

                      const SizedBox(height: 32),

                      // 周期选择器
                      _buildPeriodSelector(),

                      const SizedBox(height: 32),

                      // 会员卡片
                      _buildMembershipCards(subscriptionService),
                    ],
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),

        // 底部购买按钮
        _buildBottomSection(subscriptionService),
      ],
    );
  }

  Widget _buildHeaderSection() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Column(
          children: [
            // 移除图标，直接显示标题

            // 主标题
            Text(
              languageManager.translate('unlock_ai_tarot_reading'),
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // 副标题 - 更新为更符合产品定位的描述
            Text(
              languageManager.translate('get_professional_ai_reading'),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPeriodSelector() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Container(
          decoration: BoxDecoration(
            // 简化样式 - 在大毛玻璃容器内
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.4),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(4),
          child: Row(
            children: [
              _buildPeriodTab(languageManager.translate('weekly'), SubscriptionPeriod.weekly),
              _buildPeriodTab(languageManager.translate('monthly'), SubscriptionPeriod.monthly),
              _buildPeriodTab(languageManager.translate('annually'), SubscriptionPeriod.yearly),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPeriodTab(String title, SubscriptionPeriod period) {
    final isSelected = _selectedPeriod == period;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPeriod = period;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            // 选中时使用黄色填充，未选中时透明
            color: isSelected
                ? const Color(0xFFFFD700) // 金黄色填充
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: isSelected
                ? Border.all(
                    color: const Color(0xFFFFD700),
                    width: 1,
                  )
                : Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: const Color(0xFFFFD700).withValues(alpha: 0.4),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 5,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : null,
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              // 选中时白色字体，未选中时黑色字体
              color: isSelected ? Colors.white : Colors.black,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              fontSize: 16,
              // 添加阴影提高可读性
              shadows: isSelected ? [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  offset: const Offset(0, 1),
                  blurRadius: 2,
                ),
              ] : null,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMembershipCards(SubscriptionService subscriptionService) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        print('🔍 Building membership cards');
        final freeFeatures = languageManager.translate('free_features');
        final basicFeatures = languageManager.translate('basic_features');
        final proFeatures = languageManager.translate('pro_features');
        print('🔍 Free features: $freeFeatures');
        print('🔍 Basic features: $basicFeatures');
        print('🔍 Pro features: $proFeatures');

        // 使用最新的会员权益描述
        final actualFreeFeatures = freeFeatures.isNotEmpty ? freeFeatures : languageManager.translate('free_membership_benefits');
        final actualBasicFeatures = basicFeatures.isNotEmpty ? basicFeatures : languageManager.translate('basic_membership_benefits');
        final actualProFeatures = proFeatures.isNotEmpty ? proFeatures : languageManager.translate('premium_membership_benefits');

        // 简化会员卡片容器 - 现在在大毛玻璃容器内
        return Column(
          children: [
              // Free Member Card
              _buildMembershipCard(
                subscriptionService,
                SubscriptionTier.free,
                languageManager.translate('free_member'),
                actualFreeFeatures,
                languageManager.translate('free_price'),
                '',
                isSelected: false,
                isPopular: false,
                isFree: true,
              ),

              const SizedBox(height: 16),

              // Basic Member Card - Hidden in new tier structure
              // Kept for compatibility, basic users are automatically upgraded to premium
              if (false) // Hide basic member option
              _buildMembershipCard(
                subscriptionService,
                SubscriptionTier.basic,
                languageManager.translate('basic_member'),
                actualBasicFeatures,
                subscriptionService.getPrice(SubscriptionTier.basic, _selectedPeriod),
                _calculateDailyPrice(SubscriptionTier.basic),
                isSelected: _selectedTier == SubscriptionTier.basic,
                isPopular: false,
              ),

              // Spacing adjustment for two-tier layout
              const SizedBox(height: 16),

              // Pro Member Card
              _buildMembershipCard(
                subscriptionService,
                SubscriptionTier.premium,
                languageManager.translate('pro_member'),
                actualProFeatures,
                subscriptionService.getPrice(SubscriptionTier.premium, _selectedPeriod),
                _calculateDailyPrice(SubscriptionTier.premium),
                isSelected: _selectedTier == SubscriptionTier.premium,
                isPopular: true,
              ),
            ],
        );
      },
    );
  }

  Widget _buildMembershipCard(
    SubscriptionService subscriptionService,
    SubscriptionTier tier,
    String title,
    String description,
    String price,
    String dailyPrice,
    {
    required bool isSelected,
    required bool isPopular,
    bool isFree = false,
  }) {
    return GestureDetector(
      onTap: isFree ? null : () {
        setState(() {
          _selectedTier = tier;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          // 简化样式 - 在大毛玻璃容器内
          color: Colors.white.withValues(alpha: 0.25),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? const Color(0xFFFFD700)
                : Colors.white.withValues(alpha: 0.4),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            // 选中时添加金色光晕
            BoxShadow(
              color: const Color(0xFFFFD700).withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 0),
            ),
          ] : null,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            Row(
              children: [
                // 选择圆圈 - 免费会员不显示
                if (!isFree)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFFD700) : Colors.grey.shade300,
                        width: 2,
                      ),
                      color: isSelected ? const Color(0xFFFFD700) : Colors.transparent,
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 16)
                        : null,
                  ),

                if (!isFree) const SizedBox(width: 12),

                // 标题
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      if (isPopular)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return Text(
                                  languageManager.translate('most_popular'),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // 价格
                Text(
                  price,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isFree ? Colors.green : Colors.black,
                  ),
                ),

                if (!isFree)
                  Text(
                    ' /${_getPeriodText()}',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 12),

            // 功能列表
            _buildFeatureList(description),

            const SizedBox(height: 8),

            // 每日价格 - 仅付费会员显示
            if (!isFree && dailyPrice.isNotEmpty)
              Text(
                dailyPrice,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
              ],
            ),

            // 🔧 将绿色"current"标签移到右下角
            if (subscriptionService.isSubscribed &&
                subscriptionService.currentTier == tier &&
                subscriptionService.currentPeriod == _selectedPeriod)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('current_member'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureList(String features) {
    final featureList = features.split('\n');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: featureList.map((feature) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Color(0xFFFFD700),
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                feature,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  String _getPeriodText() {
    switch (_selectedPeriod) {
      case SubscriptionPeriod.weekly:
        return 'week';
      case SubscriptionPeriod.monthly:
        return 'month';
      case SubscriptionPeriod.yearly:
        return 'year';
    }
  }

  String _calculateDailyPrice(SubscriptionTier tier) {
    // 简单的每日价格计算
    switch (tier) {
      case SubscriptionTier.basic:
        switch (_selectedPeriod) {
          case SubscriptionPeriod.weekly:
            return 'About \$0.43/day';
          case SubscriptionPeriod.monthly:
            return 'About \$0.33/day';
          case SubscriptionPeriod.yearly:
            return 'About \$0.25/day';
        }
      case SubscriptionTier.premium:
        switch (_selectedPeriod) {
          case SubscriptionPeriod.weekly:
            return 'About \$0.71/day';
          case SubscriptionPeriod.monthly:
            return 'About \$0.60/day';
          case SubscriptionPeriod.yearly:
            return 'About \$0.41/day';
        }
      case SubscriptionTier.free:
        return '';
    }
  }

  Widget _buildBottomSection(SubscriptionService subscriptionService) {
    return Container(
      // 透明毛玻璃效果背景
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 25,
            offset: const Offset(0, -8),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Container(
        // 内层透明毛玻璃卡片
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.4),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
            BoxShadow(
              color: Colors.white.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 透明金色购买按钮
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFFFFD700), // 金色
                    Color(0xFFFFA500), // 橙金色
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () async {
                  print('🔍 Purchase button pressed for tier: $_selectedTier, period: $_selectedPeriod');

                  // 🔧 修复：如果用户已订阅且选择的是当前方案和周期，不执行任何操作
                  if (subscriptionService.isSubscribed &&
                      _selectedTier == subscriptionService.currentTier &&
                      _selectedPeriod == subscriptionService.currentPeriod) {
                    return;
                  }

                  if (_selectedTier != SubscriptionTier.free) {
                    try {
                      // 🔧 修复：检查是否是订阅切换场景
                      final wasSubscribed = subscriptionService.isSubscribed;
                      final currentTier = subscriptionService.currentTier;
                      final currentPeriod = subscriptionService.currentPeriod;

                      // 检查是否是切换到不同的订阅方案
                      final isChangingSubscription = wasSubscribed &&
                          (currentTier != _selectedTier || currentPeriod != _selectedPeriod);

                      if (isChangingSubscription) {
                        // 🚨 订阅切换需要特殊处理
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                              title: const Text('切换订阅'),
                              content: const Text('切换订阅需要重新支付，Apple会自动处理剩余天数。是否继续？'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('取消'),
                                ),
                                TextButton(
                                  onPressed: () async {
                                    Navigator.pop(context);
                      await subscriptionService.purchaseSubscription(_selectedTier, _selectedPeriod);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                        content: Text('🔄 正在处理购买请求...'),
                                        backgroundColor: Colors.blue,
                              duration: Duration(seconds: 2),
                            ),
                          );
                                  },
                                  child: const Text('继续'),
                                ),
                              ],
                            );
                          },
                        );
                        return;
                      }

                      // 正常购买流程 - 只发送购买请求，不立即检查结果
                      await subscriptionService.purchaseSubscription(_selectedTier, _selectedPeriod);
                      
                      // 🔧 修复：不立即检查购买结果，等待Apple的回调
                      // 购买结果会在 _onPurchaseUpdate 中处理
                      // 这里只显示购买请求已发送的提示
                        ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('🔄 正在处理购买请求...'),
                          backgroundColor: Colors.blue,
                          duration: Duration(seconds: 2),
                          ),
                        );
                    } catch (e) {
                      // 处理异常
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('购买失败: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } else {
                    // 选择免费版本的处理
                    if (subscriptionService.isSubscribed) {
                      // 已订阅用户想要取消订阅，显示提示信息
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('💡 请在iPhone设置 > Apple ID > 订阅中管理您的订阅'),
                          backgroundColor: Colors.orange,
                          duration: Duration(seconds: 4),
                        ),
                      );
                    } else {
                      // 免费用户选择继续免费，直接关闭
                      Navigator.pop(context);
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shadowColor: Colors.transparent,
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    String buttonText;

                    // 🔧 修复：根据用户当前状态和订阅周期显示不同按钮文字
                    if (subscriptionService.isSubscribed) {
                      // 已订阅用户
                      if (_selectedTier == subscriptionService.currentTier &&
                          _selectedPeriod == subscriptionService.currentPeriod) {
                        buttonText = languageManager.translate('current_plan');
                      } else if (_selectedTier == SubscriptionTier.free) {
                        buttonText = languageManager.translate('continue_with_free');
                      } else {
                        buttonText = languageManager.translate('change_plan');
                      }
                    } else {
                      // 未订阅用户
                      if (_selectedTier == SubscriptionTier.free) {
                        buttonText = languageManager.translate('continue_with_free');
                      } else {
                        buttonText = languageManager.translate('upgrade_to_premium_enjoy');
                      }
                    }

                    return Text(
                      buttonText,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: 12),

            // 恢复购买按钮
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return TextButton(
                  onPressed: () async {
                    await subscriptionService.restorePurchases();
                  },
                  child: Text(
                    languageManager.translate('restore_purchases'),
                    style: const TextStyle(
                      color: Colors.black54,
                      fontSize: 14,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 8),

            // 法律链接
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: () => _launchURL('https://www.apple.com/legal/privacy/'),
                      child: Text(
                        languageManager.translate('privacy_policy'),
                        style: const TextStyle(
                          color: Colors.black54,
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                    const Text(
                      ' • ',
                      style: TextStyle(color: Colors.black54, fontSize: 12),
                    ),
                    TextButton(
                      onPressed: () => _launchURL('https://www.apple.com/legal/internet-services/itunes/dev/stdeula/'),
                      child: Text(
                        languageManager.translate('terms_of_service'),
                        style: const TextStyle(
                          color: Colors.black54,
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 8),

            // 订阅说明
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(
                  languageManager.translate('subscription_auto_renew_notice'),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.black45,
                    fontSize: 11,
                    height: 1.3,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // 🔗 启动URL
  void _launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('无法启动URL: $url');
      }
    } catch (e) {
      debugPrint('启动URL时出错: $e');
    }
  }

  // 🔧 新增：显示订阅切换对话框
  void _showSubscriptionChangeDialog(
    BuildContext context,
    SubscriptionTier currentTier,
    SubscriptionPeriod currentPeriod,
    SubscriptionTier newTier,
    SubscriptionPeriod newPeriod,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return AlertDialog(
              title: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(languageManager.translate('subscription_change_title')),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${languageManager.translate('current_subscription')}：${_getTierName(currentTier)} - ${_getPeriodName(currentPeriod)}'),
                  Text('${languageManager.translate('target_subscription')}：${_getTierName(newTier)} - ${_getPeriodName(newPeriod)}'),
                  const SizedBox(height: 16),
                  Text(
                    languageManager.translate('important_notice'),
                    style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                  const SizedBox(height: 8),
                  Text(languageManager.translate('subscription_change_notice_1')),
                  Text(languageManager.translate('subscription_change_notice_2')),
                  Text(languageManager.translate('subscription_change_notice_3')),
                  Text(languageManager.translate('subscription_change_notice_4')),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          languageManager.translate('how_to_cancel_old_subscription'),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(languageManager.translate('cancel_step_1')),
                        Text(languageManager.translate('cancel_step_2')),
                        Text(languageManager.translate('cancel_step_3')),
                        Text(languageManager.translate('cancel_step_4')),
                      ],
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(languageManager.translate('cancel')),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    // 继续购买新订阅
                    try {
                      await Provider.of<SubscriptionService>(context, listen: false)
                          .purchaseSubscription(newTier, newPeriod);

                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(languageManager.translate('new_subscription_purchased')),
                            backgroundColor: Colors.green,
                            duration: const Duration(seconds: 4),
                          ),
                        );
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${languageManager.translate('purchase_failed')}: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  child: Text(languageManager.translate('continue_purchase')),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String _getTierName(SubscriptionTier tier) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (tier) {
      case SubscriptionTier.free:
        return languageManager.translate('tier_free');
      case SubscriptionTier.basic:
        return languageManager.translate('tier_basic');
      case SubscriptionTier.premium:
        return languageManager.translate('tier_premium');
    }
  }

  String _getPeriodName(SubscriptionPeriod period) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (period) {
      case SubscriptionPeriod.weekly:
        return languageManager.translate('period_weekly');
      case SubscriptionPeriod.monthly:
        return languageManager.translate('period_monthly');
      case SubscriptionPeriod.yearly:
        return languageManager.translate('period_yearly');
    }
  }
}
