import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/ai_therapy_service.dart';
import '../models/chat_message.dart';
import '../models/therapy_session.dart';
import '../widgets/chat_bubble.dart';
import '../widgets/positive_feedback_card.dart';

class AITherapyChatScreen extends StatefulWidget {
  const AITherapyChatScreen({super.key});

  @override
  State<AITherapyChatScreen> createState() => _AITherapyChatScreenState();
}

class _AITherapyChatScreenState extends State<AITherapyChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AITherapyService _therapyService;
  
  @override
  void initState() {
    super.initState();
    _therapyService = AITherapyService();
    _startSession();
  }

  void _startSession() {
    // 发送欢迎消息
    _therapyService.addMessage(ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: '''🌸 欢迎来到心灵花园 🌸

我是你的AI心理陪伴师小花，
今天想和我聊聊什么呢？

• 工作上的困惑 💼
• 感情中的纠结 💕  
• 人生方向的迷茫 🌟
• 或者任何让你烦恼的事 🤗

请放心分享，这里是安全的空间 💝''',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.welcome,
    ));
    setState(() {});
  }

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: _messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );

    _therapyService.addMessage(userMessage);
    _messageController.clear();
    setState(() {});

    // 滚动到底部
    _scrollToBottom();

    // 获取AI回复
    final aiResponse = await _therapyService.getAIResponse(userMessage.content);
    setState(() {});
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1a1a2e),
      appBar: AppBar(
        title: const Text(
          '心灵花园',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () {
              _therapyService.clearSession();
              _startSession();
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1a1a2e),
              Color(0xFF16213e),
              Color(0xFF0f3460),
            ],
          ),
        ),
        child: Column(
          children: [
            // 聊天消息列表
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: _therapyService.messages.length,
                itemBuilder: (context, index) {
                  final message = _therapyService.messages[index];
                  return ChatBubble(
                    message: message,
                    onTarotRequest: _handleTarotRequest,
                  );
                },
              ),
            ),
            
            // 正反馈卡片（如果有的话）
            if (_therapyService.currentSession?.positiveFeedback.isNotEmpty == true)
              PositiveFeedbackCard(
                feedback: _therapyService.currentSession!.positiveFeedback.last,
              ),
            
            // 输入框
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
                border: Border(
                  top: BorderSide(
                    color: Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: _messageController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          hintText: '分享你的想法...',
                          hintStyle: TextStyle(
                            color: Colors.white54,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                        maxLines: null,
                        onSubmitted: (_) => _sendMessage(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  GestureDetector(
                    onTap: _sendMessage,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
                        ),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: const Icon(
                        Icons.send_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleTarotRequest() {
    // 处理塔罗牌抽取请求
    _therapyService.addMessage(ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: '''✨ 让我们通过塔罗牌来探索你的内心世界

请在心中默念你的问题，然后给我3个1-78之间的数字，
跟随你的直觉就好 🔮

这些数字将帮我们找到：
🌟 当前状况的真相
💎 你的内在力量  
🌱 成长的方向''',
      isUser: false,
      timestamp: DateTime.now(),
      messageType: MessageType.tarotRequest,
    ));
    setState(() {});
    _scrollToBottom();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
