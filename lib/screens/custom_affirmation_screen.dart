import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:ai_tarot_reading/models/manifestation_goal.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/utils/ios_version_checker.dart';
import 'package:ai_tarot_reading/screens/manifestation_physics_game.dart';
import 'package:ai_tarot_reading/services/background_service.dart';

// 下落元素类 - 增强版
class FallingIcon {
  final String emoji;
  final double startX;
  final AnimationController animationController;
  final AnimationController bounceController; // 弹跳动画控制器
  final double rotationSpeed; // 旋转速度
  final double size; // 大小
  bool hasLanded = false; // 是否已着陆

  FallingIcon({
    required this.emoji,
    required this.startX,
    required this.animationController,
    required this.bounceController,
    required this.rotationSpeed,
    required this.size,
  });
}

// 累积元素类 - 增强版
class AccumulatedIcon {
  final String emoji;
  final double x;
  final double y;
  final double angle; // 旋转角度
  final double size; // 大小
  final AnimationController? wobbleController; // 摇摆动画控制器
  final double wobblePhase; // 摇摆相位

  AccumulatedIcon({
    required this.emoji,
    required this.x,
    required this.y,
    required this.angle,
    required this.size,
    this.wobbleController,
    required this.wobblePhase,
  });
}

class CustomAffirmationScreen extends StatefulWidget {
  final ManifestationGoal goal;

  const CustomAffirmationScreen({
    super.key,
    required this.goal,
  });

  @override
  State<CustomAffirmationScreen> createState() => _CustomAffirmationScreenState();
}

class _CustomAffirmationScreenState extends State<CustomAffirmationScreen>
    with TickerProviderStateMixin {
  late List<String> _affirmations;
  int _currentIndex = 0;
  final String _backgroundImage = 'assets/images/default_manifestation_bg.jpg';
  String _customIcon = '✨'; // 默认下落图标（可变）
  
  // 自定义背景和图标相关变量
  File? _customBackgroundImage;
  File? _customFallingIconFile;
  ImageProvider? _customFallingIconProvider;
  
  // 点击计数器相关状态
  int _totalClickCount = 0; // 累积总点击次数
  static const int _clicksPerAffirmation = 5; // 每5次点击换一个肯定语
  late AnimationController _clickAnimationController;

  // 累积下落效果相关变量
  final List<FallingIcon> _fallingIcons = [];
  final List<AccumulatedIcon> _accumulatedIcons = [];
  final Random _random = Random();

  // 传感器相关状态
  late StreamSubscription<AccelerometerEvent> _accelerometerSubscription;
  double _shakeOffsetX = 0.0; // X轴晃动偏移
  double _shakeOffsetY = 0.0; // Y轴晃动偏移
  double _shakeRotation = 0.0; // 旋转偏移

  @override
  void initState() {
    super.initState();
    _affirmations = widget.goal.affirmation!.split('|||');
    _initSensors();
    
    // 初始化点击动画控制器
    _clickAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  // 新的点击计数方法
  void _onManifestationClick() {
    setState(() {
      _totalClickCount++; // 累积计数
    });

    // 触发点击动画
    _clickAnimationController.forward().then((_) {
      _clickAnimationController.reset();
    });

    // 根据是否有自定义图标决定使用哪种掉落方式
    if (_customFallingIconProvider != null) {
      // 使用物理引擎 - 通过全局key调用物理引擎的添加方法
      _addPhysicsIcon();
    } else {
      // 使用传统掉落动画
      _triggerSparkleEffect();
    }

    // 检查是否达到5次点击，如果是则换句子
    if (_totalClickCount % _clicksPerAffirmation == 0) {
      // 自动切换到下一个肯定语
      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          _currentIndex = (_currentIndex + 1) % _affirmations.length;
        });
      });
    }
  }

  // 添加物理引擎图标的方法
  void _addPhysicsIcon() {
    // 物理引擎的图标添加由ManifestationPhysicsWidget内部处理
    debugPrint('🎯 物理引擎图标添加已触发');
  }

  // 初始化传感器监听
  void _initSensors() {
    _accelerometerSubscription = accelerometerEventStream().listen((AccelerometerEvent event) {
      // 处理加速度数据，更新晃动偏移
      setState(() {
        // 将加速度值转换为屏幕偏移（限制幅度）
        _shakeOffsetX = (event.x * 8).clamp(-15.0, 15.0); // X轴偏移限制在±15像素
        _shakeOffsetY = (event.y * 8).clamp(-15.0, 15.0); // Y轴偏移限制在±15像素
        _shakeRotation = (event.z * 0.02).clamp(-0.1, 0.1); // 旋转限制在±0.1弧度
      });
    });
  }

  @override
  void dispose() {
    _accelerometerSubscription.cancel(); // 取消传感器监听
    _clickAnimationController.dispose();

    // 清理掉落动画控制器
    for (var icon in _fallingIcons) {
      icon.animationController.dispose();
      icon.bounceController.dispose();
    }

    // 清理累积元素的摇摆动画控制器
    for (var icon in _accumulatedIcons) {
      icon.wobbleController?.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Scaffold(
      body: Stack(
        children: [
          // 背景
          Consumer<BackgroundService>(
            builder: (context, backgroundService, child) {
              // 如果用户设置了自定义背景，优先使用自定义背景
              if (backgroundService.isUsingCustomBackground && backgroundService.customBackgroundPath != null) {
                return Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: FileImage(File(backgroundService.customBackgroundPath!)),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Container(
                    // 添加半透明遮罩以确保文字可读性
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.3),
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.4),
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                  ),
                );
              }

              // 默认背景
              return Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(_backgroundImage),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.3),
                    Colors.black.withValues(alpha: 0.6),
                  ],
                ),
              ),
            ),
              );
            },
          ),

          // 点击检测层 - 全屏点击（仅在非物理引擎模式下）
          if (_customFallingIconProvider == null)
          Positioned.fill(
            child: GestureDetector(
              onTapDown: (details) {
                // 检查点击位置是否在按钮区域内，如果是则不触发
                const topButtonAreaBottom = 120; // 顶部按钮区域

                if (details.globalPosition.dy < topButtonAreaBottom) {
                  return; // 在顶部按钮区域内，不触发
                }

                // 点击屏幕任何地方都触发下落效果和切换肯定语
                  _onManifestationClick();
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // 物理引擎层 - 使用Flame Forge2D物理引擎（支持自定义和默认图标）
          if (_customFallingIconProvider != null)
            Positioned.fill(
              child: ManifestationPhysicsWidget(
                iconImageProvider: _customFallingIconProvider!,
                onTap: _onManifestationClick, // 传递点击回调
              ),
            )
          else
            // 掉落动画层（备用）
          ..._fallingIcons.map((icon) => AnimatedBuilder(
            animation: Listenable.merge([icon.animationController, icon.bounceController]),
            builder: (context, child) {
              final screenHeight = MediaQuery.of(context).size.height;

              // 计算下落位置
              double fallProgress = icon.animationController.value;
              double currentY = fallProgress * screenHeight;

              // 添加弹跳效果（当接近底部时）
              if (fallProgress > 0.85) {
                final bounceValue = icon.bounceController.value;
                final bounceHeight = (1 - fallProgress) * 50; // 弹跳高度随接近程度减小
                currentY -= bounceValue * bounceHeight * sin(bounceValue * pi * 3); // 3次弹跳
              }

              // 添加左右摆动（模拟空气阻力）
              final swayAmount = sin(fallProgress * pi * 4) * 10 * (1 - fallProgress); // 下落时摆动减少

              return Positioned(
                left: icon.startX + swayAmount,
                top: currentY,
                child: Transform.rotate(
                  angle: icon.animationController.value * 2 * pi * icon.rotationSpeed,
                  child: Transform.scale(
                    scale: 1.0 + sin(icon.bounceController.value * pi) * 0.2, // 弹跳时缩放
                    child: Text(
                      icon.emoji,
                      style: TextStyle(
                        fontSize: icon.size,
                        shadows: [
                          Shadow(
                            color: Colors.amber.withValues(alpha: 0.8),
                            blurRadius: 8 + icon.bounceController.value * 4, // 弹跳时发光更强
                          ),
                          Shadow(
                            color: Colors.white.withValues(alpha: 0.3),
                            blurRadius: 15,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          )),

          // 累积在底部的元素 - 增强版"东倒西歪"真实乱堆效果
          IgnorePointer( // 让累积层不拦截点击事件
            child: Stack(
              children: _accumulatedIcons.asMap().entries.map((entry) {
                final index = entry.key;
                final icon = entry.value;

                // 为每个元素生成固定的随机偏移
                final seed = (index * 1000 + icon.x.toInt()) % 1000000;
                final iconRandom = Random(seed);

                // 计算该列已有的元素数量，用于堆叠高度
                final column = (icon.x / 30).floor();
                final sameColumnItems = _accumulatedIcons.take(index).where((accIcon) =>
                  (accIcon.x / 30).floor() == column).length;

                // 碰撞检测和位置调整
                double finalX = icon.x;
                double finalY = sameColumnItems * 18.0 + 20; // 稍微紧密一些的堆叠

                // 检查与其他元素的碰撞
                for (int i = 0; i < index; i++) {
                  final otherIcon = _accumulatedIcons[i];
                  final distance = sqrt(pow(finalX - otherIcon.x, 2) + pow(finalY - (i * 18.0 + 20), 2));

                  // 如果距离太近，调整位置
                  if (distance < 35) {
                    finalX += (iconRandom.nextDouble() - 0.5) * 15;
                    finalY += iconRandom.nextDouble() * 10;
                  }
                }

                // 东倒西歪的随机参数
                final double baseAngle = icon.angle;
                final double xOffset = (iconRandom.nextDouble() - 0.5) * 25;
                final double yOffset = (iconRandom.nextDouble() - 0.5) * 8;

                return AnimatedBuilder(
                  animation: icon.wobbleController ?? AnimationController(duration: Duration.zero, vsync: this),
                  builder: (context, child) {
                    // 摇摆效果
                    final wobbleValue = icon.wobbleController?.value ?? 0.0;
                    final wobbleAngle = sin(wobbleValue * 2 * pi + icon.wobblePhase) * 0.05; // 轻微摇摆
                    final wobbleX = sin(wobbleValue * 2 * pi + icon.wobblePhase + pi/4) * 2; // 轻微左右摇摆

                    return Positioned(
                      left: finalX + xOffset + _shakeOffsetX + wobbleX,
                      bottom: finalY + yOffset + _shakeOffsetY,
                      child: Transform.rotate(
                        angle: baseAngle + _shakeRotation + wobbleAngle,
                        child: Transform.scale(
                          scale: 1.0 + sin(wobbleValue * 2 * pi + icon.wobblePhase) * 0.02, // 轻微呼吸效果
                          child: Text(
                            icon.emoji,
                            style: TextStyle(
                              fontSize: icon.size,
                              shadows: [
                                Shadow(
                                  color: Colors.black26,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                                Shadow(
                                  color: Colors.amber.withValues(alpha: 0.3),
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
            ),
          ),

          // 主要内容层 - 最上层
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                _buildTopBar(languageManager),

                // 调整上下空间比例，让卡片往上移动
                const Spacer(flex: 2), // 上方空间占2份

                // 肯定语卡片 - 稍微偏上的中心位置
                _buildAffirmationCard(languageManager),

                const Spacer(flex: 3), // 下方空间占3份，让卡片往上移
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar(LanguageManager languageManager) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const Spacer(),
          // 自定义图标按钮
          Consumer<SubscriptionService>(
            builder: (context, subscriptionService, child) {
              return IconButton(
                onPressed: () {
                  if (subscriptionService.isSubscribed) {
                    _showCustomIconSelector();
                  } else {
                    _showCustomIconUpgradeDialog(languageManager);
                  }
                },
                icon: Icon(
                  Icons.auto_awesome,
                  color: subscriptionService.isSubscribed
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.5),
                ),
              );
            },
          ),
          const SizedBox(width: 8),
          // 背景更换按钮
          Consumer<SubscriptionService>(
            builder: (context, subscriptionService, child) {
              return IconButton(
                onPressed: () {
                  if (subscriptionService.isSubscribed) {
                    _pickCustomBackground();
                  } else {
                    _showUpgradeDialog(languageManager);
                  }
                },
                icon: Icon(
                  Icons.image,
                  color: subscriptionService.isSubscribed
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.5),
                ),
              );
            },
          ),
        ],
      ),
    );
  }



  Widget _buildAffirmationCard(LanguageManager languageManager) {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 计数器显示 - 缩小字体和面积
            AnimatedBuilder(
              animation: _clickAnimationController,
              builder: (context, child) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.5),
                      width: 1,
                    ),
                    boxShadow: _clickAnimationController.value > 0 ? [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.4),
                        blurRadius: _clickAnimationController.value * 15,
                        spreadRadius: _clickAnimationController.value * 3,
                      ),
                    ] : null,
                  ),
                  child: Text(
                    '$_totalClickCount',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // 肯定语文本 - 显示1-2句话
            Text(
              _getCurrentAffirmationSnippet(),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.white,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // 点击提示
            Text(
              languageManager.translate('tap_anywhere_to_continue'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }



  String _getCurrentAffirmationSnippet() {
    final fullText = _affirmations[_currentIndex].trim();

    // 使用更精确的正则表达式来分割句子，保留标点符号
    final sentences = <String>[];
    final regex = RegExp(r'[。！？.!?]+');
    final matches = regex.allMatches(fullText);

    if (matches.isEmpty) {
      // 如果没有标点符号，直接返回截取的文本
      return fullText.length > 150
          ? '${fullText.substring(0, 150)}...'
          : fullText;
    }

    int lastEnd = 0;
    for (final match in matches) {
      final sentence = fullText.substring(lastEnd, match.end).trim();
      if (sentence.isNotEmpty) {
        sentences.add(sentence);
      }
      lastEnd = match.end;
    }

    // 如果还有剩余文本（没有标点符号结尾的）
    if (lastEnd < fullText.length) {
      final remaining = fullText.substring(lastEnd).trim();
      if (remaining.isNotEmpty) {
        sentences.add(remaining);
      }
    }

    // 取前1-2句话，大约100-150字
    if (sentences.length >= 2) {
      final combined = '${sentences[0]} ${sentences[1]}';
      return combined.length > 150
          ? '${combined.substring(0, 150)}...'
          : combined;
    } else if (sentences.isNotEmpty) {
      final firstSentence = sentences[0];
      return firstSentence.length > 150
          ? '${firstSentence.substring(0, 150)}...'
          : firstSentence;
    } else {
      // 备用方案
      return fullText.length > 150
          ? '${fullText.substring(0, 150)}...'
          : fullText;
    }
  }

  void _nextAffirmation() {
    if (_currentIndex < _affirmations.length - 1) {
      setState(() {
        _currentIndex++;
      });
    } else {
      // 回到第一条
      setState(() {
        _currentIndex = 0;
      });
    }
  }



  void _triggerSparkleEffect() {
    _addFallingIcons();
  }

  // 构建图标widget（支持emoji和图片）
  Widget _buildIconWidget(String iconData, double size) {
    // 检查是否是文件路径（自定义图片）
    if (iconData.startsWith('/') || iconData.contains('.')) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: FileImage(File(iconData)),
            fit: BoxFit.contain,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.amber.withValues(alpha: 0.8),
              blurRadius: 8,
            ),
            BoxShadow(
              color: Colors.white.withValues(alpha: 0.3),
              blurRadius: 15,
            ),
          ],
        ),
      );
    } else {
      // 默认emoji显示
      return Text(
        iconData,
        style: TextStyle(
          fontSize: size,
          shadows: [
            Shadow(
              color: Colors.amber.withValues(alpha: 0.8),
              blurRadius: 8,
            ),
            Shadow(
              color: Colors.white.withValues(alpha: 0.3),
              blurRadius: 15,
            ),
          ],
        ),
      );
    }
  }

  // 添加下落图标（类似其他练习模块）
  void _addFallingIcons() {
    setState(() {
      // 每次点击生成3-5个下落图标
      final iconCount = 3 + _random.nextInt(3); // 3-5个
      for (int i = 0; i < iconCount; i++) {
        final fallingIcon = _createFallingIcon();
        _fallingIcons.add(fallingIcon);
      }
    });

    // 当掉落动画完成后，将元素添加到累积列表中
    Future.delayed(const Duration(milliseconds: 1800), () {
      if (mounted) {
        setState(() {
          // 将完成的掉落元素转换为累积元素
          for (var icon in _fallingIcons) {
            if (icon.animationController.isCompleted) {
              final screenWidth = MediaQuery.of(context).size.width;
              final gridColumns = (screenWidth / 30).floor();

              // 计算列位置
              final column = (icon.startX / 30).floor().clamp(0, gridColumns - 1);

              // 创建摇摆动画控制器
              final wobbleController = AnimationController(
                duration: Duration(milliseconds: 2000 + _random.nextInt(1000)), // 2-3秒随机周期
                vsync: this,
              );
              wobbleController.repeat(reverse: true);

              _accumulatedIcons.add(AccumulatedIcon(
                emoji: icon.emoji,
                x: column * 30.0,
                y: 0, // 使用bottom定位，不需要Y坐标
                angle: (_random.nextDouble() - 0.5) * 0.4, // 随机初始角度
                size: icon.size, // 保持下落时的大小
                wobbleController: wobbleController,
                wobblePhase: _random.nextDouble() * 2 * pi, // 随机摇摆相位
              ));
            }
          }
          // 移除已完成的掉落元素
          _fallingIcons.removeWhere((icon) => icon.animationController.isCompleted);
        });
      }
    });
  }

  // 创建下落图标 - 增强版
  FallingIcon _createFallingIcon() {
    final screenWidth = MediaQuery.of(context).size.width;
    final startX = _random.nextDouble() * (screenWidth - 40);

    // 主下落动画控制器
    final controller = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );

    // 弹跳动画控制器
    final bounceController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // 随机属性
    final rotationSpeed = 1.0 + _random.nextDouble() * 2.0; // 1-3倍旋转速度
    final size = 28.0 + _random.nextDouble() * 8.0; // 28-36px大小变化

    // 启动下落动画
    controller.forward();

    // 当下落完成时触发弹跳效果
    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        bounceController.forward().then((_) {
          bounceController.reverse();
        });
      }
    });

    return FallingIcon(
      emoji: _customIcon,
      startX: startX,
      animationController: controller,
      bounceController: bounceController,
      rotationSpeed: rotationSpeed,
      size: size,
    );
  }



  void _showCustomIconSelector() async {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    // 检查iOS版本和VisionKit支持
    final iosVersionInfo = await IOSVersionChecker.getIOSVersion();
    final supportsVisionKit = await IOSVersionChecker.supportsVisionKitBackgroundRemoval();

    if (!mounted) return; // 检查widget是否仍然挂载

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            const Text('✨', style: TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            Text(languageManager.translate('custom_falling_icon')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // iOS版本信息显示
            if (IOSVersionChecker.isIOS) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: supportsVisionKit ? Colors.green.withValues(alpha: 0.1) : Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: supportsVisionKit ? Colors.green : Colors.orange,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📱 ${iosVersionInfo.fullVersionString}',
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      iosVersionInfo.supportStatusDescription,
                      style: TextStyle(
                        fontSize: 12,
                        color: supportsVisionKit ? Colors.green[700] : Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            Text(languageManager.translate('upload_transparent_png_tip')),
            const SizedBox(height: 16),

            // 选择图片按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  Navigator.pop(context);
                  await _pickCustomIcon();
                },
                icon: const Icon(Icons.upload),
                label: Text(languageManager.translate('select_image')),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                ),
              ),
            ),

            // VisionKit抠图按钮（仅iOS 17+显示）
            if (supportsVisionKit) ...[
              const SizedBox(height: 8),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () async {
                    Navigator.pop(context);
                    await _pickAndRemoveBackground();
                  },
                  icon: const Icon(Icons.auto_fix_high),
                  label: Text(languageManager.translate('auto_remove_background')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageManager.translate('cancel')),
          ),
        ],
      ),
    );
  }

  Future<void> _pickCustomIcon() async {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    // 检查是否为付费会员
    if (!subscriptionService.isSubscribed) {
      _showCustomIconUpgradeDialog(Provider.of<LanguageManager>(context, listen: false));
      return;
    }

    final ImagePicker picker = ImagePicker();
    final XFile? picked = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 512,
      maxHeight: 512,
      imageQuality: 90,
    );

    if (picked != null) {
      try {
        // 显示加载对话框
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );

        // 使用VisionKit进行背景移除
        final processedPath = await IOSVersionChecker.removeBackgroundWithVisionKit(picked.path);
        
        // 关闭加载对话框
        if (mounted) {
          Navigator.pop(context);
        }

        if (processedPath != null) {
          // 显示预览对话框
          if (mounted) {
            final languageManager = Provider.of<LanguageManager>(context, listen: false);
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text(languageManager.translate('preview_cutout_result')),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('原图:'),
                    Image.file(File(picked.path), width: 120, height: 120),
                    SizedBox(height: 16),
                    Text('抠图后:'),
                    Image.file(File(processedPath), width: 120, height: 120),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(languageManager.translate('cancel')),
                  ),
                  ElevatedButton(
                    onPressed: () {
      setState(() {
                        _customFallingIconFile = File(processedPath);
                        _customFallingIconProvider = FileImage(File(processedPath));
                      });
                      Navigator.pop(context);
                      
                      // 显示成功提示
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              const Icon(Icons.check_circle, color: Colors.white),
                              const SizedBox(width: 8),
                              Text(languageManager.translate('custom_icon_updated_successfully')),
                            ],
                          ),
                          backgroundColor: Colors.green,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    child: Text(languageManager.translate('confirm')),
                  ),
                ],
              ),
            );
          }
        } else {
          // 如果抠图失败，直接使用原图
          setState(() {
            _customFallingIconFile = File(picked.path);
            _customFallingIconProvider = FileImage(File(picked.path));
      });

          // 显示成功提示
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                    Text(languageManager.translate('custom_icon_updated_successfully')),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
          }
        }
      } catch (e) {
        // 关闭加载对话框
        if (mounted) {
          Navigator.pop(context);
        }
        
        // 显示错误提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('处理图片失败: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // VisionKit抠图功能
  Future<void> _pickAndRemoveBackground() async {
    try {
      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(languageManager.translate('selecting_image'));
                  },
                ),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // 选择图片
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 90,
      );

      if (image == null || !mounted) return;

      // 显示抠图进度
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(width: 12),
              Text('🤖 AI正在智能抠图，请稍候...'),
            ],
          ),
          backgroundColor: Colors.purple,
          duration: Duration(seconds: 5),
        ),
      );

      // 调用VisionKit抠图
      final processedImagePath = await IOSVersionChecker.removeBackgroundWithVisionKit(image.path);

      if (processedImagePath != null && mounted) {
        setState(() {
          _customIcon = processedImagePath;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.auto_fix_high, color: Colors.white),
                SizedBox(width: 8),
                Text('🎉 AI抠图完成！自定义图标已更新'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('❌ VisionKit抠图失败: $e');

      if (mounted) {
        String errorMessage;
        if (e.toString().contains('iOS 17')) {
          errorMessage = '❌ 需要iOS 17或更高版本才能使用AI抠图功能';
        } else if (e.toString().contains('VisionKit')) {
          errorMessage = '❌ AI抠图功能暂时不可用，请直接上传透明PNG图片';
        } else {
          errorMessage = '❌ 抠图失败，请重试或直接上传透明PNG图片';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: '选择图片',
              textColor: Colors.white,
              onPressed: () => _pickCustomIcon(),
            ),
          ),
        );
      }
    }
  }

  Future<void> _pickCustomBackground() async {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    // 检查是否为付费会员
    if (!subscriptionService.isSubscribed) {
      _showUpgradeDialog(Provider.of<LanguageManager>(context, listen: false));
      return;
    }

    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1920,
      maxHeight: 1080,
      imageQuality: 85,
    );

    if (image != null) {
      // 更新本地状态
      setState(() {
        _customBackgroundImage = File(image.path);
      });

      // 同时更新BackgroundService
      final backgroundService = Provider.of<BackgroundService>(context, listen: false);
      await backgroundService.setCustomBackground(image.path);

      // 显示成功提示
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('background_updated_successfully')),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showCustomIconUpgradeDialog(LanguageManager languageManager) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            const Icon(Icons.auto_awesome, color: Colors.amber),
            const SizedBox(width: 8),
            Text(languageManager.translate('premium_exclusive_feature')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(languageManager.translate('custom_icon_premium_only')),
            const SizedBox(height: 12),
            Text(
              languageManager.translate('custom_icon_instruction'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageManager.translate('later')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.amber),
            child: Text(languageManager.translate('upgrade_now')),
          ),
        ],
      ),
    );
  }

  void _showUpgradeDialog(LanguageManager languageManager) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            const Icon(Icons.image, color: Colors.amber),
            const SizedBox(width: 8),
            Text(languageManager.translate('premium_exclusive_feature')),
          ],
        ),
        content: Text(languageManager.translate('background_change_premium_only')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageManager.translate('later')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.amber),
            child: Text(languageManager.translate('upgrade_now')),
          ),
        ],
      ),
    );
  }
}
