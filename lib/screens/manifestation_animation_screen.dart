import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/services/affirmation_service.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/widgets/liquid_chrome_background.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/services/background_service.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'dart:async';
import 'package:ai_tarot_reading/screens/manifestation_physics_game.dart';
import 'package:ai_tarot_reading/services/vision_kit_service.dart';

class ManifestationAnimationScreen extends StatefulWidget {
  final ManifestationGoal goal;
  final String affirmation;
  final ManifestationVersion? version;

  const ManifestationAnimationScreen({
    super.key,
    required this.goal,
    required this.affirmation,
    this.version,
  });

  @override
  State<ManifestationAnimationScreen> createState() => _ManifestationAnimationScreenState();
}

class _ManifestationAnimationScreenState extends State<ManifestationAnimationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final List<FallingItem> _fallingItems = [];
  final List<AccumulatedItem> _accumulatedItems = []; // 累积在底部的元素
  final Random _random = Random();
  String? _customBackgroundPath;

  // 传感器相关状态
  late StreamSubscription<AccelerometerEvent> _accelerometerSubscription;
  double _shakeOffsetX = 0.0; // X轴晃动偏移
  double _shakeOffsetY = 0.0; // Y轴晃动偏移
  double _shakeRotation = 0.0; // 旋转偏移
  late AnimationController _shakeAnimationController;

  // 肯定语循环相关状态
  List<String> _affirmations = [];
  int _currentAffirmationIndex = 0;
  String _currentAffirmation = '';
  bool _isAffirmationMode = false;

  // 点击计数器相关状态
  int _totalClickCount = 0; // 累积总点击次数
  static const int _clicksPerAffirmation = 5; // 每5次点击换一个肯定语
  late AnimationController _clickAnimationController;

  // 自定义背景
  File? _customBackgroundImage;
  
  // 自定义下坠图标
  File? _customFallingIconFile;
  ImageProvider? _customFallingIconProvider;

  // 获取当前图标提供者
  ImageProvider? _getCurrentIconProvider() {
    if (_customFallingIconProvider != null) {
      return _customFallingIconProvider;
    }
    // 回退到默认图标
    return null;
  }

  // 显示自定义图标选择器
  void _showCustomIconSelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 400,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Text(
                    '自定义下坠图标',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    // 选择图片按钮
                    ListTile(
                      leading: const Icon(Icons.photo_library),
                      title: const Text('从相册选择'),
                      subtitle: const Text('选择一张图片作为下坠图标'),
                      onTap: () {
                        Navigator.pop(context);
                        _pickCustomIcon();
                      },
                    ),
                    // AI抠图按钮
                    ListTile(
                      leading: const Icon(Icons.auto_fix_high),
                      title: const Text('AI智能抠图'),
                      subtitle: const Text('自动移除背景，提取主体'),
                      onTap: () {
                        Navigator.pop(context);
                        _pickAndProcessIcon();
                      },
                    ),
                    // 重置按钮
                    if (_customFallingIconProvider != null)
                      ListTile(
                        leading: const Icon(Icons.refresh),
                        title: const Text('重置为默认'),
                        subtitle: const Text('恢复默认图标'),
                        onTap: () {
                          Navigator.pop(context);
                          _resetCustomIcon();
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 选择自定义图标
  Future<void> _pickCustomIcon() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        final file = File(image.path);
        setState(() {
          _customFallingIconFile = file;
          _customFallingIconProvider = FileImage(file);
        });

        // 重新创建物理游戏以使用新图标
        _recreatePhysicsGame();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('自定义图标设置成功！')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图标失败: $e')),
      );
    }
  }

  // 选择图片并进行AI抠图处理
  Future<void> _pickAndProcessIcon() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 90,
      );

      if (image != null) {
        // 显示处理中对话框
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('AI正在处理图片...'),
              ],
            ),
          ),
        );

        try {
          // 调用VisionKit进行背景移除
          final processedImagePath = await VisionKitService.removeBackground(image.path);

          Navigator.pop(context); // 关闭处理对话框

          if (processedImagePath != null) {
            final file = File(processedImagePath);
            setState(() {
              _customFallingIconFile = file;
              _customFallingIconProvider = FileImage(file);
            });

            // 重新创建物理游戏以使用新图标
            _recreatePhysicsGame();

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('AI抠图完成，自定义图标设置成功！')),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('AI抠图失败，请尝试其他图片')),
            );
          }
        } catch (e) {
          Navigator.pop(context); // 关闭处理对话框
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('AI处理失败: $e')),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图片失败: $e')),
      );
    }
  }

  // 重置自定义图标
  void _resetCustomIcon() {
    setState(() {
      _customFallingIconFile = null;
      _customFallingIconProvider = null;
    });

    // 重新创建物理游戏以使用默认图标
    _recreatePhysicsGame();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已重置为默认图标')),
    );
  }

  // 重新创建物理游戏
  void _recreatePhysicsGame() {
    // 这里可以重新初始化物理游戏以使用新的图标
    // 具体实现取决于物理游戏的架构
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _clickAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 初始化晃动动画控制器
    _shakeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    // 初始化肯定语 - 延迟到build方法中获取语言代码
    _currentAffirmation = widget.affirmation;

    // 初始化传感器监听
    _initSensors();
  }

  // 初始化传感器监听
  void _initSensors() {
    _accelerometerSubscription = accelerometerEventStream().listen((AccelerometerEvent event) {
      // 处理加速度数据，更新晃动偏移
      setState(() {
        // 将加速度值转换为屏幕偏移（限制幅度）
        _shakeOffsetX = (event.x * 8).clamp(-15.0, 15.0); // X轴偏移限制在±15像素
        _shakeOffsetY = (event.y * 8).clamp(-15.0, 15.0); // Y轴偏移限制在±15像素
        _shakeRotation = (event.z * 0.02).clamp(-0.1, 0.1); // 旋转限制在±0.1弧度
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _clickAnimationController.dispose();
    _shakeAnimationController.dispose(); // 释放晃动动画控制器
    _accelerometerSubscription.cancel(); // 取消传感器监听
    // 清理掉落动画控制器
    for (var item in _fallingItems) {
      item.animationController.dispose();
    }
    super.dispose();
  }

  void _nextAffirmation() {
    final affirmations = _affirmations;
    if (affirmations.isNotEmpty) {
      setState(() {
        _currentAffirmationIndex = (_currentAffirmationIndex + 1) % affirmations.length;
        _currentAffirmation = affirmations[_currentAffirmationIndex];
      });
    }
  }

  // 新的点击计数方法
  void _onManifestationClick() {
    // 如果还没有进入肯定语模式，先进入肯定语模式
    if (!_isAffirmationMode) {
      setState(() {
        _isAffirmationMode = true;
        _currentAffirmationIndex = 0;
        _currentAffirmation = _affirmations[0];
        _totalClickCount = 1; // 这次点击算作第一次
      });
    } else {
      setState(() {
        _totalClickCount++; // 累积计数
      });
    }

    // 触发点击动画
    _clickAnimationController.forward().then((_) {
      _clickAnimationController.reset();
    });

    // 触发掉落动画
    _addFallingItems();

    // 检查是否达到5次点击，如果是则换句子
    if (_totalClickCount % _clicksPerAffirmation == 0) {
      // 自动切换到下一个肯定语
      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          _currentAffirmationIndex = (_currentAffirmationIndex + 1) % _affirmations.length;
          _currentAffirmation = _affirmations[_currentAffirmationIndex];
        });
      });
    }
  }

  void _addFallingItems() {
    setState(() {
      for (int i = 0; i < 8; i++) {
        final fallingItem = _createFallingItem();
        _fallingItems.add(fallingItem);
      }
    });

    // 当掉落动画完成后，将元素添加到累积列表中  
    Future.delayed(const Duration(milliseconds: 1800), () {
      if (mounted) {
        setState(() {
          // 将完成的掉落元素转换为累积元素
          for (var item in _fallingItems) {
            if (item.animationController.isCompleted) {
              // 简化累积逻辑 - 使用Stack+bottom定位
              final screenWidth = MediaQuery.of(context).size.width;
              final gridColumns = (screenWidth / 30).floor();
              
              // 计算列位置
              final column = (item.startX / 30).floor().clamp(0, gridColumns - 1);
              
              _accumulatedItems.add(AccumulatedItem(
                emoji: item.emoji,
                imagePath: item.imagePath,
                x: column * 30.0,
                y: 0, // 不再需要Y坐标计算，使用bottom定位
              ));
              print('✅ 累积元素添加成功: 列$column, X位置${column * 30.0}, 累积总数: ${_accumulatedItems.length}');
            }
          }
          // 移除已完成的掉落元素
          _fallingItems.removeWhere((item) => item.animationController.isCompleted);
        });
      }
    });
  }

  FallingItem _createFallingItem() {
    final screenWidth = MediaQuery.of(context).size.width;
    final startX = _random.nextDouble() * (screenWidth - 40);
    
    final controller = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );
    controller.forward();

    // 根据目标和版本选择合适的emoji
    String emoji;
    String? imagePath;
    
    switch (widget.goal) {
      case ManifestationGoal.wealth:
        if (widget.version == ManifestationVersion.eastern) {
          // 东方版财富：使用金元宝图片
          emoji = ''; // 不使用emoji，使用图片
          final goldIngots = ['assets/images/gold_ingot_1.png', 'assets/images/gold_ingot_2.png'];
          imagePath = goldIngots[_random.nextInt(goldIngots.length)];
        } else {
          // 西方版财富：钻石和美钞
          final westEmojis = ['💎', '💵'];
          emoji = westEmojis[_random.nextInt(westEmojis.length)];
        }
        break;
      case ManifestationGoal.career:
        emoji = '🏆';
        break;
      case ManifestationGoal.beauty:
        emoji = '🌸'; // 桃花
        break;
      case ManifestationGoal.fame:
        emoji = '⭐';
        break;
      case ManifestationGoal.love:
        emoji = '💝';
        break;
    }

    return FallingItem(
      emoji: emoji,
      imagePath: imagePath,
      startX: startX,
      animationController: controller,
    );
  }

  Future<void> _pickCustomBackground() async {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);

    // 检查是否为付费会员
    if (!subscriptionService.isSubscribed) {
      _showUpgradeDialog();
      return;
    }

    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1920,
      maxHeight: 1080,
      imageQuality: 85,
    );

    if (image != null) {
      // 更新本地状态
      setState(() {
        _customBackgroundPath = image.path;
      });

      // 同时更新BackgroundService
      final backgroundService = Provider.of<BackgroundService>(context, listen: false);
      await backgroundService.setCustomBackground(image.path);

      // 显示成功提示
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('background_updated_successfully')),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // 显示升级对话框
  void _showUpgradeDialog() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.image,
              color: Colors.orange,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(languageManager.translate('membership_feature')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              languageManager.translate('custom_background_premium_feature'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              languageManager.translate('upgrade_to_premium_enjoy'),
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Text(
              languageManager.translate('premium_features_list'),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              languageManager.translate('later'),
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6B46C1),
              foregroundColor: Colors.white,
            ),
            child: Text(languageManager.translate('upgrade_now')),
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Consumer<BackgroundService>(
      builder: (context, backgroundService, child) {
        // 如果用户设置了自定义背景，优先使用自定义背景
        if (backgroundService.isUsingCustomBackground && backgroundService.customBackgroundPath != null) {
          return Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: FileImage(File(backgroundService.customBackgroundPath!)),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              // 添加半透明遮罩以确保文字可读性
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.3),
                    Colors.transparent,
                    Colors.black.withOpacity(0.4),
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
          );
        }

        return _buildDefaultBackground();
      },
    );
  }

  Widget _buildDefaultBackground() {

    // 名声显化使用金色液体Chrome背景
    if (widget.goal == ManifestationGoal.fame) {
      return LiquidChromeBackground(
        baseColor: const Color(0xFFD4AF37), // 金色
        speed: 0.3,
        amplitude: 0.6,
        frequencyX: 2.5,
        frequencyY: 1.8,
        interactive: true,
      );
    }

    // 财富显化根据版本使用不同背景
    if (widget.goal == ManifestationGoal.wealth) {
      String backgroundAsset;
      if (widget.version == ManifestationVersion.eastern) {
        backgroundAsset = 'assets/images/wealth_god_background.jpg';
      } else {
        // 西方版使用money.png背景
        backgroundAsset = 'assets/images/money.png';
      }

      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(backgroundAsset),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          // 添加半透明遮罩以确保文字可读性
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.3),
                Colors.transparent,
                Colors.black.withOpacity(0.4),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
        ),
      );
    }

    // 其他目标使用原有渐变背景
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFfa709a), // 粉红色
            Color(0xFFfee140), // 金黄色
            Color(0xFFa8edea), // 薄荷色
            Color(0xFFfed6e3), // 淡粉色
            Color(0xFFd299c2), // 紫粉色
          ],
          stops: [0.0, 0.25, 0.5, 0.75, 1.0],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    // 初始化肯定语（如果还没有初始化）
    if (_affirmations.isEmpty) {
      _affirmations = AffirmationService.get20RandomAffirmations(widget.goal, languageCode: languageManager.currentLanguage);
    }

    return Scaffold(
      body: Stack(
        children: [
          // 背景
          _buildBackground(),

          // 点击检测层 - 全屏点击计数
          Positioned.fill(
            child: GestureDetector(
              onTapDown: (details) {
                // 检查点击位置是否在按钮区域内，如果是则不触发计数
                final screenHeight = MediaQuery.of(context).size.height;
                final buttonAreaTop = screenHeight - 120; // 按钮区域大概位置
                const topButtonAreaBottom = 120; // 顶部按钮区域

                if (details.globalPosition.dy > buttonAreaTop ||
                    details.globalPosition.dy < topButtonAreaBottom) {
                  return; // 在按钮区域内，不触发计数
                }

                // 点击屏幕任何地方都进行计数和显化
                _onManifestationClick();
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // 物理引擎层 - 使用Flame Forge2D物理引擎（支持自定义和默认图标）
          Positioned.fill(
            child: ManifestationPhysicsWidget(
              iconImageProvider: _customFallingIconProvider,
              goal: widget.goal,
              version: widget.version,
              onTap: _onManifestationClick, // 传递点击回调
            ),
          ),

          // 掉落动画层（备用）
          ..._fallingItems.map((item) => AnimatedBuilder(
            animation: item.animationController,
            builder: (context, child) {
              return Positioned(
                left: item.startX,
                top: item.animationController.value * MediaQuery.of(context).size.height,
                child: Transform.rotate(
                  angle: item.animationController.value * 2 * pi,
                  child: item.imagePath != null
                      ? Image.asset(
                          item.imagePath!,
                          width: 40,
                          height: 40,
                          errorBuilder: (context, error, stackTrace) {
                            // 如果图片加载失败，显示默认emoji
                            return Text(
                              '💰', // 使用金钱袋作为默认emoji
                              style: const TextStyle(fontSize: 40), // 与正常emoji字体大小一致
                            );
                          },
                        )
                      : Text(
                          item.emoji!,
                          style: const TextStyle(fontSize: 32),
                        ),
                ),
              );
            },
          )),

          // 累积在底部的元素 - "东倒西歪"真实乱堆效果
          IgnorePointer( // 让累积层不拦截点击事件
            child: Stack(
              children: _accumulatedItems.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                
                // 为每个元素生成固定的随机偏移（基于index和x位置作为种子）
                final seed = (index * 1000 + item.x.toInt()) % 1000000;
                final itemRandom = Random(seed);
                
                // 计算该列已有的元素数量，用于堆叠高度
                final column = (item.x / 30).floor();
                final sameColumnItems = _accumulatedItems.take(index).where((accItem) => 
                  (accItem.x / 30).floor() == column).length;
                
                // 东倒西歪的随机参数
                final double angle = (itemRandom.nextDouble() - 0.5) * 0.4; // -0.2 ~ +0.2 radians (更大角度)
                final double xOffset = (itemRandom.nextDouble() - 0.5) * 25; // 左右偏移 -12.5 ~ +12.5
                final double yOffset = (itemRandom.nextDouble() - 0.5) * 8; // 上下轻微浮动 -4 ~ +4
                
                return Positioned(
                  left: item.x + xOffset + _shakeOffsetX, // 原位置 + 随机偏移 + 传感器偏移
                  bottom: sameColumnItems * 20.0 + 120 + yOffset + _shakeOffsetY, // 更近的叠加距离：20px而不是28px
                  child: Transform.rotate(
                    angle: angle + _shakeRotation, // 每个元素都有随机旋转 + 传感器旋转
                    child: item.imagePath != null
                        ? Image.asset(
                            item.imagePath!,
                            width: 45, // 放大90%：32 * 1.4 ≈ 45
                            height: 45, // 放大90%：32 * 1.4 ≈ 45
                            fit: BoxFit.contain, // 保持图片比例
                            errorBuilder: (context, error, stackTrace) {
                              return Text(
                                '💰', // 使用金钱袋作为默认emoji
                                style: const TextStyle(fontSize: 40), // 与正常emoji字体大小一致
                              );
                            },
                          )
                        : Text(
                            item.emoji!,
                            style: const TextStyle(
                              fontSize: 40, // 放大90%：28 * 1.4 ≈ 40
                              shadows: [
                                Shadow(
                                  color: Colors.black26,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                          ),
                  ),
                );
              }).toList(),
            ),
          ),

          // 主要UI内容层 - 放在最上层，确保正念语始终可见
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const Spacer(),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          String goalName = '';
                          switch (widget.goal) {
                            case ManifestationGoal.wealth:
                              goalName = languageManager.translate('wealth_goal');
                              break;
                            case ManifestationGoal.career:
                              goalName = languageManager.translate('career_goal');
                              break;
                            case ManifestationGoal.beauty:
                              goalName = languageManager.translate('beauty_goal');
                              break;
                            case ManifestationGoal.fame:
                              goalName = languageManager.translate('fame_goal');
                              break;
                            case ManifestationGoal.love:
                              goalName = languageManager.translate('love_goal');
                              break;
                          }
                          return Text(
                            '$goalName ${languageManager.translate('manifestation')}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                      const Spacer(),
                      Consumer<SubscriptionService>(
                        builder: (context, subscriptionService, child) {
                          if (subscriptionService.isSubscribed) {
                            // 付费会员显示设置按钮
                            return Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // 自定义图标按钮
                                IconButton(
                                  onPressed: _showCustomIconSelector,
                                  icon: const Icon(
                                    Icons.auto_fix_high,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                  tooltip: '自定义图标',
                                ),
                                // 背景更换按钮
                                IconButton(
                                  onPressed: _pickCustomBackground,
                                  icon: const Icon(
                                    Icons.image,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                  tooltip: '更换背景',
                                ),
                              ],
                            );
                          } else {
                            // 免费用户显示升级按钮
                            return IconButton(
                              onPressed: _showUpgradeDialog,
                              icon: Stack(
                                children: [
                                  const Icon(
                                    Icons.image,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                  Positioned(
                                    right: 0,
                                    top: 0,
                                    child: Container(
                                      width: 8,
                                      height: 8,
                                      decoration: const BoxDecoration(
                                        color: Colors.orange,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),

                const Spacer(),

                // 中心内容 - 液体玻璃毛玻璃样式（正念语最上层）
                Container(
                  margin: const EdgeInsets.all(32),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    // 液体玻璃毛玻璃效果 - 高透明度
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.1),
                        blurRadius: 25,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 15,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 计数器显示 - 缩小字体和面积
                      AnimatedBuilder(
                        animation: _clickAnimationController,
                        builder: (context, child) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.5),
                                width: 1,
                              ),
                              boxShadow: _clickAnimationController.value > 0 ? [
                                BoxShadow(
                                  color: Colors.white.withOpacity(0.4),
                                  blurRadius: _clickAnimationController.value * 15,
                                  spreadRadius: _clickAnimationController.value * 3,
                                ),
                              ] : null,
                            ),
                            child: Text(
                              '$_totalClickCount',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 24),

                      // 肯定语文本 - 增大字体（最上层显示）
                      Text(
                        _currentAffirmation,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 24),

                      // 提示文本
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            _isAffirmationMode
                              ? languageManager.translate('tap_screen_count')
                              : languageManager.translate('tap_screen_start'),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.8),
                            ),
                            textAlign: TextAlign.center,
                          );
                        },
                      ),

                      // 完成提示 - 20次后显示
                      if (_isAffirmationMode && _currentAffirmationIndex >= 19) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.green.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return Text(
                                '🎉 ${languageManager.translate('manifestation_practice_complete')}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.green,
                                  fontWeight: FontWeight.w600,
                                ),
                              );
                            },
                          ),
                        ).animate().fadeIn(duration: 800.ms).scale(
                          begin: const Offset(0.8, 0.8),
                          end: const Offset(1.0, 1.0),
                        ),
                      ],
                    ],
                  ),
                ),

                const Spacer(),

                // 底部按钮
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            widget.goal.displayName == '财富' 
                                ? const Color(0xFFFFD700) 
                                : widget.goal.displayName == '事业'
                                    ? const Color(0xFF4A90E2)
                                    : widget.goal.displayName == '美貌'
                                        ? const Color(0xFFFF69B4)
                                        : widget.goal.displayName == '名气'
                                            ? const Color(0xFFFF4500)
                                            : const Color(0xFFFF1493), // 感情
                            widget.goal.displayName == '财富' 
                                ? const Color(0xFFFFA500) 
                                : widget.goal.displayName == '事业'
                                    ? const Color(0xFF2E8B57)
                                    : widget.goal.displayName == '美貌'
                                        ? const Color(0xFFDA70D6)
                                        : widget.goal.displayName == '名气'
                                            ? const Color(0xFFFF6347)
                                            : const Color(0xFFC71585), // 感情
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: () => _completeManifestationPractice(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shadowColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('complete_manifestation'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 完成显化练习并记录到后端
  Future<void> _completeManifestationPractice() async {
    try {
      // 记录显化练习到后端
      final dataService = SupabaseDataService();
      await dataService.recordManifestationPractice(
        goal: widget.goal,
        clickCount: _totalClickCount,
        affirmations: _affirmations,
      );

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(Provider.of<LanguageManager>(context, listen: false)
                    .translate('manifestation_practice_recorded')),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ 记录显化练习失败: $e');
      // 即使记录失败也允许用户退出
    }

    // 返回上一页
    if (mounted) {
      Navigator.pop(context);
    }
  }
}

class FallingItem {
  final String? emoji;
  final String? imagePath;
  final double startX;
  final AnimationController animationController;

  FallingItem({
    this.emoji,
    this.imagePath,
    required this.startX,
    required this.animationController,
  }) : assert(emoji != null || imagePath != null, 'Either emoji or imagePath must be provided');
}

// 累积元素类
class AccumulatedItem {
  final String? emoji;
  final String? imagePath;
  final double x;
  final double y;

  AccumulatedItem({
    this.emoji,
    this.imagePath,
    required this.x,
    required this.y,
  });
}