import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/widgets/reading_history_item.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/models/tarot_reading.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/services/tarot_reading_service.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/models/manifestation_goal.dart';
import 'package:ai_tarot_reading/services/manifestation_goal_service.dart';

import 'package:intl/intl.dart';
import 'package:flutter_animate/flutter_animate.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  String _searchQuery = '';
  bool _isRefreshing = false;

  // 🔧 添加后端数据支持
  final TarotReadingService _tarotReadingService = TarotReadingService();
  List<TarotReading> _backendReadings = [];
  bool _isLoadingBackend = false;

  // 显化目标服务
  final ManifestationGoalService _manifestationService = ManifestationGoalService();

  // 🌟 肯定语状态管理
  final Map<String, Map<String, dynamic>> _affirmationStates = {};
  final TextEditingController _goalController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 🔧 加载后端数据
    _loadBackendReadings();
    // 初始化显化目标服务
    _manifestationService.initialize();
  }

  @override
  void dispose() {
    _goalController.dispose();
    _manifestationService.dispose();
    super.dispose();
  }

  // 🔧 加载后端塔罗解读数据
  Future<void> _loadBackendReadings() async {
    setState(() {
      _isLoadingBackend = true;
    });

    try {
      final backendData = await _tarotReadingService.getTarotReadingHistory();
      final List<TarotReading> backendReadings = [];

      for (final data in backendData) {
        try {
          // 转换后端数据为TarotReading对象
          final cards = <TarotCard>[];
          final cardsData = data['cards'] as List<dynamic>? ?? [];

          for (final cardData in cardsData) {
            final cardName = cardData['name'] as String;
            // 从塔罗牌数据中查找对应的卡牌
            final tarotCard = TarotCardsData.allCards.firstWhere(
              (card) => card.name == cardName,
              orElse: () => TarotCard(
                id: cardName.hashCode.toString(),
                name: cardName,
                description: '从后端加载的卡牌',
                meaning: '未知含义',
                keywords: [],
                imageUrl: '',
                isMajorArcana: false,
                isReversed: false,
              ),
            );
            cards.add(tarotCard);
          }

          final reading = TarotReading(
            id: data['id'] as String,
            question: data['question'] as String,
            spreadType: _parseSpreadType(data['spread_type'] as String? ?? 'three_card'),
            cards: cards,
            interpretation: data['interpretation'] as String,
            date: DateTime.parse(data['created_at'] as String),
            followUpQuestions: [],
            followUpResponses: [],
          );

          backendReadings.add(reading);
        } catch (e) {
          debugPrint('❌ 转换后端数据失败: $e');
        }
      }

      setState(() {
        _backendReadings = backendReadings;
        _isLoadingBackend = false;
      });

      debugPrint('✅ 加载了 ${backendReadings.length} 条后端塔罗解读');
    } catch (e) {
      debugPrint('❌ 加载后端塔罗解读失败: $e');
      setState(() {
        _isLoadingBackend = false;
      });
    }
  }

  // 🔧 解析牌阵类型
  SpreadType _parseSpreadType(String spreadTypeStr) {
    switch (spreadTypeStr) {
      case 'single':
        return SpreadType.single;
      case 'three_card':
        return SpreadType.three;
      case 'celtic_cross':
        return SpreadType.celtic;
      default:
        return SpreadType.three;
    }
  }







  // 下拉刷新
  Future<void> _onRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });
    
    try {
      final appState = Provider.of<AppStateProvider>(context, listen: false);
      await appState.refreshReadingHistory();

      // 🔧 同时刷新后端数据
      await _loadBackendReadings();
      
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('history_refreshed')),
              ],
            ),
            backgroundColor: const Color(0xFF4CAF50),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('refresh_failed')),
              ],
            ),
            backgroundColor: const Color(0xFFE53E3E),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context);
    final languageManager = Provider.of<LanguageManager>(context);

    // 🔧 合并本地和后端数据
    final localReadings = appState.readingHistory;
    final allReadings = <TarotReading>[...localReadings, ..._backendReadings];

    // 去重（基于ID）并按日期排序
    final uniqueReadings = <String, TarotReading>{};
    for (final reading in allReadings) {
      uniqueReadings[reading.id] = reading;
    }
    final readings = uniqueReadings.values.toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    // Filter readings based on search query and spread type
    final filteredReadings = readings.where((reading) {
      final query = _searchQuery.toLowerCase();
      final matchesSearch = reading.question.toLowerCase().contains(query) ||
             reading.interpretation.toLowerCase().contains(query);

      return matchesSearch;
    }).toList();

    // Sort by date (newest first)
    filteredReadings.sort((a, b) => b.date.compareTo(a.date));

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent, // 透明背景，使用HomeScreen的背景
      body: SafeArea(
        child: Column(
          children: [
            // 🍎 现代化顶部导航栏
            Container(
              height: 60,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(23),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const SizedBox(width: 16),
                  const Icon(
                    Icons.auto_awesome,
                    color: Color(0xFF2A404E),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      languageManager.translate('reading_retrospective'),
                      style: const TextStyle(
                        fontFamily: 'Circular Std',
                        fontSize: 22,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF2A404E),
                        height: 1.36,
                      ),
                    ),
                  ),
                  if (_isRefreshing)
                    Container(
                      margin: const EdgeInsets.only(right: 16),
                      width: 24,
                      height: 24,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2A404E)),
                      ),
                    )
                  else
                    IconButton(
                      onPressed: _onRefresh,
                      icon: const Icon(Icons.refresh, color: Color(0xFF2A404E)),
                      tooltip: languageManager.translate('refresh'),
                    ),



                ],
              ),
            ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.5, end: 0),
            
            // 主要内容区域 - 可滚动
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                backgroundColor: Colors.white,
                color: const Color(0xFF667eea),
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 10), // 进一步减少底部内边距
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [


                      // 统计信息卡片
                      _buildGoalStatsCards(languageManager),

                      const SizedBox(height: 24),

                      // 搜索栏和添加按钮
                      _buildSearchBarWithAddButton(languageManager),

                      const SizedBox(height: 24),

                      // 显化目标列表
                      _buildManifestationGoalsList(languageManager),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 🎯 精美的空状态页面 - 倒数第三层毛玻璃样式
  Widget _buildEmptyState(LanguageManager languageManager) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 移除书本图标
            
            const SizedBox(height: 24),
            
            Text(
              _searchQuery.isNotEmpty
                  ? languageManager.translate('no_matching_records')
                  : languageManager.translate('start_first_reading'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 400.ms, duration: 600.ms),

            const SizedBox(height: 12),

            Text(
              _searchQuery.isNotEmpty
                  ? languageManager.translate('adjust_search_filter')
                  : languageManager.translate('reading_history_display'),
              style: TextStyle(
                fontSize: 16,
                color: Colors.black.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 600.ms, duration: 600.ms),
            
            const SizedBox(height: 32),
            
            if (_searchQuery.isNotEmpty)
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: const Icon(Icons.clear_all, color: Colors.black),
                  label: Text(
                    languageManager.translate('clear_filter'),
                    style: const TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
                  ),
                ),
              ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
            
            const SizedBox(height: 16),
            
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: TextButton.icon(
                onPressed: _onRefresh,
                icon: const Icon(Icons.refresh, color: Colors.black),
                label: Text(
                  languageManager.translate('refresh_records'),
                  style: const TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
                ),
              ),
            ).animate().fadeIn(delay: 1000.ms, duration: 600.ms),
          ],
        ),
      ),
    );
  }

  // 📊 统计信息卡片 - 倒数第三层毛玻璃样式
  Widget _buildStatsCards(List readings, LanguageManager languageManager) {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              // 倒数第三层样式：更透明的毛玻璃效果
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(23),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageManager.translate('total_readings_count'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${readings.length}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              // 倒数第三层样式：更透明的毛玻璃效果
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(23),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageManager.translate('monthly_count'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_getMonthlyCount(readings)}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 🔍 搜索栏和添加按钮组合 - 专业UI设计
  Widget _buildSearchBarWithAddButton(LanguageManager languageManager) {
    return Consumer<ManifestationGoalService>(
      builder: (context, manifestationService, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // 搜索栏 - 缩短宽度
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(28),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 2.0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.15),
                        blurRadius: 25,
                        offset: const Offset(0, 6),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(28),
                    child: TextField(
                      onChanged: (value) => setState(() => _searchQuery = value),
                      decoration: InputDecoration(
                        hintText: languageManager.translate('search_reading_records'),
                        hintStyle: TextStyle(
                          color: Colors.black.withOpacity(0.6),
                          fontSize: 16,
                        ),
                        prefixIcon: Container(
                          padding: const EdgeInsets.all(12),
                          child: Icon(
                            Icons.search,
                            color: Colors.black.withOpacity(0.7),
                            size: 22,
                          ),
                        ),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? Container(
                                padding: const EdgeInsets.all(12),
                                child: GestureDetector(
                                  onTap: () => setState(() => _searchQuery = ''),
                                  child: Icon(
                                    Icons.clear,
                                    color: Colors.black.withOpacity(0.5),
                                    size: 20,
                                  ),
                                ),
                              )
                            : null,
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        filled: false,
                      ),
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // 苹果风格透明玻璃+按钮
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  // 透明玻璃效果
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(28),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    // 苹果风格阴影
                    BoxShadow(
                      color: Colors.white.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(28),
                    splashColor: Colors.white.withOpacity(0.1),
                    highlightColor: Colors.white.withOpacity(0.05),
                    onTap: () => _showAddGoalDialog(languageManager, manifestationService),
                    child: Center(
                      child: Icon(
                        Icons.add,
                        color: Colors.black.withOpacity(0.8),
                        size: 24,
                        weight: 600, // 更粗的线条，更苹果风格
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 📜 历史记录列表 - 倒数第三层毛玻璃样式
  Widget _buildReadingsList(List filteredReadings) {
    return Column(
      children: filteredReadings.map((reading) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            // 倒数第三层样式：更透明的毛玻璃效果
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(23),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ReadingHistoryItem(
            reading: reading,
          ),
        );
      }).toList(),
    );
  }

  void _showReadingDetails(dynamic reading) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                reading.question,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                DateFormat('yyyy-MM-dd HH:mm').format(reading.date),
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 16),
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    languageManager.translate('interpretation_content'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF4A5568),
                    ),
                  );
                },
              ),
              const SizedBox(height: 8),
              Text(
                reading.interpretation,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF2D3748),
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 24),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('关闭'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getMonthlyCount(List readings) {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);
    int count = 0;

    for (var reading in readings) {
      if (reading.date.year == currentMonth.year &&
          reading.date.month == currentMonth.month) {
        count++;
      }
    }

    return count;
  }

  String _getAverageRating(List readings) {
    if (readings.isEmpty) return '0.0';

    double totalRating = 0;
    int ratedCount = 0;

    for (var reading in readings) {
      if (reading.accuracy != null && reading.usefulness != null && reading.satisfaction != null) {
        totalRating += (reading.accuracy! + reading.usefulness! + reading.satisfaction!) / 3;
        ratedCount++;
      }
    }

    if (ratedCount == 0) return '0.0';
    return (totalRating / ratedCount).toStringAsFixed(1);
  }



  bool _hasRating(dynamic reading) {
    return reading.accuracy != null || reading.usefulness != null || reading.satisfaction != null;
  }

  String _getReadingRating(dynamic reading) {
    if (!_hasRating(reading)) return '0.0';
    
    double total = 0;
    int count = 0;
    
    if (reading.accuracy != null) {
      total += reading.accuracy!;
      count++;
    }
    if (reading.usefulness != null) {
      total += reading.usefulness!;
      count++;
    }
    if (reading.satisfaction != null) {
      total += reading.satisfaction!;
      count++;
    }
    
    if (count == 0) return '0.0';
    return (total / count).toStringAsFixed(1);
  }



  // 🌟 显化目标列表
  Widget _buildManifestationGoalsList(LanguageManager languageManager) {
    return Consumer<ManifestationGoalService>(
      builder: (context, manifestationService, child) {
        // 如果正在加载
        if (manifestationService.isLoading) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          );
        }

        // 如果有错误
        if (manifestationService.error != null) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red.shade300,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  manifestationService.error!,
                  style: TextStyle(
                    color: Colors.red.shade300,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    manifestationService.clearError();
                    manifestationService.initialize();
                  },
                  child: Text(languageManager.translate('retry')),
                ),
              ],
            ),
          );
        }

        final goals = manifestationService.searchGoals(_searchQuery);

        // 如果没有目标，显示空状态
        if (goals.isEmpty) {
          return _buildEmptyGoalsState(languageManager);
        }

        // 显示目标列表
        return Column(
          children: [
            // 目标列表
            ...goals.map((goal) => _buildGoalItem(goal, languageManager, manifestationService)),
          ],
        );
      },
    );
  }

  // 🌟 空状态显示
  Widget _buildEmptyGoalsState(LanguageManager languageManager) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.2),
            ),
            child: const Icon(
              Icons.auto_awesome,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            languageManager.translate('no_manifestation_goals'),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            languageManager.translate('create_first_goal_hint'),
            style: TextStyle(
              fontSize: 16,
              color: Colors.black.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 🌟 添加目标按钮
  Widget _buildAddGoalButton(LanguageManager languageManager, ManifestationGoalService manifestationService) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ElevatedButton.icon(
        onPressed: () => _showAddGoalDialog(languageManager, manifestationService),
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          languageManager.translate('add_manifestation_goal'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF6B46C1),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  // 🌟 显示添加目标对话框
  void _showAddGoalDialog(LanguageManager languageManager, ManifestationGoalService manifestationService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageManager.translate('add_manifestation_goal')),
        content: TextField(
          controller: _goalController,
          decoration: InputDecoration(
            hintText: languageManager.translate('enter_your_goal'),
            border: const OutlineInputBorder(),
          ),
          maxLines: 3,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(languageManager.translate('cancel')),
          ),
          ElevatedButton(
            onPressed: () => _addGoal(languageManager, manifestationService),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6B46C1),
            ),
            child: Text(
              languageManager.translate('add'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  // 🌟 添加目标
  void _addGoal(LanguageManager languageManager, ManifestationGoalService manifestationService) async {
    final title = _goalController.text.trim();
    if (title.isEmpty) return;

    try {
      await manifestationService.addGoal(title);
      _goalController.clear();

      if (mounted) {
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('goal_added_successfully')),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('添加目标失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 🌟 目标项目 - 优化UI宽度
  Widget _buildGoalItem(ManifestationGoal goal, LanguageManager languageManager, ManifestationGoalService manifestationService) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // 调整边距
      padding: const EdgeInsets.all(16), // 内边距
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.12), // 稍微降低透明度
        borderRadius: BorderRadius.circular(16), // 减少圆角，更紧凑
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.25),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主要内容行
          Row(
            children: [
              // 目标标题 - 移除左边图标，直接显示文字
              Expanded(
                child: Text(
                  goal.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
                ),
              ),

              // 生成肯定语按钮 - 更小更精致
              if (!goal.isAffirmationGenerated)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.amber.withValues(alpha: 0.25),
                      width: 0.8,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(8),
                      onTap: () => _generateMultipleAffirmations(goal.id, languageManager, manifestationService),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.auto_awesome,
                              color: Colors.amber,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              languageManager.translate('generate_5_affirmations'),
                              style: TextStyle(
                                color: Colors.amber.shade700,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // 底部信息行
          Row(
            children: [
              // 状态选择器
              _buildStatusSelector(goal, languageManager, manifestationService),
              const Spacer(),

              // 创建时间
              Text(
                DateFormat('MMM d, yyyy').format(goal.createdAt),
                style: TextStyle(
                  color: Colors.black.withValues(alpha: 0.5),
                  fontSize: 11,
                ),
              ),
            ],
          ),

          // 肯定语展示区域 - 支持多条肯定语和折叠
          if (goal.isAffirmationGenerated && goal.affirmation != null) ...[
            const SizedBox(height: 16),
            _buildMultipleAffirmationsWidget(goal, languageManager),
          ] else if (goal.syncStatus == SyncStatus.pending && !goal.isAffirmationGenerated) ...[
            // 显示加载状态
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.amber.withValues(alpha: 0.08),
                    Colors.amber.withValues(alpha: 0.04),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.amber.withValues(alpha: 0.15),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.amber.shade600),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    languageManager.translate('generating_affirmations'),
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.amber.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 🌟 苹果风格液体玻璃状态选择器 - 列表模式
  Widget _buildStatusSelector(ManifestationGoal goal, LanguageManager languageManager, ManifestationGoalService manifestationService) {
    return GestureDetector(
      onTap: () => _showStatusSelectionModal(goal, languageManager, manifestationService),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          // 液体玻璃效果
          color: Colors.white.withValues(alpha: 0.12),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 状态圆点指示器
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Color(goal.status.colorValue),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Color(goal.status.colorValue).withValues(alpha: 0.3),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            // 状态文本
            Text(
              _getShortStatusText(goal.status, languageManager),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.black.withValues(alpha: 0.8),
                letterSpacing: -0.1,
              ),
            ),
            const SizedBox(width: 6),
            // 下拉箭头
            Icon(
              Icons.keyboard_arrow_down_rounded,
              color: Colors.black.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // 🌟 液体玻璃状态选择模态框
  void _showStatusSelectionModal(ManifestationGoal goal, LanguageManager languageManager, ManifestationGoalService manifestationService) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (context) {
        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            // 液体玻璃模态框效果
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 30,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 模态框顶部指示器
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 36,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 20),

              // 标题
              Text(
                languageManager.translate('select_status'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black.withValues(alpha: 0.9),
                  letterSpacing: -0.3,
                ),
              ),

              const SizedBox(height: 20),

              // 状态选项列表
              ...ManifestationStatus.values.map((status) {
                final isSelected = goal.status == status;
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: () async {
                        if (status != goal.status) {
                          HapticFeedback.lightImpact();
                          Navigator.pop(context);

                          // 更新状态
                          await manifestationService.updateGoalStatus(goal.id, status);
                          setState(() {});
                        } else {
                          Navigator.pop(context);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Color(status.colorValue).withValues(alpha: 0.1)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(16),
                          border: isSelected ? Border.all(
                            color: Color(status.colorValue).withValues(alpha: 0.3),
                            width: 1,
                          ) : null,
                        ),
                        child: Row(
                          children: [
                            // 状态圆点
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: Color(status.colorValue),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(status.colorValue).withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            // 状态文本
                            Expanded(
                              child: Text(
                                _getShortStatusText(status, languageManager),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                  color: Colors.black.withValues(alpha: 0.9),
                                  letterSpacing: -0.2,
                                ),
                              ),
                            ),
                            // 选中指示器
                            if (isSelected)
                              Icon(
                                Icons.check_circle_rounded,
                                color: Color(status.colorValue),
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }),

              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }



  // 获取简短的状态文本 - 支持多语言
  String _getShortStatusText(ManifestationStatus status, LanguageManager languageManager) {
    switch (status) {
      case ManifestationStatus.pending:
        return languageManager.translate('manifestation_status_pending_short');
      case ManifestationStatus.manifesting:
        return languageManager.translate('manifestation_status_manifesting_short');
      case ManifestationStatus.manifested:
        return languageManager.translate('manifestation_status_manifested_short');
    }
  }

  // 🌟 苹果风格液态玻璃显化语卡片
  Widget _buildMultipleAffirmationsWidget(ManifestationGoal goal, LanguageManager languageManager) {
    // 解析多条肯定语
    final affirmations = goal.affirmation!.split('|||');
    final hasMultiple = affirmations.length > 1;

    // 使用Map来存储每个目标的状态
    final stateKey = goal.id;
    int currentIndex = _affirmationStates[stateKey]?['currentIndex'] ?? 0;
    bool isExpanded = _affirmationStates[stateKey]?['isExpanded'] ?? false;

    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            // 液态玻璃效果
            color: Colors.white.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.15),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 苹果风格简洁标题栏 - 只有向下图标
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  onTap: () {
                    // 苹果风格触觉反馈
                    HapticFeedback.lightImpact();
                    setState(() {
                      isExpanded = !isExpanded;
                      _affirmationStates[stateKey] = {
                        'currentIndex': currentIndex,
                        'isExpanded': isExpanded,
                      };
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      // 标题栏液态玻璃效果
                      color: Colors.white.withValues(alpha: 0.05),
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    child: Row(
                      children: [
                        // 苹果风格圆点指示器
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: const Color(0xFF007AFF).withValues(alpha: 0.8),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 简洁标题
                        Text(
                          languageManager.translate('affirmations'),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                            color: Colors.black.withValues(alpha: 0.85),
                            letterSpacing: -0.2,
                          ),
                        ),
                        if (hasMultiple) ...[
                          const SizedBox(width: 8),
                          // 数量标识
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: const Color(0xFF007AFF).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${affirmations.length}',
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF007AFF),
                              ),
                            ),
                          ),
                        ],
                        const Spacer(),
                        // 苹果风格展开图标
                        AnimatedRotation(
                          turns: isExpanded ? 0.5 : 0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOutCubic,
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.keyboard_arrow_down_rounded,
                              color: Colors.black.withValues(alpha: 0.6),
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // 苹果风格肯定语内容区域
              if (isExpanded) ...[
                // 液态玻璃分割线
                Container(
                  height: 0.5,
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        Colors.white.withValues(alpha: 0.2),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 肯定语文本区域 - 直接放置，移除内层容器
                      Padding(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 8),
                        child: Text(
                          affirmations[currentIndex].trim(),
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.black.withValues(alpha: 0.9),
                            height: 1.5,
                            fontWeight: FontWeight.w400,
                            letterSpacing: -0.1,
                          ),
                        ),
                      ),
                      // 底部空行区域，包含复制按钮
                      Container(
                        height: 44, // 空行高度
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        child: Row(
                          children: [
                            const Spacer(), // 推送按钮到右侧
                            // 复制按钮 - 位于空行右下角
                            GestureDetector(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                _copyAffirmation(affirmations[currentIndex].trim(), languageManager);
                              },
                              child: Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  // 液态玻璃透明背景
                                  color: Colors.white.withValues(alpha: 0.15),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 0.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.08),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.copy_rounded,
                                  color: Color(0xFF007AFF),
                                  size: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 苹果风格导航控制
                      if (hasMultiple) ...[
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 苹果风格上一条按钮
                            GestureDetector(
                              onTap: currentIndex > 0 ? () {
                                HapticFeedback.lightImpact();
                                setState(() {
                                  currentIndex--;
                                  _affirmationStates[stateKey] = {
                                    'currentIndex': currentIndex,
                                    'isExpanded': isExpanded,
                                  };
                                });
                              } : null,
                              child: Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: currentIndex > 0
                                      ? Colors.white.withValues(alpha: 0.8)
                                      : Colors.white.withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 0.5,
                                  ),
                                  boxShadow: currentIndex > 0 ? [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.08),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ] : null,
                                ),
                                child: Icon(
                                  Icons.chevron_left_rounded,
                                  color: currentIndex > 0
                                      ? const Color(0xFF007AFF)
                                      : Colors.grey.withValues(alpha: 0.5),
                                  size: 20,
                                ),
                              ),
                            ),

                            const SizedBox(width: 20),

                            // 苹果风格页码指示器
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: const Color(0xFF007AFF).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: const Color(0xFF007AFF).withValues(alpha: 0.2),
                                  width: 0.5,
                                ),
                              ),
                              child: Text(
                                '${currentIndex + 1} / ${affirmations.length}',
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: Color(0xFF007AFF),
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: -0.1,
                                ),
                              ),
                            ),

                            const SizedBox(width: 20),

                            // 苹果风格下一条按钮
                            GestureDetector(
                              onTap: currentIndex < affirmations.length - 1 ? () {
                                HapticFeedback.lightImpact();
                                setState(() {
                                  currentIndex++;
                                  _affirmationStates[stateKey] = {
                                    'currentIndex': currentIndex,
                                    'isExpanded': isExpanded,
                                  };
                                });
                              } : null,
                              child: Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: currentIndex < affirmations.length - 1
                                      ? Colors.white.withValues(alpha: 0.8)
                                      : Colors.white.withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 0.5,
                                  ),
                                  boxShadow: currentIndex < affirmations.length - 1 ? [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.08),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ] : null,
                                ),
                                child: Icon(
                                  Icons.chevron_right_rounded,
                                  color: currentIndex < affirmations.length - 1
                                      ? const Color(0xFF007AFF)
                                      : Colors.grey.withValues(alpha: 0.5),
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  // 🌟 生成多条肯定语
  void _generateMultipleAffirmations(String goalId, LanguageManager languageManager, ManifestationGoalService manifestationService) async {
    try {
      await manifestationService.generateMultipleAffirmations(goalId, count: 5);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('affirmations_generated_success')),
            backgroundColor: Colors.amber.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('generate_affirmations_failed')),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 🌟 复制肯定语
  void _copyAffirmation(String affirmation, LanguageManager languageManager) async {
    try {
      await Clipboard.setData(ClipboardData(text: affirmation));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('affirmation_copied')),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('copy_failed')),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // 📊 显化目标统计卡片
  Widget _buildGoalStatsCards(LanguageManager languageManager) {
    return Consumer<ManifestationGoalService>(
      builder: (context, manifestationService, child) {
        final goals = manifestationService.goals;
        final manifestedCount = goals.where((g) => g.status == ManifestationStatus.manifested).length;

        return Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(23),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageManager.translate('total_readings_count'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${goals.length}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(23),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageManager.translate('manifested_goals'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$manifestedCount',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }


}
