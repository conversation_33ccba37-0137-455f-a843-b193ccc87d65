import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/widgets/app_background.dart';
import 'package:ai_tarot_reading/widgets/gesture_interceptor.dart';
import 'package:ai_tarot_reading/screens/tarot_reading_screen.dart';
import 'package:ai_tarot_reading/screens/history_screen.dart';
import 'package:ai_tarot_reading/screens/daily_tarot_screen.dart';
import 'package:ai_tarot_reading/screens/user_account_screen.dart';
import 'package:ai_tarot_reading/screens/tarot_main_page.dart';
import 'dart:ui'; // 添加这个导入来支持ImageFilter

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context);
    final languageManager = Provider.of<LanguageManager>(context);

    // 获取屏幕尺寸和安全区域信息用于调试
    final mediaQuery = MediaQuery.of(context);
    final safeAreaBottom = mediaQuery.padding.bottom;
    final screenHeight = mediaQuery.size.height;

    // Update AppState language when locale changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 从语言代码中提取语言部分
      final languageCode = languageManager.currentLanguage.split('-').first;
      appState.updateLanguage(languageCode);
    });
    
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureInterceptor(
        child: AppBackground(
        child: Stack(
          children: [
            // 🌈 渐变背景 - 底层 (塔罗阅读页面配色) - 可选，用于增强效果
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFFE8F4FD).withOpacity(0.3), // 减少透明度，让背景图片更突出
                      const Color(0xFFF8E8FF).withOpacity(0.3), // 浅紫色  
                      const Color(0xFFFFE8F8).withOpacity(0.3), // 浅粉色
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
              ),
            ),
          
            // 🏗️ 主内容容器 - 包含页面内容和底部导航栏
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.only(top: 32, left: 16, right: 16, bottom: 32),
              child: Container(
                // 🐛 调试边框
                decoration: BoxDecoration(
                  // 更轻盈的液态玻璃效果 - 降低透明度让背景图片更清晰
                  color: Colors.white.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: Colors.red.withOpacity(0.3), // 临时调试边框 - 红色
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.15),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                    // 添加更细致的阴影层次
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 15,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(30),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15), // 为主容器添加毛玻璃效果
                    child: Column(
                      children: [
                        // 📱 页面内容区域 - 修复底部溢出问题
                        Expanded(
                          child: Container(
                            // 🐛 调试边框 - 可以在生产环境中移除
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.blue.withOpacity(0.5), width: 1),
                            ),
                            child: _buildCurrentScreen(appState.currentTabIndex),
                          ),
                        ),
                        
                        // ✨ 修复的嵌入式毛玻璃底部导航栏
                        _buildEmbeddedBottomBar(context, appState, safeAreaBottom),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentScreen(int index) {
    switch (index) {
      case 0:
        return const HistoryScreen();
      case 1:
        return const TarotMainPage();
      case 2:
        return const DailyTarotScreen();
      case 3:
        return const UserAccountScreen();
      default:
        return const TarotReadingScreen();
    }
  }

  Widget _buildNavItem(int index, IconData icon, String label, AppStateProvider appState) {
    final isSelected = appState.currentTabIndex == index;
    return GestureDetector(
      onTap: () => appState.setCurrentTab(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon, 
              color: isSelected ? Colors.black : Colors.black.withOpacity(0.7),
              size: 22,
            ),
            const SizedBox(height: 2),
            Text(
              label, 
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.black : Colors.black.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 修复的嵌入式毛玻璃底部导航栏 - 只显示导航栏本身
  Widget _buildEmbeddedBottomBar(BuildContext context, AppStateProvider appState, double safeAreaBottom) {
    // 只使用导航栏本身的高度，不包含安全区域
    const navBarHeight = 70.0; // 固定导航栏高度

    return ClipRRect(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(30),
        bottomRight: Radius.circular(30),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15), // 统一高斯模糊度，与主容器一致
        child: Container(
          height: navBarHeight, // 只使用导航栏高度
          decoration: BoxDecoration(
            // 液态玻璃效果 - 更高透明度，更轻盈
            color: Colors.white.withOpacity(0.2),
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1.5), // 白色玻璃质感边框
            // 添加液态玻璃的层次阴影效果
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.02),
                blurRadius: 6,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Colors.white.withOpacity(0.3),
                  width: 0.5,
                ),
              ),
            ),
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildNavItem(0, Icons.auto_awesome, languageManager.translate('nav_space'), appState),
                    _buildNavItem(1, Icons.auto_awesome, languageManager.translate('nav_tarot'), appState),
                    _buildNavItem(2, Icons.star, languageManager.translate('nav_manifestation'), appState),
                    _buildNavItem(3, Icons.person, languageManager.translate('nav_profile'), appState),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
