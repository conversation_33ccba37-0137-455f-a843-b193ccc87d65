import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/supabase_auth_service.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/screens/home_screen.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

/// 认证界面
/// 包含登录、注册和第三方登录功能
class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();

  bool _isLogin = true;
  bool _isLoading = false;
  bool _obscurePassword = true;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    _tabController.dispose();
    super.dispose();
  }







  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);
    
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                ],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    const SizedBox(height: 40),
                    _buildHeader(languageManager),
                    const SizedBox(height: 40),
                    _buildAuthCard(languageManager),
                    const SizedBox(height: 24),
                    _buildThirdPartyLogin(languageManager),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageManager languageManager) {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/cloud_background.png', // 使用首页的背景PNG女孩头像
              width: 120,
              height: 120,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // 如果图片加载失败，回退到原来的图标
                return Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.9),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    size: 60,
                    color: FigmaTheme.primaryPink,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 24),
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Text(
              languageManager.translate('ai_tarot_divination'),
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.3),
                    offset: const Offset(0, 2),
                    blurRadius: 4,
                  ),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 8),
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Text(
              languageManager.translate('explore_inner_mysterious_world'),
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.9),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAuthCard(LanguageManager languageManager) {
    return Container(
      decoration: BoxDecoration(
        // 透明度高的白色磨砂玻璃效果
        color: Colors.white.withValues(alpha: 0.25),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          // 添加内发光效果
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildTabBar(),
          _buildAuthForm(languageManager),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: FigmaTheme.primaryPink,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        dividerColor: Colors.transparent,
        onTap: (index) {
          setState(() {
            _isLogin = index == 0;
          });
        },
        tabs: const [
          Tab(text: '登录'),
          Tab(text: '注册'),
        ],
      ),
    );
  }

  Widget _buildAuthForm(LanguageManager languageManager) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            if (!_isLogin) ...[
              _buildNameField(),
              const SizedBox(height: 16),
            ],
            _buildEmailField(),
            const SizedBox(height: 16),
            _buildPasswordField(),
            const SizedBox(height: 24),
            _buildSubmitButton(),
            if (_isLogin) ...[
              const SizedBox(height: 16),
              _buildForgotPasswordButton(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: languageManager.translate('name'),
            prefixIcon: const Icon(Icons.person),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (!_isLogin && (value == null || value.isEmpty)) {
              return languageManager.translate('enter_name');
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildEmailField() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
            labelText: languageManager.translate('email'),
            prefixIcon: const Icon(Icons.email),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return languageManager.translate('enter_email');
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return languageManager.translate('enter_valid_email');
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildPasswordField() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return TextFormField(
          controller: _passwordController,
          obscureText: _obscurePassword,
          decoration: InputDecoration(
            labelText: languageManager.translate('password'),
            prefixIcon: const Icon(Icons.lock),
            suffixIcon: IconButton(
              icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return languageManager.translate('enter_password');
            }
            if (!_isLogin && value.length < 6) {
              return languageManager.translate('password_min_length');
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 56, // 增加高度确保文字显示完全
      decoration: BoxDecoration(
        // 透明毛玻璃效果
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
          // 内发光效果
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    _isLogin ? languageManager.translate('login') : languageManager.translate('register'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white, // 确保文字颜色为白色
                    ),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildForgotPasswordButton() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return TextButton(
          onPressed: _showForgotPasswordDialog,
          child: Text(
            languageManager.translate('forgot_password'),
            style: const TextStyle(
              color: FigmaTheme.primaryPink,
            ),
          ),
        );
      },
    );
  }

  Widget _buildThirdPartyLogin(LanguageManager languageManager) {
    return Column(
      children: [
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Row(
              children: [
                const Expanded(child: Divider(color: Colors.white)),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    languageManager.translate('or'),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const Expanded(child: Divider(color: Colors.white)),
              ],
            );
          },
        ),
        const SizedBox(height: 16),
        _buildAppleSignInButton(),
      ],
    );
  }

  Widget _buildAppleSignInButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        // 透明毛玻璃效果 - 深色版本
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
          // 内发光效果
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _handleAppleSignIn,
        icon: const Icon(Icons.apple, size: 24),
        label: Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Text(
              languageManager.translate('sign_in_with_apple_id'),
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
                color: Colors.white,
              ),
              maxLines: 1,
              overflow: TextOverflow.visible,
            );
          },
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    try {
      final authService = Provider.of<SupabaseAuthService>(context, listen: false);

      if (_isLogin) {
        await authService.signInWithEmail(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );
      } else {
        await authService.signUpWithEmail(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          fullName: _nameController.text.trim(),
        );
      }

      if (mounted) {
        // 登录成功后跳转到主页
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isLogin ? languageManager.translate('login_success') : languageManager.translate('register_success')),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('${_isLogin ? 'login' : 'register'}_failed') + ': $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleAppleSignIn() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      // 检查Apple Sign In可用性
      final isAvailable = await SignInWithApple.isAvailable();

      if (!isAvailable) {
        throw Exception('Apple Sign In is not available on this device');
      }

      // 获取Apple凭证
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // 与Supabase集成
      final authService = Provider.of<SupabaseAuthService>(context, listen: false);
      final response = await authService.signInWithAppleCredential(credential);

      if (response.user != null) {

        if (mounted) {
          // Apple登录成功后跳转到主页
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(languageManager.translate('apple_sign_in_success')),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Authentication failed: User is null');
      }

    } catch (e) {

      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageManager.translate('apple_sign_in_failed') + ': $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showForgotPasswordDialog() {
    final emailController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置密码'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('请输入您的邮箱地址，我们将发送重置密码的链接。'),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: '邮箱',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final authService = Provider.of<SupabaseAuthService>(context, listen: false);
                await authService.resetPassword(emailController.text.trim());
                
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('重置密码邮件已发送！'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('发送失败: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('发送'),
          ),
        ],
      ),
    );
  }
}
