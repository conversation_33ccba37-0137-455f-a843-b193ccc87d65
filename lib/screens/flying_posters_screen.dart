import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import '../utils/language_manager.dart';
import 'auth_screen.dart';

class FlyingPostersScreen extends StatefulWidget {
  const FlyingPostersScreen({super.key});

  @override
  State<FlyingPostersScreen> createState() => _FlyingPostersScreenState();
}

class _FlyingPostersScreenState extends State<FlyingPostersScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fall;
  late Animation<double> _rotation;
  late Animation<double> _scale;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    // 从屏幕顶部 (-300) 到 0
    _fall = Tween<double>(begin: -300, end: 0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutBack),
    );

    // 左右轻微旋转
    _rotation = Tween<double>(begin: -0.2, end: 0.05).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // 缩放
    _scale = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    // 启动动画
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _navigateToAuth() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const AuthScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // 天蓝色
              Color(0xFFE6E6FA), // 淡紫色
              Color(0xFFF8BBD9), // 粉色
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // 背景装饰
              _buildBackgroundDecorations(),
              
              // 主要内容
              Center(
                child: AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _fall.value),
                      child: Transform.rotate(
                        angle: _rotation.value,
                        child: Transform.scale(
                          scale: _scale.value,
                          child: child,
                        ),
                      ),
                    );
                  },
                  child: _buildParchmentPoster(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundDecorations() {
    return Stack(
      children: [
        // 🌟 飘落的魔法星星
        ...List.generate(15, (index) {
          final random = Random(index);
          return Positioned(
            left: (index * 60.0) % MediaQuery.of(context).size.width,
            top: (index * 100.0) % MediaQuery.of(context).size.height,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                final offset = sin(_controller.value * 2 * pi + index) * 20;
                return Transform.translate(
                  offset: Offset(offset, 0),
                  child: Transform.rotate(
                    angle: _controller.value * 2 * pi + index,
                    child: Icon(
                      Icons.auto_awesome,
                      color: Colors.white.withValues(alpha: 0.4 + random.nextDouble() * 0.3),
                      size: 12 + (index % 4) * 6,
                    ),
                  ),
                );
              },
            ),
          );
        }),

        // ✨ 飘落的金色光点
        ...List.generate(10, (index) {
          final random = Random(index + 100);
          return Positioned(
            left: random.nextDouble() * MediaQuery.of(context).size.width,
            top: random.nextDouble() * MediaQuery.of(context).size.height,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                final float = sin(_controller.value * pi + index * 0.5) * 30;
                return Transform.translate(
                  offset: Offset(0, float),
                  child: Container(
                    width: 4 + random.nextDouble() * 6,
                    height: 4 + random.nextDouble() * 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: const Color(0xFFFFD700).withValues(
                        alpha: 0.3 + random.nextDouble() * 0.4,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFFFD700).withValues(alpha: 0.2),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }),

        // 🍃 飘落的羽毛效果
        ...List.generate(8, (index) {
          final random = Random(index + 200);
          return Positioned(
            left: random.nextDouble() * MediaQuery.of(context).size.width,
            top: random.nextDouble() * MediaQuery.of(context).size.height * 0.3,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                final sway = sin(_controller.value * 1.5 * pi + index) * 40;
                final fall = _controller.value * 200 + index * 50;
                return Transform.translate(
                  offset: Offset(sway, fall % MediaQuery.of(context).size.height),
                  child: Transform.rotate(
                    angle: _controller.value * pi + index,
                    child: Container(
                      width: 2,
                      height: 15 + random.nextDouble() * 10,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ],
    );
  }

  Widget _buildParchmentPoster() {
    final languageManager = Provider.of<LanguageManager>(context);

    return ParchmentPoster(
      onTap: _navigateToAuth,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 🏰 霍格沃茨风格顶部装饰
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const RadialGradient(
                colors: [
                  Color(0xFFFFD700), // 金色中心
                  Color(0xFFB8860B), // 深金色
                  Color(0xFF8B7355), // 古铜色边缘
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
                BoxShadow(
                  color: const Color(0xFFFFD700).withValues(alpha: 0.2),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 40,
            ),
          ),

          const SizedBox(height: 20),

          // 📜 霍格沃茨风格标题
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                // 装饰线
                Container(
                  height: 2,
                  width: 100,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Colors.transparent,
                        Color(0xFF8B4513),
                        Colors.transparent,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),

                const SizedBox(height: 16),

                // 主标题
                Text(
                  languageManager.translate('connect_higher_self'),
                  style: const TextStyle(
                    fontSize: 26,
                    color: Color(0xFF654321),
                    fontWeight: FontWeight.w700,
                    letterSpacing: 1.2,
                    shadows: [
                      Shadow(
                        color: Color(0x60000000),
                        offset: Offset(2, 2),
                        blurRadius: 4,
                      ),
                      Shadow(
                        color: Color(0x20FFD700),
                        offset: Offset(-1, -1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // 装饰线
                Container(
                  height: 1,
                  width: 80,
                  color: const Color(0xFF8B4513).withValues(alpha: 0.5),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // 📖 优雅的副标题
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              languageManager.translate('discover_inner_wisdom'),
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xFF8B7355),
                height: 1.6,
                fontStyle: FontStyle.italic,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(height: 24),

          // ✨ 魔法提示文字
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFFFFD700).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFFFFD700).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              languageManager.translate('tap_to_begin_journey'),
              style: const TextStyle(
                fontSize: 11,
                color: Color(0xFF654321),
                fontWeight: FontWeight.w500,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

// 羊皮纸海报组件
class ParchmentPoster extends StatelessWidget {
  final double width;
  final double height;
  final VoidCallback onTap;
  final Widget? child;

  const ParchmentPoster({
    super.key,
    this.width = 300,
    this.height = 400,
    required this.onTap,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CustomPaint(
        size: Size(width, height),
        painter: ParchmentPainter(),
        child: Container(
          alignment: Alignment.center,
          width: width,
          height: height,
          padding: const EdgeInsets.all(24),
          child: child,
        ),
      ),
    );
  }
}

// 霍格沃茨风格羊皮纸画家
class ParchmentPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Path path = Path();
    final random = Random(42); // 固定种子确保一致性

    // 🎭 创建更加不规则的霍格沃茨邀请函轮廓
    path.moveTo(15, 25);

    // 左边缘 - 更多波浪和不规则
    for (int i = 0; i < 8; i++) {
      double y = (i + 1) * size.height / 9;
      double x = 5 + random.nextDouble() * 15;
      if (i % 2 == 0) {
        path.quadraticBezierTo(x - 5, y - 10, x, y);
      } else {
        path.quadraticBezierTo(x + 5, y - 10, x, y);
      }
    }

    // 底边 - 撕裂效果
    for (int i = 0; i < 6; i++) {
      double x = (i + 1) * size.width / 7;
      double y = size.height - 5 - random.nextDouble() * 15;
      path.quadraticBezierTo(x - 10, y + 5, x, y);
    }

    // 右边缘 - 烧焦效果
    for (int i = 7; i >= 0; i--) {
      double y = i * size.height / 8;
      double x = size.width - 5 - random.nextDouble() * 20;
      if (i % 2 == 0) {
        path.quadraticBezierTo(x + 8, y + 8, x, y);
      } else {
        path.quadraticBezierTo(x - 8, y + 8, x, y);
      }
    }

    // 顶边 - 古老撕裂
    for (int i = 5; i >= 0; i--) {
      double x = i * size.width / 6;
      double y = 8 + random.nextDouble() * 12;
      path.quadraticBezierTo(x + 8, y - 5, x, y);
    }

    path.close();

    // 🎨 多层渐变营造古老质感
    // 基础羊皮纸色
    final Paint basePaint = Paint()
      ..shader = const RadialGradient(
        center: Alignment(0.3, -0.2),
        radius: 1.2,
        colors: [
          Color(0xFFF5E6D3), // 中心亮色
          Color(0xFFE8D5B7), // 中间色
          Color(0xFFD4C4A8), // 边缘暗色
          Color(0xFFC8B99C), // 最外层
        ],
        stops: [0.0, 0.4, 0.7, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, basePaint);

    // 🔥 添加烧焦边缘效果
    final Paint burnPaint = Paint()
      ..shader = const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.transparent,
          Color(0x20654321),
          Color(0x40654321),
          Color(0x60654321),
        ],
        stops: [0.0, 0.7, 0.85, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8;

    canvas.drawPath(path, burnPaint);

    // ✨ 添加古老纸张纹理
    _drawPaperTexture(canvas, size, path);

    // 🏰 绘制霍格沃茨风格装饰边框
    _drawHogwartsFrame(canvas, size);

    // 📜 添加蜡封效果
    _drawWaxSeal(canvas, size);

    // 🌟 添加魔法光效
    _drawMagicalGlow(canvas, size);
  }

  // 📄 绘制纸张纹理
  void _drawPaperTexture(Canvas canvas, Size size, Path clipPath) {
    canvas.save();
    canvas.clipPath(clipPath);

    final random = Random(123);
    final texturePaint = Paint()
      ..color = const Color(0x08654321)
      ..strokeWidth = 0.5;

    // 添加随机纹理线条
    for (int i = 0; i < 200; i++) {
      final start = Offset(
        random.nextDouble() * size.width,
        random.nextDouble() * size.height,
      );
      final end = Offset(
        start.dx + (random.nextDouble() - 0.5) * 20,
        start.dy + (random.nextDouble() - 0.5) * 20,
      );
      canvas.drawLine(start, end, texturePaint);
    }

    canvas.restore();
  }

  // 🏰 绘制霍格沃茨装饰边框
  void _drawHogwartsFrame(Canvas canvas, Size size) {
    final framePaint = Paint()
      ..color = const Color(0x30654321)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // 内边框
    final innerFrame = RRect.fromRectAndRadius(
      Rect.fromLTWH(20, 20, size.width - 40, size.height - 40),
      const Radius.circular(8),
    );
    canvas.drawRRect(innerFrame, framePaint);

    // 装饰角落
    _drawCornerDecorations(canvas, size);
  }

  // 🎭 绘制装饰角落
  void _drawCornerDecorations(Canvas canvas, Size size) {
    final decorPaint = Paint()
      ..color = const Color(0x40654321)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // 四个角落的装饰
    final corners = [
      const Offset(30, 30), // 左上
      Offset(size.width - 30, 30), // 右上
      Offset(30, size.height - 30), // 左下
      Offset(size.width - 30, size.height - 30), // 右下
    ];

    for (final corner in corners) {
      // 绘制小装饰图案
      canvas.drawCircle(corner, 3, decorPaint);
      canvas.drawLine(
        Offset(corner.dx - 8, corner.dy),
        Offset(corner.dx + 8, corner.dy),
        decorPaint,
      );
      canvas.drawLine(
        Offset(corner.dx, corner.dy - 8),
        Offset(corner.dx, corner.dy + 8),
        decorPaint,
      );
    }
  }

  // 🔴 绘制蜡封
  void _drawWaxSeal(Canvas canvas, Size size) {
    final sealCenter = Offset(size.width - 50, 50);

    // 蜡封背景
    final sealPaint = Paint()
      ..shader = const RadialGradient(
        colors: [
          Color(0xFFDC143C), // 深红色中心
          Color(0xFF8B0000), // 暗红色边缘
        ],
      ).createShader(Rect.fromCircle(center: sealCenter, radius: 20));

    canvas.drawCircle(sealCenter, 18, sealPaint);

    // 蜡封图案 (简化的霍格沃茨徽章)
    final patternPaint = Paint()
      ..color = const Color(0x80FFD700)
      ..strokeWidth = 1;

    // 绘制简单的盾牌形状
    final shieldPath = Path()
      ..moveTo(sealCenter.dx, sealCenter.dy - 10)
      ..lineTo(sealCenter.dx - 8, sealCenter.dy - 5)
      ..lineTo(sealCenter.dx - 8, sealCenter.dy + 5)
      ..lineTo(sealCenter.dx, sealCenter.dy + 10)
      ..lineTo(sealCenter.dx + 8, sealCenter.dy + 5)
      ..lineTo(sealCenter.dx + 8, sealCenter.dy - 5)
      ..close();

    canvas.drawPath(shieldPath, patternPaint);
  }

  // ✨ 绘制魔法光效
  void _drawMagicalGlow(Canvas canvas, Size size) {
    final glowPaint = Paint()
      ..shader = const RadialGradient(
        colors: [
          Color(0x20FFD700),
          Color(0x10FFD700),
          Colors.transparent,
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    // 添加几个光点
    final glowPoints = [
      Offset(size.width * 0.2, size.height * 0.3),
      Offset(size.width * 0.7, size.height * 0.2),
      Offset(size.width * 0.8, size.height * 0.7),
      Offset(size.width * 0.3, size.height * 0.8),
    ];

    for (final point in glowPoints) {
      canvas.drawCircle(point, 15, glowPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
