import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/widgets/daily_tarot_card.dart';
import 'package:ai_tarot_reading/screens/manifestation_goal_screen.dart';
import 'package:ai_tarot_reading/screens/manifestation_animation_screen.dart';
import 'package:ai_tarot_reading/screens/card_shuffle_screen.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/services/diary_service.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/models/diary_entry.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/services/diary_service.dart';
import 'package:ai_tarot_reading/services/chat_summary_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:ai_tarot_reading/l10n/app_localizations.dart';

class DailyTarotScreen extends StatefulWidget {
  const DailyTarotScreen({super.key});

  @override
  State<DailyTarotScreen> createState() => _DailyTarotScreenState();
}

class _DailyTarotScreenState extends State<DailyTarotScreen>
    with TickerProviderStateMixin {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  bool _isCalendarExpanded = true;
  List<DiaryEntry> _selectedDateDiaries = [];
  List<ChatSummary> _selectedDateChatSummaries = [];
  bool _isLoadingDateData = false;
  final TextEditingController _manifestationJournalController = TextEditingController();
  String _currentManifestationJournal = '';

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    // 初始化本地化数据
    initializeDateFormatting();
    _loadManifestationJournal(_selectedDay!);
  }

  @override
  void dispose() {
    _manifestationJournalController.dispose();
    super.dispose();
  }

  // 加载显化日记
  Future<void> _loadManifestationJournal(DateTime date) async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('用户未登录，无法加载显化日记');
        return;
      }

      // 查找该日期的显化日记
      final diaries = await DiaryService.getDiariesByDate(
        userId: user.id,
        date: date,
      );

      // 查找显化日记（标签包含'manifestation'或内容包含显化相关关键词）
      final manifestationDiary = diaries.firstWhere(
        (diary) => diary.tags.contains('manifestation') ||
                   diary.tags.contains('显化') ||
                   diary.content.contains('显化'),
        orElse: () => DiaryEntry(
          id: '',
          userId: user.id,
          content: '',
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      setState(() {
        _currentManifestationJournal = manifestationDiary.content;
        _manifestationJournalController.text = manifestationDiary.content;
      });
    } catch (e) {
      debugPrint('加载显化日记失败: $e');
    }
  }

  // 保存显化日记
  Future<void> _saveManifestationJournal() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('请先登录'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        return;
      }

      final content = _manifestationJournalController.text.trim();

      if (content.isNotEmpty) {
        // 保存到数据库（同一天覆盖更新）
        final diaryEntry = await DiaryService.saveOrUpdateManifestationJournal(
          content: content,
          date: _selectedDay ?? DateTime.now(),
        );

        if (diaryEntry != null) {
          setState(() {
            _currentManifestationJournal = content;
          });

          // 显示保存成功提示
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('显化日记已保存到云端'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        } else {
          throw Exception('保存失败');
        }
      } else {
        // 如果内容为空，可以选择删除或不做处理
        setState(() {
          _currentManifestationJournal = '';
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('显化日记已清空'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('保存显化日记失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('保存失败，请检查网络连接'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageManager = Provider.of<LanguageManager>(context);

    return Consumer2<AppStateProvider, BlurSettingsService>(
      builder: (context, appState, blurSettings, child) {
        return Scaffold(
          backgroundColor: Colors.transparent, // 改为透明背景
          body: SafeArea(
            child: Column(
              children: [
                // 日历区域 - 可折叠（固定在顶部）
                Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15), // 改为半透明毛玻璃
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: blurSettings.getImageFilter(), // 使用动态模糊度
                      child: Column(
                        children: [
                          // 日历头部 - 添加折叠按钮
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.vertical(
                                top: const Radius.circular(16),
                                bottom: Radius.circular(_isCalendarExpanded ? 0 : 16),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  _formatCalendarHeader(_focusedDay, languageManager),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black, // 改为黑色
                                  ),
                                ),
                                Row(
                                  children: [
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          _focusedDay = DateTime(_focusedDay.year, _focusedDay.month - 1);
                                        });
                                      },
                                      icon: const Icon(Icons.chevron_left),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + 1);
                                        });
                                      },
                                      icon: const Icon(Icons.chevron_right),
                                    ),
                                    // 更明显的折叠按钮
                                    Container(
                                      margin: const EdgeInsets.only(left: 8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Theme.of(context).primaryColor.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _isCalendarExpanded = !_isCalendarExpanded;
                                          });
                                        },
                                        icon: AnimatedRotation(
                                          turns: _isCalendarExpanded ? 0 : 0.5,
                                          duration: const Duration(milliseconds: 300),
                                          child: Icon(
                                            Icons.expand_less,
                                            color: Theme.of(context).primaryColor,
                                            size: 24,
                                          ),
                                        ),
                                        tooltip: _isCalendarExpanded ? '折叠日历' : '展开日历',
                                      ),
                                    ),

                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          // 日历主体 - 可折叠
                          if (_isCalendarExpanded)
                            TableCalendar<DailyTarot>(
                              firstDay: DateTime.utc(2020, 1, 1),
                              lastDay: DateTime.utc(2030, 12, 31),
                              focusedDay: _focusedDay,
                              calendarFormat: _calendarFormat,
                              eventLoader: (day) {
                                final dayString = DateFormat('yyyy-MM-dd').format(day);
                                final dailyTarot = appState.dailyTarotReadings[dayString];
                                return dailyTarot != null && dailyTarot.isDrawn ? [dailyTarot] : [];
                              },
                              startingDayOfWeek: StartingDayOfWeek.monday,
                              calendarStyle: CalendarStyle(
                                outsideDaysVisible: false,
                                selectedDecoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                todayDecoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor.withOpacity(0.6),
                                  shape: BoxShape.circle,
                                ),
                                markerDecoration: const BoxDecoration(
                                  color: Colors.amber,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              headerStyle: const HeaderStyle(
                                formatButtonVisible: false,
                                titleCentered: true,
                              ),
                              onDaySelected: (selectedDay, focusedDay) {
                                setState(() {
                                  _selectedDay = selectedDay;
                                  _focusedDay = focusedDay;
                                });
                                _showDailyTarotEntry(selectedDay, appState);
                                _loadManifestationJournal(selectedDay);
                              },
                              onFormatChanged: (format) {
                                setState(() {
                                  _calendarFormat = format;
                                });
                              },
                              onPageChanged: (focusedDay) {
                                setState(() {
                                  _focusedDay = focusedDay;
                                });
                              },
                              selectedDayPredicate: (day) {
                                return isSameDay(_selectedDay, day);
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // 可滚动内容区域
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        // 显化功能区域 - 液态玻璃毛玻璃UI
                        ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: BackdropFilter(
                            filter: blurSettings.getImageFilter(), // 使用动态模糊度
                            child: Container(
                              padding: const EdgeInsets.all(24), // 增加内边距
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15), // 高透明度毛玻璃
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.4),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.white.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 40,
                                    offset: const Offset(0, 12),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.auto_awesome,
                                        color: Colors.black,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        languageManager.translate('manifestation_practice'),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    languageManager.translate('manifestation_description'),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 20), // 增加间距
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Container(
                                          height: 44, // 增加按钮高度
                                          decoration: BoxDecoration(
                                            gradient: const LinearGradient(
                                              colors: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
                                            ),
                                            borderRadius: BorderRadius.circular(12), // 稍微增加圆角
                                            boxShadow: [
                                              BoxShadow(
                                                color: const Color(0xFF8B5CF6).withOpacity(0.3),
                                                blurRadius: 8,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: ElevatedButton(
                                            onPressed: () => _showManifestationGoalSelection(context, appState),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.transparent,
                                              foregroundColor: Colors.white,
                                              elevation: 0,
                                              shadowColor: Colors.transparent,
                                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 增加内边距
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                            ),
                                            child: Text(
                                              languageManager.translate('choose_goal'),
                                              style: const TextStyle(
                                                fontSize: 15, // 稍微增加字体
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16), // 增加按钮间距
                                      Expanded(
                                        child: Container(
                                          height: 44, // 增加按钮高度
                                          decoration: BoxDecoration(
                                            color: Colors.white.withOpacity(0.2),
                                            borderRadius: BorderRadius.circular(12), // 稍微增加圆角
                                            border: Border.all(
                                              color: Colors.white.withOpacity(0.5),
                                              width: 1.5,
                                            ),
                                          ),
                                          child: OutlinedButton(
                                            onPressed: () => _startMindfulnessPractice(context, appState),
                                            style: OutlinedButton.styleFrom(
                                              backgroundColor: Colors.transparent,
                                              side: BorderSide.none,
                                              padding: EdgeInsets.zero, // 移除内边距，让Container控制尺寸
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                            ),
                                            child: Container(
                                              width: double.infinity,
                                              height: double.infinity,
                                              alignment: Alignment.center, // 确保文字居中
                                              child: Text(
                                                languageManager.translate('mindfulness_practice'),
                                                style: const TextStyle(
                                                  fontSize: 15,
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                                textAlign: TextAlign.center, // 文字居中对齐
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4), // 在按钮下方增加一点空间
                                ],
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 20),

                        // 显化日记区域 - 透明高斯玻璃效果
                        Consumer<BlurSettingsService>(
                          builder: (context, blurSettings, child) {
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: BackdropFilter(
                                filter: blurSettings.getImageFilter(),
                                child: Container(
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: Colors.white.withValues(alpha: 0.4),
                                      width: 1.5,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.white.withValues(alpha: 0.2),
                                        blurRadius: 20,
                                        offset: const Offset(0, 8),
                                      ),
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 40,
                                        offset: const Offset(0, 12),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.auto_stories,
                                            color: Colors.black.withValues(alpha: 0.7),
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Consumer<LanguageManager>(
                                            builder: (context, languageManager, child) {
                                              return Text(
                                                languageManager.translate('manifestation_diary'),
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.black.withValues(alpha: 0.8),
                                                ),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      Consumer<LanguageManager>(
                                        builder: (context, languageManager, child) {
                                          return TextField(
                                            controller: _manifestationJournalController,
                                            maxLines: 4,
                                            decoration: InputDecoration(
                                              hintText: '${languageManager.translate('record_manifestation_experience')}\n${languageManager.translate('diary_example')}',
                                              hintStyle: TextStyle(
                                                color: Colors.black.withValues(alpha: 0.4),
                                                fontSize: 14,
                                              ),
                                              border: OutlineInputBorder(
                                                borderRadius: BorderRadius.circular(12),
                                                borderSide: BorderSide(
                                                  color: Colors.white.withValues(alpha: 0.3),
                                                  width: 1,
                                                ),
                                              ),
                                              enabledBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.circular(12),
                                                borderSide: BorderSide(
                                                  color: Colors.white.withValues(alpha: 0.3),
                                                  width: 1,
                                                ),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius: BorderRadius.circular(12),
                                                borderSide: BorderSide(
                                                  color: Colors.black.withValues(alpha: 0.3),
                                                  width: 2,
                                                ),
                                              ),
                                              filled: true,
                                              fillColor: Colors.white.withValues(alpha: 0.1),
                                              contentPadding: const EdgeInsets.all(16),
                                            ),
                                            style: TextStyle(
                                              color: Colors.black.withValues(alpha: 0.8),
                                              fontSize: 14,
                                              height: 1.4,
                                            ),
                                          );
                                        },
                                      ),
                                      const SizedBox(height: 12),
                                      // 保存按钮
                                      Align(
                                        alignment: Alignment.centerRight,
                                        child: Consumer<LanguageManager>(
                                          builder: (context, languageManager, child) {
                                            return ElevatedButton.icon(
                                              onPressed: _saveManifestationJournal,
                                              icon: const Icon(Icons.save, size: 16),
                                              label: Text(
                                                languageManager.translate('save'),
                                                style: const TextStyle(fontSize: 12),
                                              ),
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: Colors.black.withValues(alpha: 0.7),
                                                foregroundColor: Colors.white,
                                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius: BorderRadius.circular(20),
                                                ),
                                                elevation: 2,
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 20), // 底部间距
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTodaysTarotCard(AppStateProvider appState, AppLocalizations? l10n) {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final dailyTarot = appState.dailyTarotReadings[today];

    return DailyTarotCard(
      dailyTarot: dailyTarot,
      selectedDate: DateTime.now(),
      onDrawCard: () => _startDailyTarotShuffle(appState),
      onRedraw: () => _redrawDailyTarot(appState),
      isToday: true,
    );
  }

  // 启动每日塔罗的洗牌抽卡流程
  void _startDailyTarotShuffle(AppStateProvider appState) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CardShuffleScreen(
          question: '今日塔罗指引',
          spreadType: '单张牌阵',
          autoMode: false,
          currentPosition: 0,
          onCardSelected: (selectedCard) {
            _saveDailyTarotCard(selectedCard, appState);
          },
        ),
      ),
    );
  }

  // 保存每日塔罗卡牌
  void _saveDailyTarotCard(TarotCard selectedCard, AppStateProvider appState) {
    appState.drawDailyTarotWithSpecificCard(selectedCard);
    if (mounted) {
      setState(() {});
    }
  }

  // 重新抽取每日塔罗
  void _redrawDailyTarot(AppStateProvider appState) {
    _startDailyTarotShuffle(appState);
  }

  // 加载指定日期的日记和聊天摘要
  Future<void> _loadDiariesForDate(DateTime date) async {
    setState(() {
      _isLoadingDateData = true;
    });

    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      debugPrint('🔍 查询用户ID: $userId');
      debugPrint('🔍 查询日期: ${date.toString().substring(0, 10)}');

      if (userId != null) {
        // 获取指定日期的日记
        final diaries = await DiaryService.getDiariesByDate(
          userId: userId,
          date: date,
        );

        // 获取指定日期的聊天摘要（从higher_self_memories表）
        final chatSummaries = await ChatSummaryService.getChatSummariesByDate(
          userId: userId,
          date: date,
        );

        // 获取指定日期的聊天摘要（从diary_entries表）
        final diaryChatSummaries = await DiaryService.getChatSummariesByDate(
          userId: userId,
          date: date,
        );

        // 将DiaryEntry转换为ChatSummary格式
        final convertedDiarySummaries = diaryChatSummaries.map((diary) => ChatSummary(
          id: diary.id,
          userId: diary.userId,
          content: diary.chatSummary ?? diary.content,
          chatSessionId: diary.chatSessionId,
          fullConversation: diary.content,
          createdAt: diary.createdAt,
        )).toList();

        // 合并两个表的聊天摘要
        final allChatSummaries = [...chatSummaries, ...convertedDiarySummaries];

        debugPrint('📅 ${date.toString().substring(0, 10)} 的日记: ${diaries.length} 条');
        debugPrint('💬 ${date.toString().substring(0, 10)} 的聊天摘要: ${allChatSummaries.length} 条');

        // 如果没有聊天摘要，尝试查询所有聊天摘要来调试
        if (allChatSummaries.isEmpty) {
          debugPrint('🔍 没有找到聊天摘要，查询所有聊天摘要进行调试...');
          final allChatSummaries = await ChatSummaryService.getRecentChatSummaries(
            userId: userId,
            limit: 10,
          );
          debugPrint('🔍 用户总共有 ${allChatSummaries.length} 条聊天摘要');
          for (var summary in allChatSummaries) {
            debugPrint('🔍 摘要: ${summary.createdAt} - ${summary.content}');
          }
        }

        setState(() {
          _selectedDateDiaries = diaries;
          _selectedDateChatSummaries = allChatSummaries;
          _isLoadingDateData = false;
        });
      } else {
        debugPrint('❌ 用户未登录，无法查询数据');
        setState(() {
          _isLoadingDateData = false;
        });
      }
    } catch (e) {
      debugPrint('❌ 加载日期数据失败: $e');
      setState(() {
        _isLoadingDateData = false;
      });
    }
  }

  void _showDailyTarotEntry(DateTime selectedDay, AppStateProvider appState) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selected = DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    if (selected.isAfter(today)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(languageManager.translate('cannot_view_future_records'))),
      );
      return;
    }

    final dayString = DateFormat('yyyy-MM-dd').format(selectedDay);

    // 先尝试从本地状态获取数据，如果没有则从后端加载
    final localDailyTarot = appState.dailyTarotReadings[dayString];

    // 同时加载聊天摘要
    _loadDiariesForDate(selectedDay);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white, // 白色底色
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20), // 增强高斯模糊
            child: Container(
              height: MediaQuery.of(context).size.height * 0.8,
              decoration: BoxDecoration(
                // 液态玻璃效果 - 白色底色叠加
                color: Colors.white.withOpacity(0.7), // 调整为70%透明度
                borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                border: Border.all(
                  color: Colors.white.withOpacity(0.6), // 更明显的边框
                  width: 1.5,
                ),
                boxShadow: [
                  // 液态玻璃阴影效果
                  BoxShadow(
                    color: Colors.white.withOpacity(0.3),
                    blurRadius: 25,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 15,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    child: Row(
                      children: [
                        Text(
                          _formatHistoryDate(selectedDay, languageManager),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.close,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 聊天摘要部分
                          Builder(
                            builder: (context) {
                              print('🎨🎨🎨 界面渲染开始 🎨🎨🎨');
                              print('🎨 界面渲染 - 总日记条目: ${_selectedDateDiaries.length}');
                              print('🎨 界面渲染 - 聊天摘要条目: ${_selectedDateChatSummaries.length}');

                              // 打印每个聊天摘要的详细信息
                              for (int i = 0; i < _selectedDateChatSummaries.length; i++) {
                                final summary = _selectedDateChatSummaries[i];
                                print('🎨 聊天摘要 $i: ${summary.content}');
                              }

                              final languageManager = Provider.of<LanguageManager>(context, listen: false);
                              final isZh = languageManager.currentLanguage.startsWith('zh');

                              if (_isLoadingDateData) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      isZh ? '💬 聊天记录' : '💬 Chat Records',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(strokeWidth: 2),
                                          ),
                                          const SizedBox(width: 12),
                                          Text(
                                            isZh ? '正在加载聊天记录...' : 'Loading chat records...',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                );
                              } else if (_selectedDateChatSummaries.isNotEmpty) {
                                print('🎨 显示聊天记录部分');

                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      isZh ? '💬 聊天记录' : '💬 Chat Records',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    ..._selectedDateChatSummaries.map((chatSummary) => Padding(
                                      padding: const EdgeInsets.only(bottom: 8),
                                      child: _buildChatSummaryCard(chatSummary),
                                    )),
                                    const SizedBox(height: 20),
                                  ],
                                );
                              } else {
                                // 加载完成但没有聊天记录时显示提示信息
                                print('🎨 聊天摘要为空，显示无记录提示');
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      isZh ? '💬 聊天记录' : '💬 Chat Records',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.grey.shade200,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.chat_bubble_outline,
                                            color: Colors.grey.shade400,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Text(
                                              isZh ? '该日期没有聊天记录' : 'No chat records for this date',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                );
                              }
                            },
                          ),

                          // 运势卡片部分
                          Builder(
                            builder: (context) {
                              final languageManager = Provider.of<LanguageManager>(context, listen: false);
                              final isZh = languageManager.currentLanguage.startsWith('zh');

                              return Text(
                                isZh ? '今日运势' : 'Daily Fortune',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 12),
                          FutureBuilder<DailyTarot?>(
                            future: _loadDailyTarotData(selectedDay),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState == ConnectionState.waiting) {
                                return const Center(
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                                  ),
                                );
                              }

                              // 使用后端数据或本地数据
                              final dailyTarot = snapshot.data ?? localDailyTarot;

                              return DailyTarotCard(
                                dailyTarot: dailyTarot,
                                selectedDate: selectedDay,
                                onDrawCard: selected.isAtSameMomentAs(today)
                                    ? () {
                                        Navigator.pop(context);
                                        _startDailyTarotShuffle(appState);
                                      }
                                    : null,
                                onRedraw: selected.isAtSameMomentAs(today)
                                    ? () {
                                        Navigator.pop(context);
                                        _redrawDailyTarot(appState);
                                      }
                                    : null,
                                isToday: selected.isAtSameMomentAs(today),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 获取指定日期的显化日记内容
  Future<String?> _getManifestationJournalForDate(DateTime date) async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return null;

      // 查找该日期的显化日记
      final diaries = await DiaryService.getDiariesByDate(
        userId: user.id,
        date: date,
      );

      // 查找显化日记（标签包含'manifestation'或内容包含显化相关关键词）
      final manifestationDiary = diaries.firstWhere(
        (diary) => diary.tags.contains('manifestation') ||
                   diary.tags.contains('显化') ||
                   diary.content.contains('显化'),
        orElse: () => DiaryEntry(
          id: '',
          userId: user.id,
          content: '',
          tags: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      return manifestationDiary.content.isNotEmpty ? manifestationDiary.content : null;
    } catch (e) {
      debugPrint('获取显化日记失败: $e');
      return null;
    }
  }

  // 从后端加载每日塔罗数据
  Future<DailyTarot?> _loadDailyTarotData(DateTime selectedDay) async {
    try {
      final dataService = SupabaseDataService();
      final dayString = DateFormat('yyyy-MM-dd').format(selectedDay);

      // 获取每日塔罗数据
      final dailyTarotData = await dataService.getDailyTarot(dayString);

      // 获取显化日记数据
      final manifestationData = await dataService.getManifestationJournal(dayString);

      // 转换为 DailyTarot 对象
      if (dailyTarotData != null) {
        print('📊 加载的每日塔罗数据: $dailyTarotData'); // 调试日志

        // 查找对应的塔罗牌
        TarotCard? card;
        if (dailyTarotData['card'] != null) {
          final cardData = dailyTarotData['card'] as Map<String, dynamic>;
          final cardId = cardData['id'] as String?;
          if (cardId != null) {
            card = TarotCardsData.allCards.firstWhere(
              (c) => c.id == cardId,
              orElse: () => TarotCardsData.allCards.first,
            );
          }
        }

        // 解析显化目标
        ManifestationGoal? manifestationGoal;
        if (dailyTarotData['manifestation_goal'] != null) {
          final goalStr = dailyTarotData['manifestation_goal'] as String;
          try {
            manifestationGoal = ManifestationGoal.values.firstWhere(
              (g) => g.name == goalStr,
            );
          } catch (e) {
            print('⚠️ 无法解析显化目标: $goalStr');
            manifestationGoal = ManifestationGoal.wealth;
          }
        }

        return DailyTarot(
          date: selectedDay,
          card: card,
          fortune: dailyTarotData['fortune'] as String?,
          advice: dailyTarotData['advice'] as String?,
          isDrawn: dailyTarotData['is_drawn'] as bool? ?? false,
          manifestationGoal: manifestationGoal,
          affirmation: dailyTarotData['affirmation'] as String?,
          manifestationJournal: await _getManifestationJournalForDate(selectedDay),
        );
      }

      return null;
    } catch (e) {
      print('❌ 加载每日塔罗数据失败: $e');
      return null;
    }
  }

  void _showManifestationGoalSelection(BuildContext context, AppStateProvider appState) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationGoalScreen(
          onGoalSelected: (goal) {
            appState.setManifestationGoal(goal);
            final languageManager = Provider.of<LanguageManager>(context, listen: false);
            String goalName;
            switch (goal) {
              case ManifestationGoal.wealth:
                goalName = languageManager.translate('wealth');
                break;
              case ManifestationGoal.career:
                goalName = languageManager.translate('career');
                break;
              case ManifestationGoal.beauty:
                goalName = languageManager.translate('beauty');
                break;
              case ManifestationGoal.fame:
                goalName = languageManager.translate('fame');
                break;
              case ManifestationGoal.love:
                goalName = languageManager.translate('love');
                break;
            }
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${languageManager.translate('manifestation_goal_set_success')}$goalName'),
                backgroundColor: Colors.purple[600],
              ),
            );
          },
        ),
      ),
    );
  }

  void _startMindfulnessPractice(BuildContext context, AppStateProvider appState) {
    final currentGoal = appState.currentManifestationGoal ?? ManifestationGoal.wealth;
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final affirmation = _getAffirmationForGoal(currentGoal, languageManager);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationAnimationScreen(
          goal: currentGoal,
          affirmation: affirmation,
        ),
      ),
    );
  }

  String _getAffirmationForGoal(ManifestationGoal goal, LanguageManager languageManager) {
    // 使用语言管理器获取对应的肯定语翻译键
    String translationKey;
    switch (goal) {
      case ManifestationGoal.wealth:
        translationKey = 'mindfulness_affirmation_wealth';
        break;
      case ManifestationGoal.career:
        translationKey = 'mindfulness_affirmation_career';
        break;
      case ManifestationGoal.beauty:
        translationKey = 'mindfulness_affirmation_beauty';
        break;
      case ManifestationGoal.fame:
        translationKey = 'mindfulness_affirmation_fame';
        break;
      case ManifestationGoal.love:
        translationKey = 'mindfulness_affirmation_love';
        break;
    }

    return languageManager.translate(translationKey);
  }

  // 格式化日历头部日期
  String _formatCalendarHeader(DateTime date, LanguageManager languageManager) {
    switch (languageManager.currentLanguage) {
      case 'en-US':
        return DateFormat('MMMM yyyy', 'en').format(date);
      case 'es-ES':
        return DateFormat('MMMM yyyy', 'es').format(date);
      case 'ja-JP':
        return DateFormat('yyyy年MM月', 'ja').format(date);
      case 'ko-KR':
        return DateFormat('yyyy년 MM월', 'ko').format(date);
      case 'zh-TW':
        return DateFormat('yyyy年MM月').format(date);
      case 'zh-CN':
      default:
        return DateFormat('yyyy年MM月').format(date);
    }
  }

  // 格式化历史页面日期
  String _formatHistoryDate(DateTime date, LanguageManager languageManager) {
    switch (languageManager.currentLanguage) {
      case 'en-US':
        return DateFormat('MMMM dd, yyyy', 'en').format(date);
      case 'es-ES':
        return DateFormat('dd \'de\' MMMM \'de\' yyyy', 'es').format(date);
      case 'ja-JP':
        return DateFormat('yyyy年MM月dd日', 'ja').format(date);
      case 'ko-KR':
        return DateFormat('yyyy년 MM월 dd일', 'ko').format(date);
      case 'zh-TW':
        return DateFormat('yyyy年MM月dd日').format(date);
      case 'zh-CN':
      default:
        return DateFormat('yyyy年MM月dd日').format(date);
    }
  }



  // 构建聊天摘要卡片
  Widget _buildChatSummaryCard(ChatSummary chatSummary) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final isZh = languageManager.currentLanguage.startsWith('zh');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.chat_bubble_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                isZh ? '聊天摘要' : 'Chat Summary',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade700,
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('HH:mm').format(chatSummary.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            chatSummary.content,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
          if (chatSummary.fullConversation != null) ...[
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () {
                _showFullConversationDialog(chatSummary);
              },
              child: Text(
                isZh ? '查看完整对话 →' : 'View Full Conversation →',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade600,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 显示完整对话的弹窗
  void _showFullConversationDialog(ChatSummary chatSummary) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final isZh = languageManager.currentLanguage.startsWith('zh');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isZh ? '完整对话' : 'Full Conversation'),
        content: SingleChildScrollView(
          child: Text(
            chatSummary.fullConversation ?? (isZh ? '暂无完整对话记录' : 'No conversation record available'),
            style: const TextStyle(height: 1.4),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isZh ? '关闭' : 'Close'),
          ),
        ],
      ),
    );
  }
}
