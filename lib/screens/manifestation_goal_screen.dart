import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/screens/manifestation_animation_screen.dart';
import 'package:ai_tarot_reading/screens/custom_goal_selection_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/widgets/liquid_glass_dialog.dart';
// 添加液态玻璃效果支持

class ManifestationGoalScreen extends StatefulWidget {
  final Function(ManifestationGoal) onGoalSelected;
  final ManifestationGoal? currentGoal;

  const ManifestationGoalScreen({
    super.key,
    required this.onGoalSelected,
    this.currentGoal,
  });

  @override
  State<ManifestationGoalScreen> createState() => _ManifestationGoalScreenState();
}

class _ManifestationGoalScreenState extends State<ManifestationGoalScreen> {
  ManifestationGoal? _selectedGoal;

  @override
  void initState() {
    super.initState();
    _selectedGoal = widget.currentGoal;
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Consumer<BlurSettingsService>(
      builder: (context, blurSettings, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF87CEEB),
                      Color(0xFFE6E6FA),
                      Color(0xFFF8BBD9),
                    ],
                  ),
                ),
              ),
              SafeArea(
                child: Column(
                  children: [
                    // 顶部导航栏
                    Container(
                      height: 60,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.arrow_back_ios,
                              color: FigmaTheme.textPrimary,
                              size: 24,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            languageManager.translate('set_manifestation_goal'),
                            style: const TextStyle(
                              color: FigmaTheme.textPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          const SizedBox(width: 48), // 平衡布局
                        ],
                      ),
                    ),

                    // 内容区域
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            // 标题区域
                            Text(
                              languageManager.translate('choose_manifestation_direction'),
                              style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.w700,
                                color: Color(0xFF2D3748),
                              ),
                              textAlign: TextAlign.center,
                            ).animate().fadeIn(duration: 600.ms),

                            const SizedBox(height: 16),

                            const SizedBox(height: 32),

                            // 自定义目标选项
                            _buildCustomGoalCard(languageManager),

                            const SizedBox(height: 16),

                            // 目标选择卡片
                            ...ManifestationGoal.values.asMap().entries.map((entry) {
                              final index = entry.key;
                              final goal = entry.value;
                              final isSelected = _selectedGoal == goal;
                              
                              return Container(
                                margin: const EdgeInsets.only(bottom: 16),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedGoal = goal;
                                    });
                                    // 财富目标显示版本选择，其他目标直接进入显化练习
                                    if (goal == ManifestationGoal.wealth) {
                                      _showWealthVersionDialog();
                                    } else {
                                      _directEnterManifestation(goal);
                                    }
                                  },
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20),
                                    child: BackdropFilter(
                                      filter: blurSettings.getImageFilter(), // 使用动态模糊度
                                      child: Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(20),
                                        decoration: BoxDecoration(
                                          color: isSelected 
                                              ? Colors.white.withOpacity(0.15) // 半透明毛玻璃
                                              : Colors.white.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(20),
                                          border: Border.all(
                                            color: isSelected 
                                                ? Colors.white.withOpacity(0.6)
                                                : Colors.white.withOpacity(0.3),
                                            width: isSelected ? 2 : 1,
                                          ),
                                          boxShadow: isSelected ? [
                                            BoxShadow(
                                              color: Colors.white.withOpacity(0.3),
                                              blurRadius: 20,
                                              offset: const Offset(0, 8),
                                            ),
                                          ] : [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.08),
                                              blurRadius: 10,
                                              offset: const Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Row(
                                          children: [
                                            // 图标
                                            Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: isSelected 
                                                    ? const Color(0xFF8B5CF6)
                                                    : Colors.grey[100],
                                                borderRadius: BorderRadius.circular(16),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  goal.emoji,
                                                  style: const TextStyle(fontSize: 28),
                                                ),
                                              ),
                                            ),
                                            
                                            const SizedBox(width: 16),
                                            
                                            // 内容
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    _getGoalName(goal),
                                                    style: TextStyle(
                                                      fontSize: 20,
                                                      fontWeight: FontWeight.w700,
                                                      color: isSelected
                                                          ? const Color(0xFF8B5CF6)
                                                          : const Color(0xFF2D3748),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    _getGoalDescription(goal),
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: Colors.grey[600],
                                                      height: 1.3,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            
                                            // 选中指示器
                                            if (isSelected)
                                              const Icon(
                                                Icons.check_circle,
                                                color: Color(0xFF8B5CF6),
                                                size: 24,
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ).animate().fadeIn(
                                delay: Duration(milliseconds: 400 + index * 100),
                                duration: 600.ms,
                              ).slideX(
                                begin: 0.3,
                                end: 0,
                              );
                            }),

                            const SizedBox(height: 32),



                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 直接进入显化练习（不显示弹窗）
  void _directEnterManifestation(ManifestationGoal goal) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    // 设定目标
    widget.onGoalSelected(goal);

    // 获取初始肯定语
    final initialAffirmation = languageManager.currentLanguage.startsWith('en')
        ? 'I believe the universe will bring me the best arrangements, and I deserve to have a beautiful life.'
        : '我相信宇宙会为我带来最好的安排，我值得拥有美好的生活。';

    // 直接进入肯定语显化页面
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationAnimationScreen(
          goal: goal,
          affirmation: initialAffirmation,
        ),
      ),
    );
  }



  void _showWealthVersionDialog() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    LiquidGlassDialogHelper.show(
      context: context,
      barrierDismissible: false,
      title: languageManager.translate('choose_wealth_version'),
      icon: Text(_selectedGoal!.emoji, style: const TextStyle(fontSize: 24)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              languageManager.translate('choose_wealth_style'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // 东方版选项
          LiquidGlassSelectionItem(
            emoji: '🏮',
            title: languageManager.translate('eastern_version'),
            subtitle: languageManager.translate('eastern_elements'),
            accentColor: const Color(0xFFE74C3C),
              onTap: () => _navigateToManifestation(ManifestationVersion.eastern),
          ),
            
            // 西方版选项
          LiquidGlassSelectionItem(
            emoji: '💎💵',
            title: languageManager.translate('western_version'),
            subtitle: languageManager.translate('western_elements'),
            accentColor: const Color(0xFF27AE60),
              onTap: () => _navigateToManifestation(ManifestationVersion.western),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
            },
            child: Text(languageManager.translate('cancel')),
          ),
        ],
    );
  }

  void _navigateToManifestation(ManifestationVersion version) {
    widget.onGoalSelected(_selectedGoal!);

    // 进入对应版本的显化页面
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final initialAffirmation = languageManager.currentLanguage.startsWith('en')
        ? 'I believe the universe will bring me the best arrangements, and I deserve to have a beautiful life.'
        : '我相信宇宙会为我带来最好的安排，我值得拥有美好的生活。';

    // 先关闭对话框，然后导航
    Navigator.pop(context); // 关闭版本选择对话框

    // 使用pushReplacement替代多次pop + push
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationAnimationScreen(
          goal: _selectedGoal!,
          version: version,
          affirmation: initialAffirmation,
        ),
      ),
    );
  }



  String _getGoalName(ManifestationGoal goal) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (goal) {
      case ManifestationGoal.wealth:
        return languageManager.translate('wealth_goal');
      case ManifestationGoal.career:
        return languageManager.translate('career_goal');
      case ManifestationGoal.beauty:
        return languageManager.translate('beauty_goal');
      case ManifestationGoal.fame:
        return languageManager.translate('fame_goal');
      case ManifestationGoal.love:
        return languageManager.translate('love_goal');
    }
  }

  String _getGoalDescription(ManifestationGoal goal) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (goal) {
      case ManifestationGoal.wealth:
        return languageManager.translate('wealth_description');
      case ManifestationGoal.career:
        return languageManager.translate('career_description');
      case ManifestationGoal.beauty:
        return languageManager.translate('beauty_description');
      case ManifestationGoal.fame:
        return languageManager.translate('fame_description');
      case ManifestationGoal.love:
        return languageManager.translate('love_description');
    }
  }

  // 构建自定义目标卡片 - 保持金色透明效果，但与其他选项长度一致
  Widget _buildCustomGoalCard(LanguageManager languageManager) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16), // 与其他选项完全相同的边距
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            // 直接跳转到自定义目标选择页面，不显示弹窗
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CustomGoalSelectionScreen(),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(20), // 与其他选项完全相同的内边距
            decoration: BoxDecoration(
              // 恢复金色透明渐变背景
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.amber.withValues(alpha: 0.2),
                  Colors.orange.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.amber.withValues(alpha: 0.4),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.amber.withValues(alpha: 0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              children: [
                // 使用cloud_background.png图标，但保持金色容器效果
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: Colors.amber.withValues(alpha: 0.3),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.asset(
                      'assets/images/cloud_background.png',
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // 如果图片加载失败，回退到原来的图标
                        return Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: Colors.amber.withValues(alpha: 0.3),
                          ),
                          child: const Icon(
                            Icons.auto_awesome,
                            color: Colors.amber,
                            size: 30,
                          ),
                        );
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // 文本内容 - 保持白色文字以配合透明背景
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        languageManager.translate('custom_goal'),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        languageManager.translate('use_your_space_goals'),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),

                // 箭头图标
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


}
