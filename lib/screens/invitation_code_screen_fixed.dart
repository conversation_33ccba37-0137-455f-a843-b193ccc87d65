import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

/// 邀请码输入界面 - 修复版本
class InvitationCodeScreenFixed extends StatefulWidget {
  const InvitationCodeScreenFixed({super.key});

  @override
  State<InvitationCodeScreenFixed> createState() => _InvitationCodeScreenFixedState();
}

class _InvitationCodeScreenFixedState extends State<InvitationCodeScreenFixed> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _submitCode() async {
    final code = _codeController.text.trim().toUpperCase();
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    

    if (code.isEmpty) {
      setState(() {
        _errorMessage = languageManager.translate('please_enter_code');
      });
      return;
    }

    if (code.length != 6) {
      setState(() {
        _errorMessage = languageManager.translate('code_should_be_6_chars');
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final invitationService = Provider.of<InvitationService>(context, listen: false);
      
      final result = await invitationService.useInvitationCode(
        code,
        language: languageManager.currentLanguage,
      );

      if (result.success) {

        // 通知订阅服务刷新状态
        try {
          final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
          // 🔧 使用强制刷新确保状态同步
          await subscriptionService.forceRefreshSubscriptionStatus();

          // 🔧 添加延迟刷新确保状态同步
          await Future.delayed(const Duration(milliseconds: 500));
          await subscriptionService.refreshSubscriptionStatus();
        } catch (e) {
        }

        // 邀请码服务已经在内部调用了 notifyListeners()

        _showSuccessDialog(result.message, result.rewardDays ?? 7);
      } else {
        setState(() {
          _errorMessage = result.message;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = languageManager.translate('network_error_retry');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog(String message, int rewardDays) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: FigmaTheme.primaryPink.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                color: FigmaTheme.primaryPink,
                size: 40,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              languageManager.translate('redemption_successful'),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: FigmaTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    FigmaTheme.primaryPink.withOpacity(0.1),
                    FigmaTheme.primaryPink.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: FigmaTheme.primaryPink.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.card_giftcard,
                    color: FigmaTheme.primaryPink,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    rewardDays == 7
                      ? languageManager.translate('weekly_membership_reward')
                      : rewardDays == 30
                        ? languageManager.translate('monthly_membership_reward')
                        : languageManager.translate('membership_reward_received').replaceAll('{days}', rewardDays.toString()),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: FigmaTheme.primaryPink,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message,
                    style: const TextStyle(
                      fontSize: 14,
                      color: FigmaTheme.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  try {
                    Navigator.of(context).pop();
                    _codeController.clear();
                    setState(() {
                      _errorMessage = null;
                    });
                  } catch (e) {
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: FigmaTheme.primaryPink,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(languageManager.translate('awesome')),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // 背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(-1.0, -1.0),
                end: Alignment(1.0, 1.0),
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                  Color(0xFFE6E6FA),
                  Color(0xFF87CEEB),
                ],
              ),
            ),
          ),

          // 主要内容
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏 - 透明毛玻璃效果
                ClipRRect(
                  borderRadius: BorderRadius.circular(0),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                    child: Container(
                      height: 60,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      decoration: BoxDecoration(
                        // 类似订阅页面的透明毛玻璃效果
                        color: Colors.white.withValues(alpha: 0.15),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 4),
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 25,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Row(
                    children: [
                      // 返回按钮
                      GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: FigmaTheme.textPrimary,
                            size: 24,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          languageManager.translate('invitation_code'),
                          style: const TextStyle(
                            color: FigmaTheme.textPrimary,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      // 右侧占位符，保持视觉平衡
                      const SizedBox(width: 48),
                    ],
                      ),
                    ),
                  ),
                ),

                // 内容区域
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        const SizedBox(height: 20),

                        // 标题区域
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: FigmaTheme.createGlassDecoration(
                            opacity: 0.8,
                            radius: 20,
                          ),
                          child: Column(
                            children: [
                              // 使用cloud_background.png图标
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: FigmaTheme.primaryPink.withOpacity(0.2),
                                      blurRadius: 15,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: ClipOval(
                                  child: Image.asset(
                                    'assets/images/cloud_background.png',
                                    width: 60,
                                    height: 60,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      // 如果图片加载失败，回退到原来的图标
                                      return const Icon(
                                        Icons.card_giftcard,
                                        size: 60,
                                        color: FigmaTheme.primaryPink,
                                      );
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                languageManager.translate('enter_invitation_code'),
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: FigmaTheme.textPrimary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                languageManager.translate('invitation_code_description'),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: FigmaTheme.textSecondary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // 我的邀请码区域
                        Consumer<InvitationService>(
                          builder: (context, invitationService, child) {
                            if (invitationService.myInvitationCode != null) {
                              return _buildMyInvitationCodeSection(invitationService, languageManager);
                            }
                            return const SizedBox.shrink();
                          },
                        ),

                        const SizedBox(height: 30),

                        // 输入区域
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: FigmaTheme.createGlassDecoration(
                            opacity: 0.9,
                            radius: 20,
                          ),
                          child: Column(
                            children: [
                              // 输入框
                              TextField(
                                controller: _codeController,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 8,
                                  color: FigmaTheme.textPrimary,
                                ),
                                decoration: InputDecoration(
                                  hintText: 'XXXXXX',
                                  hintStyle: TextStyle(
                                    color: FigmaTheme.textSecondary.withOpacity(0.5),
                                    letterSpacing: 8,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    borderSide: BorderSide(
                                      color: _errorMessage != null
                                        ? Colors.red
                                        : FigmaTheme.primaryPink.withOpacity(0.3),
                                      width: 2,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    borderSide: BorderSide(
                                      color: FigmaTheme.primaryPink.withOpacity(0.3),
                                      width: 2,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(16),
                                    borderSide: const BorderSide(
                                      color: FigmaTheme.primaryPink,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: Colors.white.withOpacity(0.9),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 20,
                                  ),
                                ),
                                inputFormatters: [
                                  UpperCaseTextFormatter(),
                                  LengthLimitingTextInputFormatter(6),
                                  FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _errorMessage = null;
                                  });
                                },
                                onSubmitted: (_) => _submitCode(),
                              ),

                              // 错误信息
                              if (_errorMessage != null) ...[
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.red.withOpacity(0.3),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.error_outline,
                                        color: Colors.red,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          _errorMessage!,
                                          style: const TextStyle(
                                            color: Colors.red,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],

                              const SizedBox(height: 24),

                              // 提交按钮
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: _isLoading ? null : _submitCode,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: FigmaTheme.primaryPink,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 8,
                                    shadowColor: FigmaTheme.primaryPink.withOpacity(0.3),
                                  ),
                                  child: _isLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : Text(
                                        languageManager.translate('use_invitation_code'),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // 使用规则
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: FigmaTheme.createGlassDecoration(
                            opacity: 0.7,
                            radius: 16,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.info_outline,
                                    color: FigmaTheme.primaryPink,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    languageManager.translate('usage_rules'),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: FigmaTheme.textPrimary,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                languageManager.translate('invitation_code_rules'),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: FigmaTheme.textSecondary,
                                  height: 1.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),


        ],
      ),
    );
  }

  /// 构建我的邀请码区域
  Widget _buildMyInvitationCodeSection(InvitationService invitationService, LanguageManager languageManager) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: FigmaTheme.createGlassDecoration(
        opacity: 0.8,
        radius: 20,
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.share,
                color: FigmaTheme.primaryPink,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                languageManager.translate('my_invitation_code'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: FigmaTheme.textPrimary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 邀请码显示
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  FigmaTheme.primaryPink.withOpacity(0.1),
                  FigmaTheme.primaryPink.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: FigmaTheme.primaryPink.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitationService.myInvitationCode ?? '',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 4,
                          color: FigmaTheme.primaryPink,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        languageManager.translate('share_with_friends'),
                        style: const TextStyle(
                          fontSize: 12,
                          color: FigmaTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    _copyInvitationCode(invitationService.myInvitationCode!, languageManager);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: FigmaTheme.primaryPink.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.copy,
                      color: FigmaTheme.primaryPink,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 邀请统计
          FutureBuilder<InvitationStats?>(
            future: invitationService.getInvitationStats(),
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data != null) {
                final stats = snapshot.data!;
                return Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        languageManager.translate('invited_friends'),
                        stats.successfulInvitations.toString(),
                        Icons.people,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        languageManager.translate('reward_days'),
                        '${stats.totalRewardDays}${languageManager.currentLanguage.startsWith('zh') ? '天' : ' days'}',
                        Icons.card_giftcard,
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),

          const SizedBox(height: 16),

          // 分享按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _shareInvitationCode(invitationService.myInvitationCode!, languageManager);
              },
              icon: const Icon(Icons.share, size: 20),
              label: Text(languageManager.translate('share_invitation_code')),
              style: ElevatedButton.styleFrom(
                backgroundColor: FigmaTheme.primaryPink.withOpacity(0.1),
                foregroundColor: FigmaTheme.primaryPink,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(
                  color: FigmaTheme.primaryPink.withOpacity(0.3),
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: FigmaTheme.primaryPink,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: FigmaTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: FigmaTheme.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 复制邀请码
  void _copyInvitationCode(String code, LanguageManager languageManager) {
    Clipboard.setData(ClipboardData(text: code));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageManager.translate('invitation_code_copied')),
        backgroundColor: FigmaTheme.primaryPink,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// 分享邀请码
  void _shareInvitationCode(String code, LanguageManager languageManager) {
    final shareTemplate = languageManager.translate('invitation_share_template');
    final text = shareTemplate.replaceAll('{code}', code);

    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageManager.translate('invitation_text_copied')),
        backgroundColor: FigmaTheme.primaryPink,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }
}

/// 自动转换为大写的输入格式化器
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
