import 'package:flutter/material.dart';

/// 基于Figma设计的主题配置 - 苹果风格粉色/白色UI
class FigmaTheme {
  // 🍎 苹果风格颜色系统 - 粉色/白色主题
  static const Color primaryPink = Color(0xFFFF6B9D); // 苹果粉色
  static const Color secondaryBlue = Color(0xFF007AFF); // 苹果蓝色
  static const Color accentPurple = Color(0xFF8B5CF6); // 紫色渐变
  static const Color backgroundLight = Color(0xFFFAFAFA); // 浅色背景
  static const Color surfaceWhite = Color(0xFFFFFFFF); // 纯白表面
  static const Color cardBackground = Color(0xFFF8F9FA); // 卡片背景
  static const Color textPrimary = Color(0xFF1D1D1F); // 深色文字
  static const Color textSecondary = Color(0xFF6E6E73); // 次要文字
  static const Color textMuted = Color(0xFF8E8E93); // 静音文字

  // 🌈 渐变背景色 - 与主页一致
  static const Color gradientSkyBlue = Color(0xFF87CEEB); // #87CEEB
  static const Color gradientLavender = Color(0xFFE6E6FA); // #E6E6FA
  static const Color gradientPink = Color(0xFFF8BBD9); // #F8BBD9

  // 🌈 苹果风格渐变色
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryPink, accentPurple],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [surfaceWhite, cardBackground],
  );

  // 🎨 主页背景渐变 - 与主页完全一致
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment(-1.0, -1.0), // 135deg
    end: Alignment(1.0, 1.0),
    stops: [0.0, 0.25, 0.5, 0.75, 1.0],
    colors: [
      gradientSkyBlue,   // #87CEEB 0%
      gradientLavender,  // #E6E6FA 25%
      gradientPink,      // #F8BBD9 50%
      gradientLavender,  // #E6E6FA 75%
      gradientSkyBlue,   // #87CEEB 100%
    ],
  );

  // 🔮 现代玻璃效果渐变
  static const LinearGradient glassGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFFFFF), // 白色
      Color(0xFFF8F9FA), // 浅灰
    ],
  );

  // 文本样式
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: textPrimary,
    fontFamily: 'Inter',
    height: 1.2,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: textPrimary,
    fontFamily: 'Inter',
    height: 1.3,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    fontFamily: 'Inter',
    height: 1.4,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: textPrimary,
    fontFamily: 'Inter',
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: textSecondary,
    fontFamily: 'Inter',
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: textMuted,
    fontFamily: 'Inter',
    height: 1.4,
  );

  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    fontFamily: 'Inter',
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: textSecondary,
    fontFamily: 'Inter',
    height: 1.3,
  );

  // 阴影
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
    BoxShadow(
      color: Color(0x0D000000),
      blurRadius: 16,
      offset: Offset(0, 8),
    ),
  ];

  static const List<BoxShadow> elevatedShadow = [
    BoxShadow(
      color: Color(0x26000000),
      blurRadius: 12,
      offset: Offset(0, 6),
    ),
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 24,
      offset: Offset(0, 12),
    ),
  ];

  static const List<BoxShadow> glowShadow = [
    BoxShadow(
      color: Color(0x4D6B46C1),
      blurRadius: 20,
      offset: Offset(0, 0),
    ),
  ];

  // 边框半径
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;

  // 间距
  static const double spacingXS = 4.0;
  static const double spacingSM = 8.0;
  static const double spacingMD = 16.0;
  static const double spacingLG = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  /// 创建基于Figma设计的主题
  static ThemeData createTheme() {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Inter',
      
      // 🍎 苹果风格颜色方案 - 浅色主题
      colorScheme: const ColorScheme.light(
        primary: primaryPink,
        secondary: secondaryBlue,
        tertiary: accentPurple,
        surface: surfaceWhite,
        onPrimary: surfaceWhite,
        onSecondary: surfaceWhite,
        onSurface: textPrimary,
      ),

      // 文本主题
      textTheme: const TextTheme(
        headlineLarge: headlineLarge,
        headlineMedium: headlineMedium,
        headlineSmall: headlineSmall,
        bodyLarge: bodyLarge,
        bodyMedium: bodyMedium,
        bodySmall: bodySmall,
        labelLarge: labelLarge,
        labelMedium: labelMedium,
      ),

      // 🎴 苹果风格卡片主题
      cardTheme: CardThemeData(
        color: surfaceWhite,
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),

      // 📱 苹果风格应用栏主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: headlineMedium,
      ),

      // 🔘 苹果风格按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryPink,
          foregroundColor: surfaceWhite,
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLG,
            vertical: spacingMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusLarge),
          ),
          textStyle: labelLarge,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryPink,
          side: const BorderSide(color: primaryPink),
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLG,
            vertical: spacingMD,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusLarge),
          ),
          textStyle: labelLarge,
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
          borderSide: const BorderSide(color: primaryPink, width: 2),
        ),
        hintStyle: bodyMedium.copyWith(color: textMuted),
        labelStyle: labelMedium,
      ),

      // 🧭 苹果风格底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceWhite,
        selectedItemColor: primaryPink,
        unselectedItemColor: textMuted,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
      ),

      // 💬 苹果风格对话框主题
      dialogTheme: DialogThemeData(
        backgroundColor: surfaceWhite,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
        ),
        titleTextStyle: headlineSmall,
        contentTextStyle: bodyMedium,
      ),

      // 🏷️ 苹果风格芯片主题
      chipTheme: ChipThemeData(
        backgroundColor: cardBackground,
        selectedColor: primaryPink,
        labelStyle: labelMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLarge),
        ),
      ),
    );
  }

  /// 🎨 创建苹果风格卡片装饰
  static BoxDecoration createCardDecoration({
    Color? color,
    List<BoxShadow>? shadows,
    Gradient? gradient,
  }) {
    return BoxDecoration(
      color: color ?? surfaceWhite,
      gradient: gradient,
      borderRadius: BorderRadius.circular(radiusLarge),
      boxShadow: shadows ?? cardShadow,
    );
  }

  /// 🌟 创建苹果风格玻璃效果装饰
  static BoxDecoration createGlassDecoration({
    double opacity = 0.8,
    double radius = radiusLarge,
  }) {
    return BoxDecoration(
      color: surfaceWhite.withValues(alpha: opacity),
      borderRadius: BorderRadius.circular(radius),
      border: Border.all(
        color: surfaceWhite.withValues(alpha: 0.2),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 20,
          offset: const Offset(0, 10),
        ),
      ],
    );
  }

  /// 创建发光效果装饰
  static BoxDecoration createGlowDecoration({
    required Color glowColor,
    double radius = radiusMedium,
  }) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(radius),
      boxShadow: [
        BoxShadow(
          color: glowColor.withValues(alpha: 0.3),
          blurRadius: 20,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }
}
