import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/services/psychological_guidance_service.dart';
import 'package:ai_tarot_reading/models/psychological_tarot_reading.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/services/langfuse_service.dart';

class AITarotService {
  static final _supabase = Supabase.instance.client;

  /// 生成心理引导式塔罗解读
  static Future<PsychologicalTarotReading> generatePsychologicalReading({
    required String question,
    required List<TarotCard> cards,
    required String spreadType,
  }) async {
    try {
      // 1. 分析问题类型
      final questionType = PsychologicalGuidanceService.analyzeQuestionType(question);

      // 2. 生成基础塔罗解读
      final basicReading = await generateTarotReading(
        question: question,
        cards: cards,
        spreadType: spreadType,
      );

      // 3. 为每张牌生成引导性问题
      final guidingQuestions = <String, List<String>>{};
      for (int i = 0; i < cards.length; i++) {
        final card = cards[i];
        guidingQuestions[card.id] = PsychologicalGuidanceService.generateGuidingQuestions(
          questionType,
          card,
          question,
          1, // 第一轮对话
        );
      }

      // 4. 生成初始疗愈建议（基于问题类型的预判）
      final initialPattern = _predictPatternFromQuestion(question, questionType);
      final healingTechniques = PsychologicalGuidanceService.generateHealingTechniques(initialPattern);
      final manifestationGuidance = PsychologicalGuidanceService.generateManifestationGuidance(initialPattern);

      return PsychologicalTarotReading(
        basicReading: basicReading,
        questionType: questionType,
        guidingQuestions: guidingQuestions,
        conversationHistory: [question],
        identifiedPattern: initialPattern,
        healingTechniques: healingTechniques,
        manifestationGuidance: manifestationGuidance,
        conversationRound: 1,
      );

    } catch (e) {
      print('❌ 心理引导式解读生成失败: $e');
      // 降级到基础解读
      final basicReading = await generateTarotReading(
        question: question,
        cards: cards,
        spreadType: spreadType,
      );

      return PsychologicalTarotReading(
        basicReading: basicReading,
        questionType: QuestionType.general,
        guidingQuestions: {},
        conversationHistory: [question],
        identifiedPattern: PsychologicalPattern.unknown,
        healingTechniques: [],
        manifestationGuidance: '相信宇宙正在为你安排最好的。',
        conversationRound: 1,
      );
    }
  }

  /// 继续心理引导对话
  static Future<PsychologicalTarotReading> continueGuidedConversation({
    required PsychologicalTarotReading previousReading,
    required String userResponse,
    required TarotCard focusCard,
  }) async {
    try {
      // 1. 更新对话历史
      final updatedHistory = [...previousReading.conversationHistory, userResponse];
      final nextRound = previousReading.conversationRound + 1;

      // 2. 重新识别心理模式（基于更多信息）
      final identifiedPattern = PsychologicalGuidanceService.identifyPattern(updatedHistory);

      // 3. 生成下一轮引导问题
      final nextQuestions = PsychologicalGuidanceService.generateGuidingQuestions(
        previousReading.questionType,
        focusCard,
        updatedHistory.first, // 原始问题
        nextRound,
      );

      // 4. 更新疗愈建议
      final updatedHealingTechniques = PsychologicalGuidanceService.generateHealingTechniques(identifiedPattern);
      final updatedManifestationGuidance = PsychologicalGuidanceService.generateManifestationGuidance(identifiedPattern);

      // 5. 更新引导问题
      final updatedGuidingQuestions = Map<String, List<String>>.from(previousReading.guidingQuestions);
      updatedGuidingQuestions[focusCard.id] = nextQuestions;

      return previousReading.copyWith(
        guidingQuestions: updatedGuidingQuestions,
        conversationHistory: updatedHistory,
        identifiedPattern: identifiedPattern,
        healingTechniques: updatedHealingTechniques,
        manifestationGuidance: updatedManifestationGuidance,
        conversationRound: nextRound,
      );

    } catch (e) {
      print('❌ 继续引导对话失败: $e');
      return previousReading;
    }
  }

  /// 根据问题预测可能的心理模式
  static PsychologicalPattern _predictPatternFromQuestion(String question, QuestionType questionType) {
    final lowerQuestion = question.toLowerCase();

    if (questionType == QuestionType.relationship) {
      if (lowerQuestion.contains('复合') || lowerQuestion.contains('放不下')) {
        return PsychologicalPattern.attachment;
      }
    }

    if (lowerQuestion.contains('害怕') || lowerQuestion.contains('不敢')) {
      return PsychologicalPattern.fear;
    }

    if (lowerQuestion.contains('纠结') || lowerQuestion.contains('想太多')) {
      return PsychologicalPattern.rumination;
    }

    return PsychologicalPattern.unknown;
  }

  /// 生成心理引导式简短塔罗解读（流式生成）
  static Stream<String> generateTarotReadingStream({
    required String question,
    required List<TarotCard> cards,
    required String spreadType,
    required String userLanguage, // 新增：用户语言参数
    List<String> conversationHistory = const [],
  }) async* {
    try {
      // 1. 先分析问题类型（心理引导式）
      final questionType = PsychologicalGuidanceService.analyzeQuestionType(question);
      
      // 2. 构建简短解读的请求数据
      final requestData = {
        'question': question,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': card.name,
          'description': card.description,
          'meaning': card.meaning,
          'keywords': card.keywords,
          'isMajorArcana': card.isMajorArcana,
          'isReversed': card.isReversed,
        }).toList(),
        'spreadType': spreadType,
        'requestType': 'brief_reading', // 改为简短解读
        'userLanguage': userLanguage, // 传递用户语言
        'questionType': questionType.toString(),
        'maxLength': 60, // 限制在60字以内，更简洁
        'includeGuidingQuestion': true, // 包含引导性问题
        'stream': true,
        'conversationHistory': conversationHistory, // 传递对话历史
      };

      // 3. 调用Supabase Edge Function
      print('🔄 开始调用Edge Function (流式解读): deepseek-tarot-reading');
      print('📋 请求数据: ${requestData.toString()}');
      
      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      print('📨 Edge Function响应: ${response.data}');
      print('📊 响应状态: ${response.status}');

      if (response.data != null && response.data['success'] == true) {
        final reading = response.data['reading'] as String;
        
        // 4. 流式输出效果 - 但间隔改短
        final words = reading.split(' ');
        String currentText = '';
        
        for (int i = 0; i < words.length; i++) {
          currentText += words[i];
          if (i < words.length - 1) currentText += ' ';
          
          yield currentText;
          
          // 极速响应，模拟真实占卜师的节奏
          await Future.delayed(const Duration(milliseconds: 15));
        }
      } else {
        // 使用fallback简短解读（仍然基于问题内容个性化）
        yield* _getBriefFallbackReadingStream(question, cards, spreadType, questionType);
      }
    } catch (e) {
      print('AI解读服务失败: $e');
      // 分析问题类型用于fallback
      final questionType = PsychologicalGuidanceService.analyzeQuestionType(question);
      yield* _getBriefFallbackReadingStream(question, cards, spreadType, questionType);
    }
  }

  /// 生成简短的fallback解读（心理引导式）
  static Stream<String> _getBriefFallbackReadingStream(
    String question, 
    List<TarotCard> cards, 
    String spreadType,
    QuestionType questionType,
  ) async* {
    try {
      // 1. 生成100字以内的卡牌分析（基于用户问题个性化）
      final briefAnalysis = _generateBriefCardAnalysis(cards, spreadType, question);
      
      // 2. 生成引导性问题
      final guidingQuestion = _generateInitialGuidingQuestion(question, cards.first, questionType);
      
      // 3. 组合完整回复
      final fullResponse = '''$briefAnalysis

✨ **深入探索**
$guidingQuestion''';

      // 4. 流式输出
      final words = fullResponse.split(' ');
      String currentText = '';
      
      for (int i = 0; i < words.length; i++) {
        currentText += words[i];
        if (i < words.length - 1) currentText += ' ';
        
        yield currentText;
        await Future.delayed(const Duration(milliseconds: 40));
      }
    } catch (e) {
      yield '抱歉，解读生成遇到问题，请重试。';
    }
  }

  /// 生成个性化的简短卡牌分析（基于用户问题内容）
  static String _generateBriefCardAnalysis(List<TarotCard> cards, String spreadType, String question) {
    // 分析问题中的关键词来个性化回复
    final lowerQuestion = question.toLowerCase();
    final isRelationshipQuestion = lowerQuestion.contains('感情') || lowerQuestion.contains('爱情') || lowerQuestion.contains('关系');
    final isCareerQuestion = lowerQuestion.contains('工作') || lowerQuestion.contains('职业') || lowerQuestion.contains('事业');
    final isDecisionQuestion = lowerQuestion.contains('选择') || lowerQuestion.contains('决定') || lowerQuestion.contains('应该');
    
    if (cards.length == 1) {
      final card = cards.first;
      final position = card.isReversed ? '逆位' : '正位';
      
      // 根据问题类型个性化描述
      String contextPhrase;
      if (isRelationshipQuestion) {
        contextPhrase = '在情感的层面上';
      } else if (isCareerQuestion) {
        contextPhrase = '在职业发展方面';
      } else if (isDecisionQuestion) {
        contextPhrase = '面对选择时';
      } else {
        contextPhrase = '在当前的状况下';
      }
      
      // 根据卡牌特性选择不同的描述方式
      final cardEnergy = card.isMajorArcana ? '重要的生命课题' : '日常生活的智慧';
      
      return '''🔮 ${card.name}（$position）$contextPhrase揭示$cardEnergy。${_getCardInsight(card, question)}''';
    } else {
      final summary = cards.map((card) => card.name).join('、');
      final energyDescription = _getSpreadEnergyDescription(cards, question);
      
      return '''🔮 $summary 织就了$energyDescription的能量画面，每张牌都有独特故事。''';
    }
  }

  /// 根据卡牌和问题生成个性化洞察
  static String _getCardInsight(TarotCard card, String question) {
    final cardKeywords = card.keywords.take(2).join('、');
    if (question.contains('为什么')) {
      return '它通过"$cardKeywords"的能量回应着你内心的疑问';
    } else if (question.contains('怎么')) {
      return '"$cardKeywords"的智慧为你指出了可能的方向';
    } else {
      return '$cardKeywords的能量与你当前的处境形成了微妙的对话';
    }
  }

  /// 获取牌阵的能量描述
  static String _getSpreadEnergyDescription(List<TarotCard> cards, String question) {
    final majorCount = cards.where((card) => card.isMajorArcana).length;
    final totalCount = cards.length;
    
    if (majorCount >= totalCount * 0.6) {
      return '深刻而充满转化力量';
    } else if (question.contains('感情')) {
      return '情感丰富而富有层次';
    } else if (question.contains('工作')) {
      return '实用而充满行动力';
    } else {
      return '平衡而富有指导性';
    }
  }

  /// 生成初始引导性问题
  static String _generateInitialGuidingQuestion(String question, TarotCard firstCard, QuestionType questionType) {
    // 使用心理引导服务生成第一轮问题
    final questions = PsychologicalGuidanceService.generateGuidingQuestions(
      questionType, 
      firstCard, 
      question, 
      1, // 第一轮
    );
    
    // 返回第一个问题，如果没有则生成通用问题
    if (questions.isNotEmpty) {
      return questions.first;
    }
    
         return '这张${firstCard.name}触发了你什么样的感受？我们可以从这里开始深入探索。';
   }



  /// 生成塔罗解读 - 非流式版本（兼容性保留）
  static Future<String> generateTarotReading({
    required String question,
    required List<TarotCard> cards,
    required String spreadType,
    String? userLanguage, // 添加用户语言参数
  }) async {
    try {
      // 构建发送给Supabase Edge Function的数据
      final requestData = {
        'question': question,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': card.name,
          'description': card.description,
          'meaning': card.meaning,
          'keywords': card.keywords,
          'isMajorArcana': card.isMajorArcana,
          'isReversed': card.isReversed,
        }).toList(),
        'spreadType': spreadType,
        'requestType': 'brief_reading',
        'userLanguage': userLanguage ?? 'en', // 使用传入的用户语言，默认英文
      };

      // 调用Supabase Edge Function
      print('🔄 开始调用Edge Function (基础解读): deepseek-tarot-reading');
      print('📋 请求数据: ${requestData.toString()}');
      
      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      print('📨 Edge Function响应: ${response.data}');
      print('📊 响应状态: ${response.status}');

      if (response.data != null && response.data['success'] == true) {
        return response.data['reading'] as String;
      } else {
        final errorMsg = 'Supabase函数调用失败 - 响应状态: ${response.status}, 错误: ${response.data?['error'] ?? response.data?.toString() ?? '未知错误'}';
        print('❌ $errorMsg');
        throw Exception(errorMsg);
      }
    } catch (e) {
      print('AI解读服务失败: $e');
      return _getFallbackReading(question, cards, spreadType);
    }
  }

  /// 生成心理引导式后续回复（流式生成）
  static Stream<String> generateFollowUpResponseStream({
    required String userMessage,
    required String previousReading,
    required List<TarotCard> cards,
    List<String>? conversationHistory, // 完整对话历史
    String? traceId, // Langfuse追踪ID
    String? userId, // 用户ID
  }) async* {
    // 0. 创建或使用现有的Langfuse Trace
    final activeTraceId = traceId ?? await LangfuseService.createTrace(
      userId: userId ?? 'anonymous',
      sessionId: 'session_${DateTime.now().millisecondsSinceEpoch}',
      metadata: {
        'action': 'followup_response',
        'cards_count': cards.length,
        'has_conversation_history': conversationHistory != null,
      },
    );

    try {
      // 1. 分析用户回复和心理模式
      final fullHistory = conversationHistory ?? [previousReading, userMessage];
      final identifiedPattern = PsychologicalGuidanceService.identifyPattern(fullHistory);
      
      // 2. 检测重复问题模式
      final repetitionAnalysis = _analyzeQuestionRepetition(userMessage, fullHistory);

      // 记录用户输入事件
      await LangfuseService.createEvent(
        traceId: activeTraceId,
        name: 'user_followup_question',
        input: {
          'message': userMessage,
          'pattern': identifiedPattern.toString(),
          'repetition_detected': repetitionAnalysis['isRepetitive'],
          'repetition_level': repetitionAnalysis['intensityLevel'],
        },
        metadata: {
          'conversation_length': fullHistory.length,
          'focus_card': cards.first.name,
        },
      );
      
      // 3. 构建心理引导式AI请求
      final requestData = {
        'userMessage': userMessage,
        'previousReading': previousReading,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': card.name,
          'description': card.description,
          'meaning': card.meaning,
          'keywords': card.keywords,
          'isReversed': card.isReversed,
        }).toList(),
                 'requestType': 'psychological_guidance',
         'identifiedPattern': identifiedPattern.toString(),
         'repetitionAnalysis': repetitionAnalysis,
         'guidancePrompt': _buildPsychologicalGuidancePrompt(userMessage, cards.first, identifiedPattern, repetitionAnalysis),
        'stream': true,
      };

      // 3. 调用AI API生成个性化回复
      final startTime = DateTime.now();
      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      if (response.data != null && response.data['success'] == true) {
        final responseText = response.data['response'] as String;
        
        // 记录AI生成结果
        await LangfuseService.createGeneration(
          traceId: activeTraceId,
          name: 'deepseek_followup_generation',
          input: requestData,
          output: {'response': responseText},
          model: 'deepseek-chat',
          metadata: {
            'pattern': identifiedPattern.toString(),
            'repetition_handling': repetitionAnalysis['isRepetitive'],
            'response_length': responseText.length,
            'generation_type': 'ai_api',
            'duration_ms': DateTime.now().difference(startTime).inMilliseconds,
          },
        );

        yield* _streamResponse(responseText, 15);
              } else {
          // 降级到本地引导模板
          final templateResponse = _generateGuidedResponseFromTemplate(userMessage, cards.first, identifiedPattern, repetitionAnalysis);
          
          // 记录模板生成结果
          await LangfuseService.createGeneration(
            traceId: activeTraceId,
            name: 'template_fallback_generation',
            input: {'message': userMessage, 'pattern': identifiedPattern.toString()},
            output: {'response': templateResponse},
            model: 'template_system',
            metadata: {
              'generation_type': 'template_fallback',
              'repetition_handling': repetitionAnalysis['isRepetitive'],
              'response_length': templateResponse.length,
            },
          );
          
          yield* _streamResponse(templateResponse, 15);
        }
      
    } catch (e) {
      print('心理引导式回复失败: $e');
      
      // 降级处理
      try {
        final fallbackHistory = conversationHistory ?? [previousReading, userMessage];
        final identifiedPattern = PsychologicalGuidanceService.identifyPattern(fallbackHistory);
        final fallbackRepetitionAnalysis = _analyzeQuestionRepetition(userMessage, fallbackHistory);
        final templateResponse = _generateGuidedResponseFromTemplate(userMessage, cards.first, identifiedPattern, fallbackRepetitionAnalysis);
        yield* _streamResponse(templateResponse, 15);
      } catch (fallbackError) {
        print('模板引导回复也失败: $fallbackError');
        yield* _getFallbackFollowUpResponse(userMessage);
      }
    }
  }

  /// 分析重复问题模式
  static Map<String, dynamic> _analyzeQuestionRepetition(String currentMessage, List<String> conversationHistory) {
    // 如果对话历史少于2轮，不算重复
    if (conversationHistory.length < 2) {
      return {
        'isRepetitive': false,
        'repetitionType': 'none',
        'blockPoint': null,
        'deeperIntent': null,
      };
    }

    final currentLower = currentMessage.toLowerCase();
    int repetitionCount = 0;
    String? similarQuestion;
    
    // 检查是否有相似问题在历史中出现
    for (int i = conversationHistory.length - 2; i >= 0; i--) {
      final historyMessage = conversationHistory[i].toLowerCase();
      
      // 跳过AI回复（包含特定标识符）
      if (historyMessage.contains('🔮') || historyMessage.contains('💜') || 
          historyMessage.contains('🤗') || historyMessage.contains('🧘') || 
          historyMessage.contains('🌟') || historyMessage.contains('💎')) {
        continue;
      }
      
      final similarity = _calculateMessageSimilarity(currentLower, historyMessage);
      if (similarity > 0.6) { // 60%以上相似度认为是重复（降低阈值，更敏感）
        repetitionCount++;
        similarQuestion = conversationHistory[i];
      }
    }

    // 第二次重复就进入警觉模式，第三次重复进入深层挖掘模式
    if (repetitionCount >= 1) {
      // 分析重复类型和可能的卡点
      final blockAnalysis = _analyzeBlockPoint(currentMessage, similarQuestion!);
      
      // 确定处理级别
      String intensityLevel;
      if (repetitionCount == 1) {
        intensityLevel = 'alert'; // 警觉模式
      } else if (repetitionCount >= 2) {
        intensityLevel = 'deep_exploration'; // 深层挖掘模式
      } else {
        intensityLevel = 'gentle'; // 温和提醒
      }
      
      return {
        'isRepetitive': true,
        'repetitionCount': repetitionCount,
        'intensityLevel': intensityLevel,
        'repetitionType': blockAnalysis['type'],
        'blockPoint': blockAnalysis['blockPoint'],
        'deeperIntent': blockAnalysis['deeperIntent'],
        'originalQuestion': similarQuestion,
      };
    }

    return {
      'isRepetitive': false,
      'repetitionType': 'none',
      'blockPoint': null,
      'deeperIntent': null,
    };
  }

  /// 计算消息相似度
  static double _calculateMessageSimilarity(String msg1, String msg2) {
    // 移除标点符号和空格
    final clean1 = msg1.replaceAll(RegExp(r'[^\u4e00-\u9fa5a-zA-Z0-9]'), '');
    final clean2 = msg2.replaceAll(RegExp(r'[^\u4e00-\u9fa5a-zA-Z0-9]'), '');
    
    if (clean1.isEmpty || clean2.isEmpty) return 0.0;
    
    // 关键词匹配
    final keywords1 = _extractKeywords(clean1);
    final keywords2 = _extractKeywords(clean2);
    
    int matchCount = 0;
    for (final keyword in keywords1) {
      if (keywords2.contains(keyword)) {
        matchCount++;
      }
    }
    
    return matchCount / (keywords1.length + keywords2.length - matchCount);
  }

  /// 提取关键词
  static List<String> _extractKeywords(String text) {
    final emotionWords = ['爱', '喜欢', '恨', '怕', '担心', '焦虑', '开心', '难过', '生气'];
    final relationWords = ['他', '她', '我们', '感情', '关系', '复合', '分手', '前任'];
    final careerWords = ['工作', '职业', '跳槽', '升职', '老板', '同事', '公司'];
    final decisionWords = ['选择', '决定', '应该', '要不要', '怎么办'];
    
    final allKeywords = [...emotionWords, ...relationWords, ...careerWords, ...decisionWords];
    
    return allKeywords.where((keyword) => text.contains(keyword)).toList();
  }

  /// 分析心理卡点
  static Map<String, String> _analyzeBlockPoint(String currentMessage, String previousMessage) {
    final lowerCurrent = currentMessage.toLowerCase();
    
    // 情感需求未满足
    if (lowerCurrent.contains('为什么') && (lowerCurrent.contains('不') || lowerCurrent.contains('没有'))) {
      return {
        'type': 'emotional_need',
        'blockPoint': '情感需求认知缺失',
        'deeperIntent': '用户可能在寻求情感价值确认，而不只是事实答案',
      };
    }
    
    // 控制感缺失
    if (lowerCurrent.contains('怎么') && (lowerCurrent.contains('让') || lowerCurrent.contains('使'))) {
      return {
        'type': 'control_need',
        'blockPoint': '控制感焦虑',
        'deeperIntent': '用户渴望掌控局面，需要探索对不确定性的恐惧',
      };
    }
    
    // 确定性寻求
    if (lowerCurrent.contains('会') && (lowerCurrent.contains('吗') || lowerCurrent.contains('不会'))) {
      return {
        'type': 'certainty_seeking',
        'blockPoint': '不确定性恐惧',
        'deeperIntent': '用户在逃避面对未知，需要探索对失败的恐惧',
      };
    }
    
    // 自我价值确认
    if (lowerCurrent.contains('我') && (lowerCurrent.contains('应该') || lowerCurrent.contains('值得'))) {
      return {
        'type': 'self_worth',
        'blockPoint': '自我价值怀疑',
        'deeperIntent': '用户需要外部确认自己的价值，反映内在自信不足',
      };
    }
    
    return {
      'type': 'general_repeat',
      'blockPoint': '信息消化困难',
      'deeperIntent': '用户可能需要更具体的行动指导或情感支持',
    };
  }

  /// 构建心理引导式AI Prompt（包含重复问题分析）
  static String _buildPsychologicalGuidancePrompt(String userMessage, TarotCard focusCard, PsychologicalPattern pattern, Map<String, dynamic> repetitionAnalysis) {
    
    // 根据重复模式调整prompt策略
    String repetitionGuidance = '';
    if (repetitionAnalysis['isRepetitive'] == true) {
      final intensityLevel = repetitionAnalysis['intensityLevel'];
      final blockPoint = repetitionAnalysis['blockPoint'];
      final deeperIntent = repetitionAnalysis['deeperIntent'];
      
      if (intensityLevel == 'alert') {
        repetitionGuidance = '''
        
【重复问题警觉】用户第二次问类似问题，需要温和提醒并探索更深层需求：
- 察觉到用户可能对之前的回答不够满意
- 温和地询问是否还有其他担心的点
- 开始探索问题背后的真正需求
        ''';
      } else if (intensityLevel == 'deep_exploration') {
        repetitionGuidance = '''
        
【深层挖掘模式】用户已第三次问类似问题，需要直接切入核心：
- 识别的心理卡点：$blockPoint
- 深层意图分析：$deeperIntent
- 需要直接而温暖地指出重复模式
- 引导用户觉察自己的真正恐惧和需求
- 提供具体的心理突破方向
        ''';
      }
    }
    
    final basePrompt = '''
作为专业的心理引导师和塔罗解读师，请基于用户的回复生成个性化的引导回复。$repetitionGuidance

用户回复: "$userMessage"
焦点卡牌: ${focusCard.name}
识别的心理模式: ${pattern.toString()}

请根据识别的心理模式，生成一个温暖、共情且具有引导性的回复：
''';

    switch (pattern) {
      case PsychologicalPattern.attachment:
        return '''$basePrompt

【依恋模式引导原则】
- 表达理解和共情，避免评判
- 温柔地探索依恋背后的需求
- 引导觉察童年时期的情感模式
- 提供安全感和支持
- 示例风格："我能感受到你对这段关系的深深眷恋..."

请生成一个不超过60字的简洁回复，包含共情确认和一个深入问题。''';

      case PsychologicalPattern.fear:
        return '''$basePrompt

【恐惧模式引导原则】
- 创造安全的对话空间
- 确认恐惧情绪的合理性
- 探索恐惧背后的需求和保护机制
- 引导用户与内在小孩对话
- 示例风格："感谢你分享内心的担忧..."

请生成一个不超过60字的简洁回复，包含安全确认和一个温柔问题。''';

      case PsychologicalPattern.rumination:
        return '''$basePrompt

【内耗模式引导原则】
- 邀请回到当下，停止思维循环
- 引导觉察思维模式
- 探索内耗背后的深层恐惧
- 提供正念和觉察的工具
- 示例风格："我注意到你的思绪似乎在不停运转..."

请生成一个不超过60字的简洁回复，包含觉察邀请和一个探索问题。''';

      case PsychologicalPattern.healthy:
        return '''$basePrompt

【健康阳光模式引导原则】
- 确认和强化正向状态
- 探索成长的可能性
- 引导设定更高的目标
- 支持持续的自我发展
- 示例风格："很高兴看到你的积极状态..."

请生成一个不超过60字的简洁回复，包含正向确认和一个成长问题。''';

      default:
        return '''$basePrompt

【通用引导原则】
- 深度共鸣用户的分享
- 结合塔罗卡牌的象征意义
- 引导继续探索内在真相
- 保持温暖和支持性
- 示例风格："感谢你的真诚分享..."

请生成一个不超过60字的简洁回复，包含深度共鸣和一个探索问题。''';
    }
  }

  /// 流式输出文本的通用方法
  static Stream<String> _streamResponse(String text, int delayMs) async* {
    final words = text.split(' ');
        String currentText = '';
        
        for (int i = 0; i < words.length; i++) {
          currentText += words[i];
          if (i < words.length - 1) currentText += ' ';
          
          yield currentText;
      await Future.delayed(Duration(milliseconds: delayMs));
    }
  }

  /// 基于模板生成引导式回复（当AI API不可用时的fallback）
  static String _generateGuidedResponseFromTemplate(String userMessage, TarotCard focusCard, PsychologicalPattern pattern, [Map<String, dynamic>? repetitionAnalysis]) {
    // 优先处理重复问题
    if (repetitionAnalysis != null && repetitionAnalysis['isRepetitive'] == true) {
      return _generateRepetitionHandlingResponse(userMessage, focusCard, repetitionAnalysis);
    }
    
    // 分析用户回复中的关键词来个性化回复
    final lowerMessage = userMessage.toLowerCase();
    final hasEmotionalWords = _containsEmotionalWords(lowerMessage);
    final emotionalIntensity = _assessEmotionalIntensity(lowerMessage);
    
    // 根据识别的心理模式和用户情绪生成个性化回复
    switch (pattern) {
      case PsychologicalPattern.attachment:
        return _generateAttachmentTemplateResponse(userMessage, focusCard, emotionalIntensity);
      case PsychologicalPattern.fear:
        return _generateFearTemplateResponse(userMessage, focusCard, emotionalIntensity);
      case PsychologicalPattern.rumination:
        return _generateRuminationTemplateResponse(userMessage, focusCard, emotionalIntensity);
      case PsychologicalPattern.healthy:
        return _generateHealthyTemplateResponse(userMessage, focusCard, emotionalIntensity);
      default:
        return _generateGeneralTemplateResponse(userMessage, focusCard, emotionalIntensity);
    }
  }

  /// 生成重复问题处理回复
  static String _generateRepetitionHandlingResponse(String userMessage, TarotCard focusCard, Map<String, dynamic> repetitionAnalysis) {
    final intensityLevel = repetitionAnalysis['intensityLevel'];
    final blockPoint = repetitionAnalysis['blockPoint'];
    final deeperIntent = repetitionAnalysis['deeperIntent'];
    final repetitionType = repetitionAnalysis['repetitionType'];
    
    if (intensityLevel == 'alert') {
      // 第二次重复 - 温和警觉模式
             final alertResponses = [
         '💜 我注意到你似乎对这个话题特别关心，你的问题背后可能有更深的需求',
         '🔮 我观察到你再次回到这个问题，让我们从另一个角度来探索',
         '✨ 我感受到你内心的重视，也许我们可以探索一下这种重复关注的原因？',
       ];
      return alertResponses[userMessage.length % alertResponses.length];
    } else if (intensityLevel == 'deep_exploration') {
      // 第三次重复 - 深层挖掘模式
      switch (repetitionType) {
                 case 'emotional_need':
           return '💡 我发现你一直在问类似的问题，这可能反映了$blockPoint。我观察到：你真正需要的也许不是答案，而是内心的安全感和被理解的感觉。你觉得呢？';
         case 'control_need':
           return '🎯 你已经问了几次类似问题，我观察到这背后可能是$blockPoint。让我们思考：什么让你感到必须要掌控这个结果？放下控制会发生什么？';
         case 'certainty_seeking':
           return '🌟 这是你第三次问类似问题了，$blockPoint在呼唤我们关注。我想温柔地问：你在害怕什么不确定的结果？那个"不知道"的空间里藏着什么恐惧？';
         case 'self_worth':
           return '💎 我注意到你反复回到这个话题，这可能与$blockPoint有关。作为塔罗师我想告诉你：你不需要外界的确认就值得被爱。这种重复的询问背后，是什么让你怀疑自己的价值？';
         default:
           return '🔍 你多次回到这个问题，这本身就是一个重要信息。我观察到：有时候重复本身就是答案 - $deeperIntent。让我们停下来，感受一下这种"必须要问"的冲动来自哪里？';
      }
    }
    
    // 默认温和提醒
    return '💫 我感受到这个话题对你很重要，让我们换个角度来探索吧。';
  }

  /// 检测情绪词汇
  static bool _containsEmotionalWords(String text) {
    final emotionalWords = ['痛苦', '难受', '开心', '高兴', '害怕', '担心', '焦虑', '激动', '失望', '愤怒'];
    return emotionalWords.any((word) => text.contains(word));
  }

  /// 评估情绪强度
  static String _assessEmotionalIntensity(String text) {
    if (text.contains('特别') || text.contains('非常') || text.contains('极其') || text.contains('超级')) {
      return 'high';
    } else if (text.contains('有点') || text.contains('稍微') || text.contains('还好')) {
      return 'low';
    }
    return 'medium';
  }

  /// 依恋模式模板回复（基于用户内容个性化）
  static String _generateAttachmentTemplateResponse(String userMessage, TarotCard focusCard, String intensity) {
    final intensityPrefix = intensity == 'high' ? '我深深地' : intensity == 'low' ? '我' : '我能够';
    
    // 提取用户消息中的关键信息
    final hasTimeReference = userMessage.contains('年') || userMessage.contains('月') || userMessage.contains('很久');
    final timePhrase = hasTimeReference ? '这段时间以来的感受' : '这种体验';
    
    final questions = [
      '这种连接背后，是否有某些未被满足的情感需求在呼唤？',
      '在你的成长过程中，是否也有过类似的依恋体验？',
      '如果让这份眷恋告诉你它最需要什么，会是什么呢？',
    ];
    
    final selectedQuestion = questions[userMessage.length % questions.length];
    
    return '''💜 $intensityPrefix感受到你的$timePhrase，从卡面我看到了你内心的渴望。

$selectedQuestion''';
  }

  /// 恐惧模式模板回复
  static String _generateFearTemplateResponse(String userMessage, TarotCard focusCard, String intensity) {
    final supportLevel = intensity == 'high' ? '完全理解' : intensity == 'low' ? '注意到' : '感受到';
    
    final hasSpecificFear = userMessage.contains('害怕') || userMessage.contains('担心') || userMessage.contains('怕');
    final fearPhrase = hasSpecificFear ? '这个担忧' : '你内心的不安';
    
    final questions = [
      '如果这份恐惧有一个声音，它最想保护你免受什么伤害？',
      '在什么样的情况下，你会感到最有安全感？',
      '这种担心是否在你过去的经历中也出现过？',
    ];
    
    final selectedQuestion = questions[userMessage.hashCode % questions.length];
    
    return '''🤗 $supportLevel你的勇气，我从牌面感受到$fearPhrase是内在智慧的表达。

$selectedQuestion''';
  }

  /// 内耗模式模板回复
  static String _generateRuminationTemplateResponse(String userMessage, TarotCard focusCard, String intensity) {
    final awarenessLevel = intensity == 'high' ? '清晰地感受到' : intensity == 'low' ? '观察到' : '注意到';
    
    final hasDecisionStruct = userMessage.contains('选择') || userMessage.contains('决定') || userMessage.contains('应该');
    final thinkingPhrase = hasDecisionStruct ? '这种纠结的选择' : '思维的运转';
    
    final questions = [
      '在这些想法背后，什么是你最不愿意面对的？',
      '如果暂停思考，直接跟随内心的声音，它会说什么？',
      '这种反复思考是在保护你避免什么结果？',
    ];
    
    final selectedQuestion = questions[(userMessage.length + focusCard.name.length) % questions.length];
    
    return '''🧘 $awarenessLevel你的$thinkingPhrase，我建议我们暂停一下。

$selectedQuestion''';
  }

  /// 健康阳光模式模板回复
  static String _generateHealthyTemplateResponse(String userMessage, TarotCard focusCard, String intensity) {
    final positivityLevel = intensity == 'high' ? '非常欣喜地看到' : intensity == 'low' ? '感受到' : '看到';
    
    final hasGrowthWords = userMessage.contains('成长') || userMessage.contains('进步') || userMessage.contains('更好');
    final statePhrase = hasGrowthWords ? '这种持续成长的状态' : '你的正向能量';
    
    final questions = [
      '在这种和谐中，你最想在哪个领域继续拓展？',
      '这种积极状态给你的生活带来了什么新的可能性？',
      '你觉得还有什么地方可以让这种美好更加圆满？',
    ];
    
    final selectedQuestion = questions[DateTime.now().millisecond % questions.length];
    
    return '''🌟 $positivityLevel$statePhrase，我感受到了很好的能量共振。

$selectedQuestion''';
  }

  /// 通用模板回复
  static String _generateGeneralTemplateResponse(String userMessage, TarotCard focusCard, String intensity) {
    final resonanceLevel = intensity == 'high' ? '深深地' : intensity == 'low' ? '温柔地' : '';
    
    final hasQuestionWords = userMessage.contains('为什么') || userMessage.contains('怎么') || userMessage.contains('如何');
    final sharingPhrase = hasQuestionWords ? '你的探索精神' : '你的真诚分享';
    
    final questions = [
      '这个话题还有什么地方让你特别好奇？',
      '如果${focusCard.name}能对你说一句话，你觉得会是什么？',
      '在这次对话中，什么感受最强烈？',
    ];
    
    final selectedQuestion = questions[(userMessage.hashCode + focusCard.id.hashCode) % questions.length];
    
    return '''💎 $resonanceLevel感谢$sharingPhrase，我感受到了深层的内在连接。

$selectedQuestion''';
  }

  /// Fallback后续回复 - 更自然的个性化回复
  static Stream<String> _getFallbackFollowUpResponse(String userMessage) async* {
    // 分析用户消息内容，生成个性化回复
    final response = _generatePersonalizedResponse(userMessage);
    yield* _streamResponse(response, 15);
  }

  /// 生成个性化回复（基于用户消息内容）
  static String _generatePersonalizedResponse(String userMessage) {
    final message = userMessage.toLowerCase();
    
    // 情感类回复
    if (message.contains('开心') || message.contains('高兴') || message.contains('好') || message.contains('满意')) {
      return '💫 我感受到你的喜悦能量！这种积极的状态值得庆祝。还有什么让你感到好奇的地方吗？';
    }
    
    if (message.contains('难过') || message.contains('痛苦') || message.contains('困难') || message.contains('不容易')) {
      return '🤗 我理解这种感受不容易，从你的分享中我看到了你的勇气。让我们继续探索这背后的意义，好吗？';
    }
    
    if (message.contains('困惑') || message.contains('不明白') || message.contains('为什么') || message.contains('怎么')) {
      return '🔍 我观察到你渴望更深的理解，这种探索精神很珍贵。让我们从另一个角度来看看这个情况。';
    }
    
    if (message.contains('害怕') || message.contains('担心') || message.contains('焦虑') || message.contains('不安')) {
      return '🌟 感谢你分享内心的担忧，这需要很大勇气。这种不安是否在提醒我们关注什么重要的事情？';
    }
    
    // 关系类回复
    if (message.contains('他') || message.contains('她') || message.contains('对方') || message.contains('关系')) {
      return '💕 我听到了你对这段关系的深深关注。你觉得在这个连接中，什么是最需要被理解的？';
    }
    
    // 工作/事业类回复
    if (message.contains('工作') || message.contains('事业') || message.contains('公司') || message.contains('职业')) {
      return '🎯 从你的分享中，我感受到了你对事业的重视。这个挑战可能正在为你打开新的可能性。';
    }
    
    // 决定/选择类回复
    if (message.contains('选择') || message.contains('决定') || message.contains('应该') || message.contains('还是')) {
      return '⚖️ 我注意到你正在一个重要的选择点。有时候，最智慧的答案就藏在你的直觉深处。';
    }
    
    // 默认回复（更温暖自然）
    final responses = [
      '✨ 我从你的分享中感受到了真诚，这种开放很珍贵。还有什么想要探索的吗？',
      '🌙 每个人的内在旅程都是独特的，你正在勇敢地探索自己。我们继续这个对话吧。',
      '💎 我观察到你有很好的自我觉察能力。在这次交流中，什么感受对你来说最强烈？',
    ];
    
    return responses[userMessage.length % responses.length];
  }

  /// 生成后续对话回复 - 非流式版本（兼容性保留）
  static Future<String> generateFollowUpResponse({
    required String userMessage,
    required String previousReading,
    required List<TarotCard> cards,
  }) async {
    try {
      final requestData = {
        'userMessage': userMessage,
        'previousReading': previousReading,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': card.name,
          'description': card.description,
          'meaning': card.meaning,
          'keywords': card.keywords,
          'isReversed': card.isReversed,
        }).toList(),
        'requestType': 'follow_up',
      };

      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      if (response.data != null && response.data['success'] == true) {
        return response.data['response'] as String;
      } else {
        throw Exception('Supabase函数调用失败: ${response.data?['error'] ?? '未知错误'}');
      }
    } catch (e) {
      print('AI后续回复服务失败: $e');
      return '感谢您的问题。基于之前的解读，我建议您继续关注塔罗牌给出的指引，相信自己的直觉，积极行动。如果您还有其他疑问，我很乐意为您解答。';
    }
  }

  /// 构建塔罗解读的提示词
  static String _buildTarotPrompt(String question, List<TarotCard> cards, String spreadType) {
    final cardInfo = cards.map((card) {
      final position = card.isReversed ? '（逆位）' : '（正位）';
      final meaning = card.isReversed
          ? TarotCardsData.getReversedMeaning(card)
          : card.meaning;
      final keywords = card.isReversed
          ? TarotCardsData.getFullKeywords(card)
          : card.keywords;

      return '${card.name}$position - ${card.description}\n关键词: ${keywords.join(", ")}\n含义: $meaning';
    }).join('\n\n');

    return '''
用户问题: $question
牌阵类型: $spreadType
抽取的塔罗牌:

$cardInfo

请作为专业塔罗占卜师，结合用户的问题和抽取的牌，提供深入、有洞察力的解读。
特别注意：
- 正位牌代表正面能量、顺利发展、积极状态
- 逆位牌代表阻碍、内在问题、需要反思的领域
- 请根据每张牌的正逆位状态给出相应的解读

解读应该包含：
1. 当前状况分析
2. 指引建议
3. 未来展望
4. 具体的行动建议

请用温暖、专业的语调，给出积极正面的指引。
''';
  }

  /// fallback解读流式生成 - 当API调用失败时使用
  static Stream<String> _getFallbackReadingStream(String question, List<TarotCard> cards, String spreadType) async* {
    final reading = _getFallbackReading(question, cards, spreadType);
    final words = reading.split(' ');
    String currentText = '';
    
    for (int i = 0; i < words.length; i++) {
      currentText += words[i];
      if (i < words.length - 1) currentText += ' ';
      
      yield currentText;
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// fallback解读 - 当API调用失败时使用
  static String _getFallbackReading(String question, List<TarotCard> cards, String spreadType, [LanguageManager? languageManager]) {
    // 如果没有传入languageManager，使用默认中文
    final cardText = cards.length == 1 ? '这张牌' : '这${cards.length}张牌';
    final mainCard = cards.isNotEmpty ? cards.first : null;

    if (languageManager != null) {
      final translatedCardText = cards.length == 1 ?
        languageManager.translate('this_card') :
        '${languageManager.translate('these')}${cards.length}${languageManager.translate('cards')}';

      return '''${languageManager.translate('based_on_question_and_cards').replaceAll('{question}', question).replaceAll('{cardText}', translatedCardText)}：

🔮 **${languageManager.translate('current_situation')}**
${mainCard != null ? '${mainCard.name}${languageManager.translate('shows')}' : languageManager.translate('current')}${languageManager.translate('fallback_reading_current').replaceAll('{cardText}', translatedCardText)}

✨ **${languageManager.translate('guidance_advice')}**
${languageManager.translate('fallback_reading_guidance')}

🌟 **${languageManager.translate('future_outlook')}**
${languageManager.translate('fallback_reading_future')}

💫 **${languageManager.translate('tarot_advice')}**
${_getSpreadSpecificAdvice(spreadType, languageManager)}

${languageManager.translate('ask_more_questions')}''';
    } else {
      // 默认中文版本
      return '''根据您的问题"$question"和抽取的$cardText，我看到：

🔮 **当前状况**
${mainCard != null ? '${mainCard.name}显示' : '当前'}您正处在一个重要的时刻。$cardText提醒您要相信自己的内在智慧和直觉。

✨ **指引建议**
保持开放的心态，接受即将到来的变化。这些变化虽然可能带来挑战，但最终会引导您走向更好的未来。

🌟 **未来展望**
前方的道路充满希望。只要您保持积极的态度和坚定的信念，成功就在不远处等待着您。

💫 **塔罗建议**
${_getSpreadSpecificAdvice(spreadType, languageManager)}

您还有什么想要深入了解的吗？''';
    }
  }

  static String _getSpreadSpecificAdvice(String spreadType, [LanguageManager? languageManager]) {
    if (languageManager != null) {
      switch (spreadType) {
        case '单张牌':
          return languageManager.translate('single_card_advice');
        case '三张牌':
          return languageManager.translate('three_card_advice');
        case '凯尔特十字':
          return languageManager.translate('celtic_cross_advice');
        default:
          return languageManager.translate('default_spread_advice');
      }
    } else {
      // 默认中文版本
      switch (spreadType) {
        case '单张牌':
          return '这张牌提醒您要专注于当下，相信内心的声音会为您指引正确的方向。';
        case '三张牌':
          return '过去的经验、现在的选择和未来的可能性都在这三张牌中得到了完美的体现。';
        case '凯尔特十字':
          return '这个复杂的牌阵揭示了您生活中各个层面的深层含义，需要综合考虑所有因素。';
        default:
          return '这个牌阵为您提供了全面而深入的指引，帮助您更好地理解当前的处境。';
      }
    }
  }
} 