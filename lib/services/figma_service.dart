import 'dart:convert';
import 'package:http/http.dart' as http;

class FigmaService {
  static const String baseUrl = 'https://api.figma.com/v1';
  final String accessToken;
  
  FigmaService({required this.accessToken});
  
  /// 获取Figma文件信息
  Future<Map<String, dynamic>?> getFile(String fileKey) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/files/$fileKey'),
        headers: {
          'X-Figma-Token': accessToken,
        },
      );
      
      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
    } catch (e) {
      print('Error fetching Figma file: $e');
    }
    return null;
  }
  
  /// 获取组件图片
  Future<Map<String, String>?> getImages(String fileKey, List<String> nodeIds) async {
    try {
      final nodeIdsParam = nodeIds.join(',');
      final response = await http.get(
        Uri.parse('$baseUrl/images/$fileKey?ids=$nodeIdsParam&format=png&scale=2'),
        headers: {
          'X-Figma-Token': accessToken,
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Map<String, String>.from(data['images'] ?? {});
      }
    } catch (e) {
      print('Error fetching Figma images: $e');
    }
    return null;
  }
  
  /// 下载图片到本地
  Future<bool> downloadImage(String url, String localPath) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        // 在实际应用中，这里需要使用文件系统API保存图片
        print('Image downloaded: $localPath');
        return true;
      }
    } catch (e) {
      print('Error downloading image: $e');
    }
    return false;
  }
  
  /// 解析Figma文件中的设计token
  Map<String, dynamic> extractDesignTokens(Map<String, dynamic> figmaFile) {
    final tokens = <String, dynamic>{};
    
    // 提取颜色
    final styles = figmaFile['styles'] as Map<String, dynamic>? ?? {};
    final colors = <String, String>{};
    
    styles.forEach((key, value) {
      if (value['styleType'] == 'FILL') {
        final name = value['name'] as String;
        final fills = value['fills'] as List? ?? [];
        if (fills.isNotEmpty) {
          final fill = fills.first;
          if (fill['type'] == 'SOLID') {
            final color = fill['color'];
            final r = (color['r'] * 255).round();
            final g = (color['g'] * 255).round();
            final b = (color['b'] * 255).round();
            final a = (color['a'] * 255).round();
            colors[name] = 'Color.fromARGB($a, $r, $g, $b)';
          }
        }
      }
    });
    
    tokens['colors'] = colors;
    
    // 提取文本样式
    final textStyles = <String, Map<String, dynamic>>{};
    styles.forEach((key, value) {
      if (value['styleType'] == 'TEXT') {
        final name = value['name'] as String;
        final style = value['style'] as Map<String, dynamic>? ?? {};
        textStyles[name] = {
          'fontSize': style['fontSize'],
          'fontWeight': _convertFontWeight(style['fontWeight']),
          'fontFamily': style['fontFamily'],
        };
      }
    });
    
    tokens['textStyles'] = textStyles;
    
    return tokens;
  }
  
  String _convertFontWeight(dynamic weight) {
    switch (weight) {
      case 100: return 'FontWeight.w100';
      case 200: return 'FontWeight.w200';
      case 300: return 'FontWeight.w300';
      case 400: return 'FontWeight.w400';
      case 500: return 'FontWeight.w500';
      case 600: return 'FontWeight.w600';
      case 700: return 'FontWeight.w700';
      case 800: return 'FontWeight.w800';
      case 900: return 'FontWeight.w900';
      default: return 'FontWeight.normal';
    }
  }
}

/// Figma设计token管理器
class FigmaDesignTokens {
  static const Map<String, String> colors = {
    'primary': '#6B46C1',
    'secondary': '#EC4899',
    'accent': '#F59E0B',
    'background': '#1F2937',
    'surface': '#374151',
    'onPrimary': '#FFFFFF',
    'onSecondary': '#FFFFFF',
    'onBackground': '#F9FAFB',
    'onSurface': '#F9FAFB',
  };
  
  static const Map<String, Map<String, dynamic>> textStyles = {
    'headline1': {
      'fontSize': 32.0,
      'fontWeight': 'FontWeight.bold',
      'fontFamily': 'Inter',
    },
    'headline2': {
      'fontSize': 24.0,
      'fontWeight': 'FontWeight.bold',
      'fontFamily': 'Inter',
    },
    'body1': {
      'fontSize': 16.0,
      'fontWeight': 'FontWeight.normal',
      'fontFamily': 'Inter',
    },
    'body2': {
      'fontSize': 14.0,
      'fontWeight': 'FontWeight.normal',
      'fontFamily': 'Inter',
    },
    'caption': {
      'fontSize': 12.0,
      'fontWeight': 'FontWeight.normal',
      'fontFamily': 'Inter',
    },
  };
  
  static const Map<String, double> spacing = {
    'xs': 4.0,
    'sm': 8.0,
    'md': 16.0,
    'lg': 24.0,
    'xl': 32.0,
    'xxl': 48.0,
  };
  
  static const Map<String, double> borderRadius = {
    'sm': 4.0,
    'md': 8.0,
    'lg': 12.0,
    'xl': 16.0,
    'full': 999.0,
  };
}
