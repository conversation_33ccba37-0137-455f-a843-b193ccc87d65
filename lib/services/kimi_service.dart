import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/services/langfuse_service.dart';

class KimiService {
  static final _supabase = Supabase.instance.client;
  static const String _baseUrl = 'https://api.moonshot.cn/v1';
  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_KIMI_API_KEY', // 这里应该从环境变量获取
  };

  // 不再需要直接配置API Key，通过Supabase Edge Function调用

  /// 通过Supabase Edge Function调用Kimi API（使用messages格式）
  static Future<Map<String, dynamic>> generateResponseWithMessages({
    required List<Map<String, String>> messages,
    required String traceId,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async {
    final startTime = DateTime.now();

    try {
      // 1. 构建请求体，使用messages格式
      final requestBody = {
        'messages': messages, // 标准messages格式
        'requestType': 'chat_with_context', // 带上下文的聊天请求
        'userLanguage': 'zh', // 默认中文
        'maxLength': maxTokens,
        'temperature': temperature,
        'traceId': traceId,
        'provider': 'kimi', // 指定使用Kimi
      };

      // 2. 调用Supabase Edge Function
      final response = await _supabase.functions.invoke(
        'kimi-chat', // 新的Kimi Edge Function
        body: requestBody,
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      if (response.data != null) {
        final data = response.data as Map<String, dynamic>;
        
        if (data['success'] == true) {
          final content = data['content'] as String? ?? data['reading'] as String? ?? '';
          final usage = data['usage'] as Map<String, dynamic>? ?? {};
          
          // 记录成功的调用到Langfuse
          await LangfuseService.createGeneration(
            traceId: traceId,
            name: 'kimi_chat_completion',
            input: {'messages': messages},
            output: {'content': content},
            model: 'moonshot-v1-8k',
            metadata: {
              'temperature': temperature,
              'max_tokens': maxTokens,
              'duration_ms': duration.inMilliseconds,
              'input_tokens': usage['prompt_tokens'] ?? 0,
              'output_tokens': usage['completion_tokens'] ?? 0,
              'total_tokens': usage['total_tokens'] ?? 0,
              ...?metadata,
            },
          );

          return {
            'success': true,
            'content': content,
            'response': content, // 兼容现有代码
            'usage': usage,
            'duration_ms': duration.inMilliseconds,
          };
        } else {
          // Edge Function调用失败，记录错误
          final errorMsg = 'Kimi Edge Function错误: ${data['error'] ?? 'Unknown error'}';
          await LangfuseService.createEvent(
            traceId: traceId,
            name: 'kimi_edge_function_error',
            input: {'messages': messages},
            output: {'error': errorMsg},
            metadata: {
              'duration_ms': duration.inMilliseconds,
            },
          );

          return {
            'success': false,
            'error': errorMsg,
          };
        }
      } else {
        // 响应为空
        final errorMsg = 'Kimi Edge Function返回空响应';
        await LangfuseService.createEvent(
          traceId: traceId,
          name: 'kimi_empty_response',
          input: {'messages': messages},
          output: {'error': errorMsg},
          metadata: {
            'duration_ms': duration.inMilliseconds,
          },
        );

        return {
          'success': false,
          'error': errorMsg,
        };
      }
    } catch (e) {
      // 网络或其他错误
      final errorMsg = 'Kimi调用异常: $e';
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'kimi_call_exception',
        input: {'messages': messages},
        output: {'error': errorMsg},
        metadata: {
          'exception': e.toString(),
        },
      );

      return {
        'success': false,
        'error': errorMsg,
      };
    }
  }

  /// 通过Supabase Edge Function调用Kimi API并追踪到Langfuse
  static Future<Map<String, dynamic>> generateResponse({
    required String prompt,
    required String traceId,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async {
    final startTime = DateTime.now();
    
    try {
      // 1. 构建请求体，兼容现有Edge Function格式
      final requestBody = {
        'question': prompt, // 使用question字段而不是prompt
        'cards': [], // 空的卡牌数组
        'spreadType': 'chat', // 标识为聊天类型
        'requestType': 'simple_chat', // 简单聊天请求
        'userLanguage': 'zh', // 默认中文
        'userMessage': prompt,
        'conversationHistory': [],
        'maxLength': maxTokens,
        'temperature': temperature,
        'provider': 'kimi', // 指定使用Kimi
      };

      // 2. 调用Supabase Edge Function
      final response = await _supabase.functions.invoke(
        'kimi-chat', // 新的Kimi Edge Function
        body: requestBody,
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      if (response.data != null) {
        final data = response.data as Map<String, dynamic>;
        
        if (data['success'] == true) {
          final content = data['content'] as String? ?? data['reading'] as String? ?? '';
          final usage = data['usage'] as Map<String, dynamic>? ?? {};
          
          // 记录成功的调用到Langfuse
          await LangfuseService.createGeneration(
            traceId: traceId,
            name: 'kimi_simple_chat',
            input: {'prompt': prompt},
            output: {'content': content},
            model: 'moonshot-v1-8k',
            metadata: {
              'temperature': temperature,
              'max_tokens': maxTokens,
              'duration_ms': duration.inMilliseconds,
              'input_tokens': usage['prompt_tokens'] ?? 0,
              'output_tokens': usage['completion_tokens'] ?? 0,
              'total_tokens': usage['total_tokens'] ?? 0,
              ...?metadata,
            },
          );

          return {
            'success': true,
            'content': content,
            'response': content, // 兼容现有代码
            'usage': usage,
            'duration_ms': duration.inMilliseconds,
          };
        } else {
          // Edge Function调用失败，记录错误
          final errorMsg = 'Kimi Edge Function错误: ${response.data}';
          await LangfuseService.createEvent(
            traceId: traceId,
            name: 'kimi_edge_function_error',
            input: {'prompt': prompt},
            output: {'error': errorMsg},
            metadata: {
              'duration_ms': duration.inMilliseconds,
            },
          );

          return {
            'success': false,
            'error': errorMsg,
          };
        }
      } else {
        // 响应为空
        final errorMsg = 'Kimi Edge Function返回空响应';
        await LangfuseService.createEvent(
          traceId: traceId,
          name: 'kimi_empty_response',
          input: {'prompt': prompt},
          output: {'error': errorMsg},
          metadata: {
            'duration_ms': duration.inMilliseconds,
          },
        );

        return {
          'success': false,
          'error': errorMsg,
        };
      }
    } catch (e) {
      // 网络或其他错误
      final errorMsg = 'Kimi调用异常: $e';
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'kimi_call_exception',
        input: {'prompt': prompt},
        output: {'error': errorMsg},
        metadata: {
          'exception': e.toString(),
        },
      );

      return {
        'success': false,
        'error': errorMsg,
      };
    }
  }

  /// 生成肯定语句
  static Future<String> generateAffirmation(String prompt, {String? language}) async {
    try {
      final response = await KimiService.generateResponse(
        prompt: prompt,
        traceId: 'affirmation_${DateTime.now().millisecondsSinceEpoch}',
        temperature: 0.8,
        maxTokens: 200,
      );

      if (response['success'] == true) {
        return response['content'] as String;
      } else {
        throw Exception(response['error'] ?? '生成肯定语失败');
      }
    } catch (e) {
      debugPrint('生成肯定语失败: $e');
      // 返回默认肯定语
      return language?.startsWith('en') == true 
          ? 'I deserve a beautiful life, and my goals are manifesting.'
          : '我值得拥有美好的生活，我的目标正在实现。';
    }
  }

  /// 验证API密钥是否有效
  static Future<bool> validateApiKey() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/models'),
        headers: _headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
