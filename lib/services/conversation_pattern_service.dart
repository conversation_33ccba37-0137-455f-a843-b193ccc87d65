
/// 重复模式类型
enum RepetitionPattern {
  emotionalLoop,        // 情感循环：不断询问同一个情感问题
  validationSeeking,    // 寻求验证：希望得到想要的答案
  realityDenial,        // 现实否认：拒绝接受明显的事实
  anxietySpiral,        // 焦虑螺旋：因恐惧而反复确认
  attachmentTrauma,     // 依恋创伤：无法放下的深层创伤
}

/// 对话模式识别服务 - 专门识别重复问题并提供心理引导
class ConversationPatternService {
  static const int _repetitionThreshold = 2; // 重复2次开始警觉
  static const int _interventionThreshold = 3; // 重复3次开始干预
  
  /// 分析对话历史中的重复模式
  static Map<String, dynamic> analyzeConversationPattern(List<String> userMessages) {
    final patterns = _detectRepetitionPatterns(userMessages);
    final coreIssue = _identifyCoreIssue(userMessages);
    final repetitionCount = _countSimilarQuestions(userMessages);
    
    return {
      'hasRepetition': repetitionCount >= _repetitionThreshold,
      'needsIntervention': repetitionCount >= _interventionThreshold,
      'repetitionCount': repetitionCount,
      'pattern': patterns,
      'coreIssue': coreIssue,
      'interventionLevel': _getInterventionLevel(repetitionCount),
    };
  }
  
  /// 检测重复模式类型
  static RepetitionPattern _detectRepetitionPatterns(List<String> messages) {
    final messageText = messages.join(' ').toLowerCase();
    
    // 情感循环模式
    if (_containsPattern(messageText, [
      ['复合', '分手', '回来'],
      ['爱', '不爱', '喜欢'],
      ['离开', '回来', '走了']
    ])) {
      return RepetitionPattern.emotionalLoop;
    }
    
    // 寻求验证模式
    if (_containsPattern(messageText, [
      ['真的', '确定', '肯定'],
      ['会不会', '可能', '也许'],
      ['还有', '机会', '希望']
    ])) {
      return RepetitionPattern.validationSeeking;
    }
    
    // 现实否认模式
    if (_containsPattern(messageText, [
      ['但是', '可是', '不过'],
      ['如果', '假如', '要是'],
      ['还是', '依然', '仍然']
    ])) {
      return RepetitionPattern.realityDenial;
    }
    
    // 焦虑螺旋模式
    if (_containsPattern(messageText, [
      ['担心', '害怕', '恐惧'],
      ['怎么办', '怎么样', '如何'],
      ['会', '能', '可以']
    ])) {
      return RepetitionPattern.anxietySpiral;
    }
    
    return RepetitionPattern.attachmentTrauma;
  }
  
  /// 检查是否包含特定模式
  static bool _containsPattern(String text, List<List<String>> patterns) {
    for (final pattern in patterns) {
      int matchCount = 0;
      for (final keyword in pattern) {
        if (text.contains(keyword)) matchCount++;
      }
      if (matchCount >= 2) return true;
    }
    return false;
  }
  
  /// 识别核心问题
  static String _identifyCoreIssue(List<String> messages) {
    final messageText = messages.join(' ').toLowerCase();
    
    if (messageText.contains('复合') || messageText.contains('分手')) {
      return '情感关系的分离与重聚焦虑';
    } else if (messageText.contains('工作') || messageText.contains('职业')) {
      return '职业发展的不确定性恐惧';
    } else if (messageText.contains('选择') || messageText.contains('决定')) {
      return '决策困难与控制感缺失';
    } else {
      return '自我价值与存在意义的质疑';
    }
  }
  
  /// 计算相似问题数量
  static int _countSimilarQuestions(List<String> messages) {
    if (messages.length < 2) return 0;
    
    int repetitionCount = 0;
    final lastMessage = messages.last.toLowerCase();
    
    // 检查与历史消息的相似度
    for (int i = 0; i < messages.length - 1; i++) {
      final similarity = _calculateSimilarity(lastMessage, messages[i].toLowerCase());
      if (similarity > 0.6) { // 60%相似度阈值
        repetitionCount++;
      }
    }
    
    return repetitionCount;
  }
  
  /// 计算文本相似度
  static double _calculateSimilarity(String text1, String text2) {
    final words1 = text1.split(' ').toSet();
    final words2 = text2.split(' ').toSet();
    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;
    return union > 0 ? intersection / union : 0.0;
  }
  
  /// 获取干预级别
  static String _getInterventionLevel(int repetitionCount) {
    if (repetitionCount >= 5) return 'deep_intervention';    // 深度干预
    if (repetitionCount >= 3) return 'gentle_intervention';  // 温和干预
    if (repetitionCount >= 2) return 'awareness_building';   // 意识培养
    return 'normal_response';                                // 正常回应
  }
}

/// 心理引导模板生成器
class PsychologicalGuidanceTemplates {
  /// 生成重复模式干预的Few-Shot示例
  static String generateFewShotExamples() {
    return '''
以下是专业塔罗师处理重复问题的最佳实践示例：

## 示例1：情感循环模式 - 温和干预
**用户重复问题**：
- "他会回来吗？"
- "我们还有可能复合吗？"  
- "真的不会回来了吗？"

**塔罗师回应**：
"我注意到你连续询问了关于他回来的可能性。从卡面来看，你的内心正经历着深深的不确定感。让我们暂停一下对结果的关注，我更想了解：在这段关系中，什么时刻让你感到最有价值？"

**要点**：温和指出重复 → 共情情绪 → 转向内在探索

## 示例2：现实否认模式 - 深度干预
**用户重复表达**：
- "他说要分手，但是..."
- "虽然他很糟糕，但是如果..."
- "我知道他有问题，可是..."

**塔罗师回应**：
"亲爱的，我发现你多次使用'但是'来回避卡牌想告诉你的信息。这很正常，面对痛苦的真相需要勇气。我感受到你内心有一个很深的恐惧，是什么让你如此害怕失去这段关系？即使它给你带来了痛苦？"

**要点**：直接但温和地点出回避 → 认可恐惧情绪 → 探索深层依恋创伤

## 示例3：焦虑螺旋模式 - 引导释放
**用户重复焦虑**：
- "我该怎么办？"
- "如果他不联系我怎么办？"
- "我应该主动吗还是等待？"

**塔罗师回应**：
"我观察到你的问题都围绕着'怎么办'，这反映了你现在承受着很大的焦虑。塔罗牌想提醒你，真正的答案不在于如何控制他的行为，而在于你如何与内在的恐惧相处。告诉我，如果你知道不管发生什么你都会好起来，你还会这样焦虑吗？"

**要点**：识别焦虑模式 → 重新框架问题 → 引导内在力量

## 示例4：依恋创伤模式 - 深层疗愈
**用户重复创伤表达**：
- "他毁了我的生日..."
- "但是他又来找我了..."
- "我知道他不好，为什么还放不下？"

**塔罗师回应**：
"我听到了你内心深处的撕裂，一边是理智的声音告诉你要离开，一边是内在的小孩desperately想要被爱。这种矛盾反复出现，说明这段关系触发了你很早期的一些创伤。你愿意和我一起探索，是什么让你觉得自己'不值得'被好好对待吗？"

**要点**：识别内在冲突 → 连接童年创伤 → 引导自我价值重建

## 核心干预原则：
1. **温和观察**：不批判地指出重复模式
2. **深度共情**：认可和理解用户的痛苦  
3. **重新框架**：从外在控制转向内在探索
4. **核心触达**：触及重复背后的深层恐惧
5. **力量唤醒**：引导用户发现内在资源
''';
  }
  
  /// 根据重复模式生成个性化引导问题
  static String generateGuidanceQuestion(
    RepetitionPattern pattern, 
    String coreIssue,
    int repetitionCount
  ) {
    switch (pattern) {
      case RepetitionPattern.emotionalLoop:
        return repetitionCount >= 3 
          ? "我注意到你多次询问同样的情感问题。让我们深入探索：在这段关系中，什么让你感到最恐惧？"
          : "这个情感问题对你来说很重要。除了关心结果，你内心最需要的是什么？";
          
      case RepetitionPattern.validationSeeking:
        return repetitionCount >= 3
          ? "亲爱的，我发现你在寻求一个特定的答案。真正让你无法接受其他可能性的是什么恐惧？"
          : "我感受到你希望得到确定性。如果暂时放下对结果的执着，你内心真正的渴望是什么？";
          
      case RepetitionPattern.realityDenial:
        return repetitionCount >= 3
          ? "我注意到你多次使用'但是'来回避卡牌的信息。是什么让你如此害怕面对这个真相？"
          : "你的'但是'让我感受到内心的挣扎。什么让接受现实变得如此困难？";
          
      case RepetitionPattern.anxietySpiral:
        return repetitionCount >= 3
          ? "我观察到你陷入了焦虑的循环。如果你知道不管发生什么都会好起来，你还会这样担心吗？"
          : "你的焦虑反映了对失控的恐惧。什么能让你感到更有力量？";
          
      case RepetitionPattern.attachmentTrauma:
        return repetitionCount >= 3
          ? "这种反复的痛苦模式说明触及了很深的创伤。你愿意探索是什么让你觉得自己'不值得'被好好对待吗？"
          : "我感受到你内在小孩的呼唤。什么经历让你形成了这样的关系模式？";
    }
  }
} 