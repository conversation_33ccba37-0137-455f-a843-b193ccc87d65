import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'kimi_service.dart';

/// 专业显化肯定语生成服务
class ManifestationAffirmationService {
  static const String _functionName = 'deepseek-tarot-reading';
  
  /// 生成专业显化肯定语
  static Future<String> generateAffirmation(String goalTitle) async {
    final affirmations = await generateMultipleAffirmations(goalTitle, 1);
    return affirmations.first;
  }

  /// 生成多条专业显化肯定语 - 支持多语言，优先使用AI
  static Future<List<String>> generateMultipleAffirmations(String goalTitle, int count, [String? language]) async {
    try {
      debugPrint('🔮 开始生成$count条显化肯定语: $goalTitle (语言: ${language ?? 'zh-CN'})');

      final lang = language ?? 'zh-CN';
      final affirmations = <String>[];

      // 优先使用AI生成
      for (int i = 0; i < count; i++) {
        try {
          final prompt = _buildAffirmationPrompt(goalTitle, lang, i + 1);
          final aiAffirmation = await _callSupabaseFunction(prompt, lang);

          if (aiAffirmation != null && aiAffirmation.isNotEmpty && !aiAffirmation.contains('抱歉') && !aiAffirmation.contains('Sorry')) {
            affirmations.add(_cleanAffirmationText(aiAffirmation));
            debugPrint('✅ AI生成肯定语 ${i + 1}: ${aiAffirmation.substring(0, aiAffirmation.length > 50 ? 50 : aiAffirmation.length)}...');
          } else {
            throw Exception('AI返回无效内容');
          }
        } catch (e) {
          debugPrint('❌ AI生成第${i + 1}条肯定语失败: $e，使用模板备用');
          // AI失败时使用模板
          final templateAffirmations = _generatePersonalizedAffirmations(goalTitle, 1, lang);
          if (templateAffirmations.isNotEmpty) {
            affirmations.add(templateAffirmations.first);
          }
        }
      }

      // 如果AI完全失败，使用模板生成
      if (affirmations.isEmpty) {
        debugPrint('🔄 AI完全失败，回退到模板生成');
        return _generatePersonalizedAffirmations(goalTitle, count, lang);
      }

      debugPrint('✅ 成功生成${affirmations.length}条肯定语（AI: ${affirmations.length}条）');
      return affirmations;
    } catch (e) {
      debugPrint('❌ 生成肯定语失败: $e');
      return _generateMultipleTemplateAffirmations(goalTitle, count, language ?? 'zh-CN');
    }
  }

  /// 生成个性化肯定语（基于目标内容）- 支持多语言
  static List<String> _generatePersonalizedAffirmations(String goalTitle, int count, String language) {
    final affirmations = <String>[];
    final keywords = _extractKeywords(goalTitle);
    final templates = _getAffirmationTemplates();

    // 根据关键词生成针对性肯定语
    for (final keyword in keywords) {
      if (templates.containsKey(keyword)) {
        final keywordTemplates = templates[keyword]!;
        affirmations.addAll(keywordTemplates);
      }
    }

    // 如果没有匹配的关键词，生成基于目标的个性化肯定语
    if (affirmations.isEmpty) {
      affirmations.addAll(_generateGoalSpecificAffirmations(goalTitle, language));
    }

    // 打乱顺序并返回指定数量
    affirmations.shuffle();
    return affirmations.take(count).toList();
  }

  /// 根据具体目标生成个性化肯定语 - 支持多语言
  static List<String> _generateGoalSpecificAffirmations(String goalTitle, String language) {
    final title = goalTitle.toLowerCase();
    final affirmations = <String>[];

    // 双眼皮相关 - 详细版本（约300字）
    if (title.contains('双眼皮') || title.contains('眼睛') ||
        title.contains('double eyelid') || title.contains('eyelid') || title.contains('eyes')) {
      if (language.startsWith('en')) {
        affirmations.addAll([
          'I possess the most exquisite double eyelids that nature has ever crafted. Every morning when I look in the mirror, I am amazed by the perfect symmetry and graceful curves of my eyelid creases. My eyes sparkle with an inner radiance that draws people in like magnets. Strangers stop me on the street to compliment my captivating gaze, and photographers beg to capture the ethereal beauty of my eyes. I feel an overwhelming sense of gratitude and confidence knowing that my natural beauty continues to blossom each day. My double eyelids frame my eyes like delicate works of art, creating shadows and highlights that enhance every expression I make. When I smile, my eyes crinkle in the most charming way, and when I\'m thoughtful, they hold depths of wisdom and mystery. I am living proof that dreams do come true, and I embrace this gift with joy and appreciation.',

          'My transformation into having perfect double eyelids has been nothing short of miraculous. I can feel the subtle changes happening in my eye area every single day - the skin becoming more supple, the natural fold deepening and defining itself with elegant precision. Friends and family constantly comment on how my eyes seem brighter, more open, and incredibly alluring. I catch myself admiring my reflection throughout the day, marveling at how my new eye shape has enhanced my entire facial harmony. The confidence I feel radiates from within, affecting every aspect of my life. I walk taller, speak with more assurance, and approach new opportunities with fearless enthusiasm. My eyes have become windows to my soul, reflecting the inner beauty and strength that was always there but now shines through more brilliantly than ever before. This positive change has opened doors I never imagined possible.',

          'The universe has blessed me with the most stunning double eyelids, and I am overwhelmed with gratitude for this incredible gift. Every time I apply makeup, I am amazed at how effortlessly beautiful my eyes look - the eyeshadow blends perfectly along my natural crease, creating depth and dimension that professional makeup artists dream of achieving. My eyelashes appear longer and more voluminous, framing my eyes like a natural curtain of silk. People often ask if I\'m wearing false lashes because my eyes look so dramatically beautiful. I feel like a goddess when I catch glimpses of myself in windows and mirrors throughout the day. My self-esteem has soared to new heights, and I approach each day with excitement and anticipation. The joy I feel is infectious, spreading to everyone around me and creating a positive ripple effect in all my relationships.',

          'I am living my dream of having perfectly sculpted double eyelids that enhance my natural beauty beyond my wildest imagination. The delicate fold above each eye creates the most elegant frame, making my eyes appear larger, brighter, and infinitely more expressive. When I laugh, my eyes dance with joy, and when I\'m serious, they convey depth and intelligence that commands respect. I notice how people are drawn to maintain eye contact with me longer, as if mesmerized by the magnetic quality of my gaze. My confidence has transformed every area of my life - I\'m more successful at work, more adventurous in my personal life, and more willing to put myself out there in social situations. I wake up each morning feeling grateful for this beautiful transformation and excited to see how my enhanced natural beauty will open new doors and create wonderful opportunities throughout my day.',

          'My double eyelids are a masterpiece of natural beauty, perfectly complementing my unique facial features and enhancing my overall attractiveness in ways I never thought possible. The smooth, graceful lines create an elegant sophistication that makes me feel like I belong on magazine covers and red carpets. I\'ve noticed how my eyes photograph beautifully from every angle, capturing light in the most flattering ways and creating stunning visual impact in every image. My friends constantly ask for my beauty secrets, amazed by the dramatic improvement in my appearance. I feel like I\'m glowing from the inside out, radiating confidence and joy that attracts positive people and experiences into my life. This transformation has taught me that when we believe in our dreams and maintain unwavering faith, the universe conspires to make our deepest desires manifest in the most beautiful and perfect ways.'
        ]);
      } else {
        affirmations.addAll([
          '我拥有这世界上最完美的双眼皮，每当我照镜子时，都会被自己眼部的精致美丽所震撼。我的双眼皮线条流畅自然，就像是大自然精心雕琢的艺术品，完美地勾勒出我眼部的轮廓。我的眼睛明亮有神，散发着迷人的光芒，无论走到哪里都会吸引众人的目光。朋友们总是赞美我的眼睛，说我的眼神中有一种特殊的魅力，让人过目难忘。我感到无比的自信和喜悦，因为我知道我的美丽正在一天天绽放。每当我微笑时，我的眼睛会弯成最美的弧度，传递出内心的快乐和满足。我的双眼皮让我的整个面部轮廓都变得更加立体和精致，我为拥有如此美丽的眼睛而感到深深的感恩。这份美丽不仅改变了我的外表，更重要的是提升了我的自信心，让我在生活的各个方面都更加积极主动，勇敢地追求自己的梦想。',

          '我的双眼皮正在发生着神奇的变化，每一天我都能感受到眼部肌肤的细微改善。我的眼皮变得更加紧致有弹性，自然的褶皱线条越来越清晰明显，形成了完美的双眼皮形状。周围的人都注意到了我眼部的变化，纷纷夸赞我的眼睛变得更加明亮动人。我在化妆时发现眼影更容易上色，眼线也更好描绘，整个眼妆效果都提升了好几个层次。我的睫毛看起来更加浓密卷翘，仿佛天生就拥有完美的眼部条件。这种变化让我感到前所未有的兴奋和期待，我知道我正在朝着理想的自己不断靠近。我的心情变得更加愉悦，对生活充满了热情和希望。每当我看到镜中的自己，都会情不自禁地微笑，为这份美丽的蜕变而感到无比幸福。我相信这只是一个开始，更多美好的变化还在等待着我。',

          '宇宙赐予了我最令人惊艳的双眼皮，我为这份珍贵的礼物感到无限的感激。我的眼睛现在拥有了完美的比例和形状，双眼皮的弧度恰到好处，既不会太深显得突兀，也不会太浅失去效果。当我眨眼时，可以清楚地看到优美的褶皱线条，这让我的眼神变得更加生动有趣。我发现自己在拍照时更加自信了，因为我知道我的眼睛在镜头前会呈现出最美的状态。朋友们都说我的气质发生了很大的变化，变得更加优雅迷人。我的内心充满了喜悦和满足感，这种正能量感染着身边的每一个人。我开始更加注重自己的形象管理，因为我知道我值得拥有最好的一切。这份美丽让我在社交场合更加从容自信，我敢于表达自己的想法，也更愿意与他人建立深入的连接。我深深地相信，这份外在美丽的改变正在为我的人生带来更多精彩的可能性。',

          '我正在经历着人生中最美好的蜕变，我的双眼皮已经达到了我梦寐以求的完美状态。每当我照镜子时，都会被自己眼部的精致美丽所深深震撼。我的双眼皮线条自然流畅，完美地平衡了我的面部比例，让我的整个人看起来更加和谐美丽。我注意到人们在与我交谈时，会不自觉地被我的眼神所吸引，这让我在人际交往中更加游刃有余。我的自信心得到了极大的提升，这种内在的改变比外在的美丽更加珍贵。我开始勇敢地追求自己的梦想，因为我知道我拥有足够的魅力和能力去实现任何目标。我的生活变得更加丰富多彩，我愿意尝试新的事物，结识新的朋友，探索新的可能性。这份美丽的改变让我明白，当我们真心相信并为之努力时，宇宙会以最美好的方式回应我们的愿望。我为自己的坚持和信念感到骄傲，也为即将到来的更多美好变化而充满期待。',

          '我的双眼皮是大自然赐予我的最珍贵的礼物，它们完美地诠释了什么叫做天生丽质。我的眼部轮廓现在拥有了理想的立体感和层次感，无论从哪个角度看都是那么的完美无瑕。我的眼睛在不同的光线下会呈现出不同的美感，有时深邃神秘，有时明亮清澈，总是能够准确地传达我内心的情感。我发现自己在镜头前更加自然放松了，因为我知道我的眼睛会为每一张照片增添独特的魅力。我的朋友们都羡慕我拥有如此美丽的眼睛，经常向我请教美容的秘诀。我感到无比的幸福和满足，因为我知道我正在成为最好的自己。这份美丽不仅仅停留在表面，它更深层地影响着我的心态和生活方式。我变得更加积极乐观，更加热爱生活，也更加珍惜身边的每一个美好时刻。我相信这份美丽会为我的人生带来无限的可能性和机遇，我已经准备好迎接所有即将到来的精彩和惊喜。'
        ]);
      }
    }

    // 美丽相关
    else if (title.contains('美') || title.contains('漂亮') || title.contains('好看') ||
             title.contains('颜值') || title.contains('变美') ||
             title.contains('beautiful') || title.contains('pretty') || title.contains('beauty')) {
      if (language.startsWith('en')) {
        affirmations.addAll([
          'My beauty radiates brilliantly, attracting admiring gazes from everyone, I confidently bloom with unique charm.',
          'I possess stunning beauty, my charm is irresistible, I deserve to be treated gently by the world.',
          'My beauty emanates from within, I radiate enchanting light, I am the most beautiful self.',
          'My attractiveness keeps increasing, my beauty is unforgettable, I confidently show my true self.',
          'I have natural beauty, my appearance increasingly matches my ideals, I am the most beautiful being.',
        ]);
      } else {
        affirmations.addAll([
          '我的美丽光芒四射，吸引着所有人的赞美目光，我自信地绽放独特魅力。',
          '我拥有令人惊艳的美貌，我的魅力无人能挡，我值得被世界温柔以待。',
          '我的美丽由内而外散发，我散发着迷人的光芒，我是最美的自己。',
          '我的颜值越来越高，我的美丽让人过目不忘，我自信地展现真实的自己。',
          '我拥有天生丽质的美貌，我的外表越来越符合我的理想，我是最美的存在。',
        ]);
      }
    }

    // 爱情相关
    else if (title.contains('男友') || title.contains('女友') || title.contains('恋人') ||
             title.contains('爱情') || title.contains('伴侣')) {
      affirmations.addAll([
        '我散发爱的磁场，完美伴侣被我深深吸引，我们拥有幸福美满关系。',
        '我值得被深深爱着，我的真命天子正向我走来，我们的爱情甜蜜美好。',
        '我是爱情的幸运儿，我吸引着忠诚专一的伴侣，我们彼此珍惜相爱。',
        '我的爱情运势极佳，我遇到了生命中的那个人，我们拥有童话般的爱情。',
        '我拥有吸引真爱的能力，我的理想伴侣正在寻找我，我们即将相遇。',
      ]);
    }

    // 财富相关
    else if (title.contains('钱') || title.contains('财') || title.contains('富') ||
             title.contains('收入') || title.contains('发财')) {
      affirmations.addAll([
        '我轻松吸引丰盛财富，金钱如流水般涌向我，我值得拥有富足生活。',
        '我是金钱磁铁，财富主动找到我，我的银行账户数字不断增长。',
        '我拥有创造财富的天赋，我的收入持续增加，我过着富足自由的生活。',
        '金钱爱我，我也爱金钱，我们是完美的伙伴，我轻松拥有财务自由。',
        '我散发着财富的能量，机会和金钱被我吸引，我享受丰盛的生活。',
      ]);
    }

    // 通用个性化模板
    else {
      if (language.startsWith('en')) {
        affirmations.addAll([
          'I deserve to have $goalTitle, my goal is perfectly manifesting, I gratefully accept this blessing.',
          'I believe in my power, my desire for $goalTitle is becoming reality, I am filled with gratitude and joy.',
          'I radiate positive energy, $goalTitle is manifesting in my life, I embrace this beauty.',
          'I am a master of manifestation, $goalTitle is easily achieved, I deserve the best of everything.',
          'My heart is full of power, $goalTitle is coming true, I am grateful for the universe\'s abundant gifts.',
        ]);
      } else {
        affirmations.addAll([
          '我值得拥有$goalTitle，我的目标正在完美实现，我感恩地接受这份美好。',
          '我相信自己的力量，关于$goalTitle的愿望正在成为现实，我充满感恩和喜悦。',
          '我散发着正能量，$goalTitle正在我的生活中显化，我拥抱这份美好。',
          '我是显化的大师，$goalTitle轻松实现，我值得拥有最好的一切。',
          '我的内心充满力量，$goalTitle正在成真，我感恩宇宙的丰盛恩赐。',
        ]);
      }
    }

    return affirmations;
  }

  /// 构建AI肯定语生成提示词
  static String _buildAffirmationPrompt(String goalTitle, String language, int variation) {
    // 直接返回目标标题，让Edge Function处理提示词构建
    return goalTitle;
  }

  /// 构建专业显化提示词
  static String _buildProfessionalPrompt(String goalTitle, [int variation = 1]) {
    final variationPrompts = [
      '请创作一句充满力量的显化肯定语',
      '请生成一句积极正面的肯定语句',
      '请创造一句能量满满的显化宣言',
      '请编写一句潜意识接受的肯定语',
      '请构思一句振动频率很高的肯定语',
    ];

    final selectedPrompt = variationPrompts[(variation - 1) % variationPrompts.length];

    // 分析目标类型，提供更精准的指导
    String specificGuidance = _getSpecificGuidance(goalTitle);

    return '''你是世界顶级的显化大师，专精吸引力法则和潜意识编程。$selectedPrompt：

用户的具体目标：「$goalTitle」

$specificGuidance

核心要求：
1. 【第一人称现在时】必须以"我"开头，用现在时态表达目标已经完全实现
2. 【针对性强】必须紧密围绕用户的具体目标「$goalTitle」来创作
3. 【情感共振】包含强烈的正面情感，如喜悦、感恩、自信、满足
4. 【潜意识接受】语言要让潜意识完全接受，避免任何怀疑或阻抗
5. 【具体生动】让肯定语生动具体，潜意识能清晰"看见"结果
6. 【长度适中】25-40字，便于记忆和重复默念

请直接返回一句完整的显化肯定语，不要引号，不要解释，不要多余文字。''';
  }

  /// 根据目标类型提供具体指导
  static String _getSpecificGuidance(String goalTitle) {
    final title = goalTitle.toLowerCase();

    // 美丽/外貌相关
    if (title.contains('双眼皮') || title.contains('眼睛')) {
      return '''针对眼部美化目标，请创作体现以下要素的肯定语：
- 眼睛的美丽和魅力
- 双眼皮的自然完美
- 眼神的吸引力和神采
- 整体面容的提升''';
    }

    if (title.contains('美') || title.contains('漂亮') || title.contains('好看') ||
        title.contains('颜值') || title.contains('外貌') || title.contains('皮肤') ||
        title.contains('脸') || title.contains('变美')) {
      return '''针对美丽外貌目标，请创作体现以下要素的肯定语：
- 由内而外的美丽光芒
- 令人惊艳的魅力
- 自信的美丽展现
- 他人的赞美和认可''';
    }

    // 爱情相关
    if (title.contains('男友') || title.contains('女友') || title.contains('恋人') ||
        title.contains('爱情') || title.contains('伴侣')) {
      return '''针对爱情关系目标，请创作体现以下要素的肯定语：
- 爱的磁场和吸引力
- 完美伴侣的出现
- 甜蜜美满的关系
- 彼此的深爱和珍惜''';
    }

    // 财富相关
    if (title.contains('钱') || title.contains('财') || title.contains('富') ||
        title.contains('收入') || title.contains('发财')) {
      return '''针对财富丰盛目标，请创作体现以下要素的肯定语：
- 财富的轻松流入
- 金钱的丰盛状态
- 财务自由的实现
- 值得拥有富足生活''';
    }

    // 事业相关
    if (title.contains('工作') || title.contains('事业') || title.contains('成功') ||
        title.contains('升职') || title.contains('创业')) {
      return '''针对事业成功目标，请创作体现以下要素的肯定语：
- 才华的被认可
- 成功机会的到来
- 事业的蒸蒸日上
- 专业领域的闪耀''';
    }

    // 健康/身材相关
    if (title.contains('健康') || title.contains('减肥') || title.contains('瘦') ||
        title.contains('身材') || title.contains('运动')) {
      return '''针对健康身材目标，请创作体现以下要素的肯定语：
- 身体的健康活力
- 理想身材的拥有
- 健康生活的享受
- 身心的和谐统一''';
    }

    // 通用指导
    return '''请根据用户的具体目标创作个性化的肯定语，确保：
- 紧密贴合目标内容
- 体现目标实现后的美好状态
- 包含相关的正面情感
- 让用户感到振奋和有力量''';
  }

  /// 获取多语言系统提示词
  static String _getSystemPrompt(String language) {
    switch (language) {
      case 'en-US':
      case 'en':
        return '''You are a world-class manifestation master and subconscious programming expert, specializing in creating manifestation affirmations.

Your task:
1. Carefully read the user's specific goal
2. Create a professional manifestation affirmation for that goal
3. Must use first person present tense, expressing the goal as already achieved
4. Must closely align with the user's specific goal content
5. Return only the affirmation itself, no explanations or extra text

Remember: Each affirmation must be custom-tailored to the user's specific goal!''';

      case 'es-ES':
        return '''Eres un maestro de manifestación de clase mundial y experto en programación subconsciente, especializado en crear afirmaciones de manifestación.

Tu tarea:
1. Lee cuidadosamente el objetivo específico del usuario
2. Crea una afirmación de manifestación profesional para ese objetivo
3. Debe usar primera persona tiempo presente, expresando el objetivo como ya logrado
4. Debe alinearse estrechamente con el contenido específico del objetivo del usuario
5. Devuelve solo la afirmación en sí, sin explicaciones o texto adicional

¡Recuerda: Cada afirmación debe estar hecha a medida para el objetivo específico del usuario!''';

      case 'ja-JP':
        return '''あなたは世界クラスの顕現マスターであり、潜在意識プログラミングの専門家で、顕現アファメーションの作成を専門としています。

あなたのタスク：
1. ユーザーの具体的な目標を注意深く読む
2. その目標に対する専門的な顕現アファメーションを作成する
3. 一人称現在形を使用し、目標が既に達成されたことを表現する
4. ユーザーの具体的な目標内容と密接に一致させる
5. アファメーション自体のみを返し、説明や余分なテキストは不要

覚えておいて：各アファメーションはユーザーの具体的な目標に合わせてカスタマイズされなければなりません！''';

      case 'ko-KR':
        return '''당신은 세계적인 현실화 마스터이자 잠재의식 프로그래밍 전문가로, 현실화 확언 작성을 전문으로 합니다.

당신의 임무:
1. 사용자의 구체적인 목표를 주의 깊게 읽기
2. 그 목표에 대한 전문적인 현실화 확언 작성
3. 1인칭 현재형을 사용하여 목표가 이미 달성되었음을 표현
4. 사용자의 구체적인 목표 내용과 밀접하게 일치시키기
5. 확언 자체만 반환하고, 설명이나 추가 텍스트는 없이

기억하세요: 각 확언은 사용자의 구체적인 목표에 맞춤 제작되어야 합니다!''';

      default: // zh-CN, zh-TW
        return '''你是世界顶级的显化大师和潜意识编程专家，专门创作显化肯定语。

你的任务：
1. 仔细阅读用户的具体目标
2. 创作一句针对该目标的专业显化肯定语
3. 必须用第一人称现在时，表达目标已经实现
4. 必须紧密贴合用户的具体目标内容
5. 只返回肯定语本身，不要任何解释或多余文字

记住：每句肯定语都必须针对用户的具体目标量身定制！''';
    }
  }

  /// 调用Supabase Edge Function
  static Future<String?> _callSupabaseFunction(String prompt, [String? language]) async {
    try {
      final supabase = Supabase.instance.client;

      debugPrint('🔮 调用AI生成肯定语，提示词长度: ${prompt.length}');

      final response = await supabase.functions.invoke(
        _functionName,
        body: {
          'requestType': 'affirmation_generation',
          'goalTitle': prompt,
          'userLanguage': language ?? 'zh-CN',
          'affirmationVariation': DateTime.now().millisecondsSinceEpoch % 5 + 1, // 确保每次生成不同的肯定语
        },
      );

      if (response.data != null) {
        final data = response.data as Map<String, dynamic>;
        if (data['success'] == true) {
          // 尝试不同的响应字段
          final content = data['content'] ?? data['response'] ?? data['reading'];
          if (content != null) {
            debugPrint('✅ AI返回内容: $content');
            return content.toString();
          }
        }
        debugPrint('❌ AI返回失败: ${data['error'] ?? 'Unknown error'}');
      }

      return null;
    } catch (e) {
      debugPrint('❌ Supabase函数调用失败: $e');
      return null;
    }
  }

  /// 清理肯定语文本
  static String _cleanAffirmationText(String text) {
    // 移除引号
    String cleaned = text.replaceAll('"', '').replaceAll('"', '').replaceAll('"', '');
    
    // 移除多余的空格和换行
    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');
    
    // 确保以句号结尾
    if (!cleaned.endsWith('。') && !cleaned.endsWith('.')) {
      cleaned += '。';
    }
    
    return cleaned;
  }

  /// 生成多条模板肯定语（备用方案）
  static List<String> _generateMultipleTemplateAffirmations(String goalTitle, int count, [String? language]) {
    final lang = language ?? 'zh-CN';
    final templates = _getAffirmationTemplates(lang);
    final keywords = _extractKeywords(goalTitle);
    final affirmations = <String>[];

    // 根据关键词获取相关模板
    for (final keyword in keywords) {
      if (templates.containsKey(keyword)) {
        final templateList = templates[keyword]!;
        affirmations.addAll(templateList);
      }
    }

    // 如果没有匹配的关键词，使用通用模板
    if (affirmations.isEmpty) {
      if (lang.startsWith('en')) {
        affirmations.addAll([
          'I deserve to have $goalTitle, my goal is manifesting perfectly, and I gratefully accept this blessing.',
          'I believe in my power, my desire for $goalTitle is becoming reality, and I am filled with gratitude and joy.',
          'I radiate positive energy, $goalTitle is manifesting in my life, and I embrace this goodness.',
          'I am a master of manifestation, $goalTitle comes to me easily, and I deserve the very best.',
          'My heart is full of power, $goalTitle is coming true, and I am grateful for the universe\'s abundant gifts.',
        ]);
      } else {
        affirmations.addAll([
          '我值得拥有$goalTitle，我的目标正在完美实现，我感恩地接受这份美好。',
          '我相信自己的力量，关于$goalTitle的愿望正在成为现实，我充满感恩和喜悦。',
          '我散发着正能量，$goalTitle正在我的生活中显化，我拥抱这份美好。',
          '我是显化的大师，$goalTitle轻松实现，我值得拥有最好的一切。',
          '我的内心充满力量，$goalTitle正在成真，我感恩宇宙的丰盛恩赐。',
        ]);
      }
    }

    // 打乱顺序并返回指定数量
    affirmations.shuffle();
    return affirmations.take(count).toList();
  }

  /// 生成单条模板肯定语（备用方案）
  static String _generateTemplateAffirmation(String goalTitle) {
    final multipleAffirmations = _generateMultipleTemplateAffirmations(goalTitle, 1);
    return multipleAffirmations.first;
  }

  /// 提取关键词
  static List<String> _extractKeywords(String goalTitle) {
    final keywords = <String>[];
    final title = goalTitle.toLowerCase();

    // 美丽/外貌相关 - 扩展关键词
    if (title.contains('美') || title.contains('漂亮') || title.contains('好看') ||
        title.contains('颜值') || title.contains('外貌') || title.contains('双眼皮') ||
        title.contains('眼睛') || title.contains('皮肤') || title.contains('脸') ||
        title.contains('变美') || title.contains('整容') || title.contains('化妆') ||
        title.contains('护肤') || title.contains('美白') || title.contains('祛痘') ||
        title.contains('瘦脸') || title.contains('鼻子') || title.contains('嘴唇') ||
        title.contains('身材') || title.contains('气质') || title.contains('魅力')) {
      keywords.add('beauty');
    }

    // 财富相关
    if (title.contains('钱') || title.contains('财') || title.contains('富') ||
        title.contains('收入') || title.contains('赚') || title.contains('发财') ||
        title.contains('中奖') || title.contains('投资') || title.contains('理财')) {
      keywords.add('wealth');
    }

    // 爱情相关
    if (title.contains('男友') || title.contains('女友') || title.contains('恋人') ||
        title.contains('爱情') || title.contains('结婚') || title.contains('伴侣') ||
        title.contains('脱单') || title.contains('恋爱') || title.contains('表白') ||
        title.contains('追求') || title.contains('喜欢') || title.contains('暗恋')) {
      keywords.add('love');
    }

    // 事业相关
    if (title.contains('工作') || title.contains('事业') || title.contains('职业') ||
        title.contains('升职') || title.contains('成功') || title.contains('创业') ||
        title.contains('面试') || title.contains('跳槽') || title.contains('加薪')) {
      keywords.add('career');
    }

    // 健康/身材相关
    if (title.contains('健康') || title.contains('身体') || title.contains('减肥') ||
        title.contains('瘦') || title.contains('运动') || title.contains('健身') ||
        title.contains('体重') || title.contains('塑形') || title.contains('马甲线')) {
      keywords.add('health');
    }

    return keywords;
  }

  /// 获取肯定语模板库
  static Map<String, List<String>> _getAffirmationTemplates([String? language]) {
    final lang = language ?? 'zh-CN';
    return {
      'beauty': [
        '我的美丽光芒四射，吸引着所有人的赞美目光，我自信地绽放独特魅力。',
        '我拥有令人惊艳的美貌，我的魅力无人能挡，我值得被世界温柔以待。',
        '我的美丽由内而外散发，我散发着迷人的光芒，我是最美的自己。',
        '我的颜值越来越高，我的美丽让人过目不忘，我自信地展现真实的自己。',
        '我拥有完美的双眼皮，我的眼睛明亮动人，我的美丽让所有人为之倾倒。',
        '我的眼睛越来越美丽，我拥有理想的眼型，我的魅力无法抵挡。',
        '我的五官越来越精致，我的美貌日益完美，我散发着令人着迷的光芒。',
        '我拥有天生丽质的美貌，我的外表越来越符合我的理想，我是最美的存在。',
        '我的皮肤光滑细腻，我的容颜绝美动人，我值得拥有完美的外貌。',
        '我的美丽是独一无二的，我的外表正在变得越来越完美，我爱我自己。',
        '我拥有迷人的大眼睛，我的双眼皮自然美丽，我的眼神充满魅力。',
        '我的容貌越来越精致，我拥有完美的五官比例，我是人群中最亮眼的存在。',
        '我的美丽从内心散发，我的外表完美无瑕，我自信地展现我的美丽。',
        '我拥有理想中的容貌，我的美貌让人过目不忘，我是最美丽的自己。',
        '我的眼睛大而有神，我的双眼皮完美自然，我的美丽让世界为我停留。',
      ],
      'wealth': [
        '我轻松吸引丰盛财富，金钱如流水般涌向我，我值得拥有富足生活。',
        '我是金钱磁铁，财富主动找到我，我的银行账户数字不断增长。',
        '我拥有创造财富的天赋，我的收入持续增加，我过着富足自由的生活。',
        '金钱爱我，我也爱金钱，我们是完美的伙伴，我轻松拥有财务自由。',
      ],
      'love': [
        '我散发爱的磁场，完美伴侣被我深深吸引，我们拥有幸福美满关系。',
        '我值得被深深爱着，我的真命天子正向我走来，我们的爱情甜蜜美好。',
        '我是爱情的幸运儿，我吸引着忠诚专一的伴侣，我们彼此珍惜相爱。',
        '我的爱情运势极佳，我遇到了生命中的那个人，我们拥有童话般的爱情。',
      ],
      'career': [
        '我的才华被世界看见，成功机会主动找到我，我在事业上闪闪发光。',
        '我拥有卓越的工作能力，我的事业蒸蒸日上，我实现了职业梦想。',
        '我是职场的佼佼者，我的价值被充分认可，我享受事业带来的成就感。',
        '我的事业道路一帆风顺，我轻松获得理想工作，我在专业领域发光发热。',
      ],
      'health': [
        '我拥有完美健康的身体，我的每个细胞都充满活力，我散发着健康光芒。',
        '我的身材越来越好，我拥有理想的体重，我自信地展现健康美丽的自己。',
        '我的身体是我最好的朋友，我们和谐共处，我享受健康带来的快乐。',
        '我轻松保持理想身材，我的新陈代谢完美运作，我拥有令人羡慕的好身材。',
      ],
    };
  }
}
