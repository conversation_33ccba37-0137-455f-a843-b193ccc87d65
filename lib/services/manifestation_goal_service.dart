import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/manifestation_goal.dart';
import '../services/manifestation_affirmation_service.dart';
import '../services/supabase_manifestation_service.dart';
import '../utils/language_manager.dart';

/// 显化目标管理服务 - 支持本地缓存和Supabase同步
class ManifestationGoalService extends ChangeNotifier {
  static const String _storageKey = 'manifestation_goals';
  static const String _migrationKey = 'goals_migrated_to_supabase';

  List<ManifestationGoal> _goals = [];
  bool _isLoading = false;
  String? _error;
  bool _isOnline = true;
  bool _isSyncing = false;

  // Supabase service for cloud sync
  final SupabaseManifestationService _supabaseService = SupabaseManifestationService();
  StreamSubscription<List<ManifestationGoal>>? _realtimeSubscription;

  List<ManifestationGoal> get goals => List.unmodifiable(_goals);
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isOnline => _isOnline;
  bool get isSyncing => _isSyncing;

  /// 初始化服务
  Future<void> initialize() async {
    _setLoading(true);

    try {
      // First load from local storage for immediate UI
      await _loadGoalsFromStorage();

      // Check if user is authenticated for Supabase sync
      final supabase = Supabase.instance.client;
      if (supabase.auth.currentUser != null) {
        await _initializeSupabaseSync();
      } else {
        debugPrint('📱 User not authenticated, using local storage only');
      }
    } catch (e) {
      debugPrint('❌ Failed to initialize service: $e');
      _setError('初始化失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 初始化Supabase同步
  Future<void> _initializeSupabaseSync() async {
    try {
      _setSyncing(true);
      debugPrint('🔄 开始初始化Supabase同步...');

      // Initialize real-time sync
      await _supabaseService.initializeRealtimeSync();
      debugPrint('✅ 实时同步服务已初始化');

      // Subscribe to real-time updates
      _realtimeSubscription = _supabaseService.goalsStream.listen(
        (cloudGoals) {
          debugPrint('📡 收到实时更新: ${cloudGoals.length} 个目标');
          _handleRealtimeUpdate(cloudGoals);
        },
        onError: (error) {
          debugPrint('❌ Real-time sync error: $error');
          _setError('同步错误: $error');
        },
      );
      debugPrint('✅ 实时更新订阅已建立');

      // Check if migration is needed
      await _checkAndMigrateLocalData();

      // Perform initial sync
      await _performInitialSync();

      _isOnline = true;
      debugPrint('✅ Supabase sync initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize Supabase sync: $e');
      _isOnline = false;
      // Continue with local storage only
    } finally {
      _setSyncing(false);
    }
  }

  /// 从本地存储加载目标
  Future<void> _loadGoalsFromStorage() async {
    try {
      _setLoading(true);
      final prefs = await SharedPreferences.getInstance();
      final goalsJson = prefs.getString(_storageKey);
      
      if (goalsJson != null) {
        final List<dynamic> goalsList = json.decode(goalsJson);
        _goals = goalsList.map((json) => ManifestationGoal.fromJson(json)).toList();
        
        // 按创建时间排序（最新的在前）
        _goals.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }
      
      _setLoading(false);
    } catch (e) {
      _setError('加载显化目标失败: $e');
      _setLoading(false);
    }
  }

  /// 保存目标到本地存储
  Future<void> _saveGoalsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final goalsJson = json.encode(_goals.map((goal) => goal.toJson()).toList());
      await prefs.setString(_storageKey, goalsJson);
    } catch (e) {
      debugPrint('保存显化目标失败: $e');
    }
  }

  /// 添加新的显化目标
  Future<void> addGoal(String title, {String? description}) async {
    try {
      _setLoading(true);
      debugPrint('🔄 开始添加目标: $title');

      if (_isOnline) {
        // Try to create in Supabase first
        try {
          final cloudGoal = await _supabaseService.createGoal(
            title: title.trim(),
            description: description?.trim(),
          );

          debugPrint('✅ Goal created in cloud: ${cloudGoal.title}');

          // 立即添加到本地列表，避免等待实时更新造成的延迟
          if (!_goals.any((goal) => goal.id == cloudGoal.id)) {
            debugPrint('✅ 添加新目标到本地列表');
            _goals.insert(0, cloudGoal);
            await _saveGoalsToStorage();
            notifyListeners();
          } else {
            debugPrint('⚠️ 目标已存在，跳过添加');
          }
        } catch (e) {
          debugPrint('❌ Failed to create goal in cloud, creating locally: $e');
          await _createGoalLocally(title, description);
        }
      } else {
        // Create locally when offline
        debugPrint('📱 离线模式，本地创建目标');
        await _createGoalLocally(title, description);
      }

      _setLoading(false);
    } catch (e) {
      debugPrint('❌ 添加目标失败: $e');
      _setError('添加显化目标失败: $e');
      _setLoading(false);
    }
  }

  /// 在本地创建目标
  Future<void> _createGoalLocally(String title, String? description) async {
    final newGoal = ManifestationGoal(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title.trim(),
      description: description?.trim(),
      createdAt: DateTime.now(),
      syncStatus: _isOnline ? SyncStatus.pending : SyncStatus.pending,
    );

    _goals.insert(0, newGoal); // 插入到列表开头
    await _saveGoalsToStorage();
    notifyListeners();

    // Try to sync later if online
    if (_isOnline) {
      _syncPendingGoals();
    }
  }

  /// 更新目标状态
  Future<void> updateGoalStatus(String goalId, ManifestationStatus status) async {
    try {
      final goalIndex = _goals.indexWhere((goal) => goal.id == goalId);
      if (goalIndex == -1) return;

      debugPrint('🔄 更新目标状态: $goalId -> ${status.name}');

      if (_isOnline) {
        // Try to update in Supabase first
        try {
          final updatedGoal = await _supabaseService.updateGoal(
            goalId: goalId,
            status: status,
          );

          debugPrint('✅ 云端状态更新成功: ${updatedGoal.title} -> ${updatedGoal.status.name}');

          // 立即更新本地状态，不等待实时订阅
          _goals[goalIndex] = updatedGoal;
          await _saveGoalsToStorage();
          notifyListeners();
          debugPrint('✅ 本地UI已立即更新');

        } catch (e) {
          debugPrint('❌ Failed to update goal in cloud, updating locally: $e');
          await _updateGoalLocally(goalIndex, status: status);
        }
      } else {
        // Update locally when offline
        debugPrint('📱 离线模式，本地更新状态');
        await _updateGoalLocally(goalIndex, status: status);
      }
    } catch (e) {
      debugPrint('❌ 更新目标状态失败: $e');
      _setError('更新目标状态失败: $e');
    }
  }

  /// 在本地更新目标
  Future<void> _updateGoalLocally(int goalIndex, {
    ManifestationStatus? status,
    String? affirmation,
    bool? isAffirmationGenerated,
  }) async {
    _goals[goalIndex] = _goals[goalIndex].copyWith(
      status: status,
      affirmation: affirmation,
      isAffirmationGenerated: isAffirmationGenerated,
      updatedAt: DateTime.now(),
      syncStatus: _isOnline ? SyncStatus.pending : SyncStatus.pending,
    );

    await _saveGoalsToStorage();
    notifyListeners();

    // Try to sync later if online
    if (_isOnline) {
      _syncPendingGoals();
    }
  }

  /// 为目标生成多条肯定语 - 不使用全局loading
  Future<void> generateMultipleAffirmations(String goalId, {int count = 10}) async {
    try {
      debugPrint('🔮 开始生成多条肯定语: goalId=$goalId, count=$count');

      final goalIndex = _goals.indexWhere((goal) => goal.id == goalId);
      if (goalIndex == -1) {
        debugPrint('❌ 目标不存在: $goalId');
        _setError('目标不存在');
        return;
      }

      final goal = _goals[goalIndex];
      debugPrint('✅ 找到目标: ${goal.title}');

      // 立即设置生成中状态，触发UI更新显示加载状态
      _goals[goalIndex] = goal.copyWith(
        syncStatus: SyncStatus.pending, // 使用pending状态表示正在生成
      );
      notifyListeners();
      debugPrint('🔄 已设置目标为生成中状态');

      // 使用专业显化肯定语服务生成多条肯定语
      debugPrint('🤖 调用AI服务生成肯定语...');
      final languageManager = LanguageManager();
      final currentLanguage = languageManager.currentLanguage;
      debugPrint('🌐 当前语言: $currentLanguage');
      final affirmations = await ManifestationAffirmationService.generateMultipleAffirmations(
        goal.title,
        count,
        currentLanguage
      );
      debugPrint('✅ AI服务返回${affirmations.length}条肯定语');

      // 将多条肯定语合并为一个字符串，用特殊分隔符分隔
      final combinedAffirmations = affirmations.join('|||');

      if (_isOnline) {
        // Try to update in Supabase first
        try {
          final updatedGoal = await _supabaseService.updateGoal(
            goalId: goalId,
            affirmation: combinedAffirmations,
            isAffirmationGenerated: true,
          );

          debugPrint('✅ 云端多条肯定语更新成功: ${updatedGoal.title}');

          // 立即更新本地状态，不等待实时订阅
          _goals[goalIndex] = updatedGoal;
          await _saveGoalsToStorage();
          notifyListeners();
          debugPrint('✅ 多条肯定语UI已立即更新');

        } catch (e) {
          debugPrint('❌ Failed to update multiple affirmations in cloud, updating locally: $e');
          await _updateGoalLocally(goalIndex,
            affirmation: combinedAffirmations,
            isAffirmationGenerated: true);
        }
      } else {
        // Update locally when offline
        debugPrint('📱 离线模式，本地更新多条肯定语');
        await _updateGoalLocally(goalIndex,
          affirmation: combinedAffirmations,
          isAffirmationGenerated: true);
      }
    } catch (e) {
      debugPrint('❌ 生成多条肯定语失败: $e');
      debugPrint('❌ 错误堆栈: ${StackTrace.current}');
      _setError('生成多条肯定语失败: $e');

      // 恢复原始状态
      final goalIndex = _goals.indexWhere((goal) => goal.id == goalId);
      if (goalIndex != -1) {
        debugPrint('🔄 恢复目标状态为synced');
        _goals[goalIndex] = _goals[goalIndex].copyWith(
          syncStatus: SyncStatus.synced,
        );
        notifyListeners();
      }
    }
  }

  /// 为目标生成单条肯定语（兼容旧版本）
  Future<void> generateAffirmation(String goalId) async {
    await generateMultipleAffirmations(goalId, count: 1);
  }



  /// 删除目标
  Future<void> deleteGoal(String goalId) async {
    try {
      _goals.removeWhere((goal) => goal.id == goalId);
      await _saveGoalsToStorage();
      notifyListeners();
    } catch (e) {
      _setError('删除目标失败: $e');
    }
  }

  /// 搜索目标
  List<ManifestationGoal> searchGoals(String query) {
    if (query.isEmpty) return _goals;
    
    final lowerQuery = query.toLowerCase();
    return _goals.where((goal) {
      return goal.title.toLowerCase().contains(lowerQuery) ||
             (goal.description?.toLowerCase().contains(lowerQuery) ?? false) ||
             (goal.affirmation?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// 按状态筛选目标
  List<ManifestationGoal> filterByStatus(ManifestationStatus? status) {
    if (status == null) return _goals;
    return _goals.where((goal) => goal.status == status).toList();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 设置同步状态
  void _setSyncing(bool syncing) {
    _isSyncing = syncing;
    notifyListeners();
  }

  /// 处理实时更新
  void _handleRealtimeUpdate(List<ManifestationGoal> cloudGoals) {
    debugPrint('📡 处理实时更新: ${cloudGoals.length} 个目标');

    // 检查是否有真正的变化，避免不必要的更新
    bool hasChanges = false;

    // 首先检查数量变化
    if (_goals.length != cloudGoals.length) {
      hasChanges = true;
      debugPrint('📡 目标数量变化: ${_goals.length} -> ${cloudGoals.length}');
    } else {
      // 检查每个目标的内容是否有变化
      for (final cloudGoal in cloudGoals) {
        final localGoal = _goals.firstWhere(
          (g) => g.id == cloudGoal.id,
          orElse: () => ManifestationGoal(id: '', title: '', createdAt: DateTime.now())
        );

        if (localGoal.id.isEmpty ||
            localGoal.title != cloudGoal.title ||
            localGoal.description != cloudGoal.description ||
            localGoal.status != cloudGoal.status ||
            localGoal.affirmation != cloudGoal.affirmation ||
            localGoal.isAffirmationGenerated != cloudGoal.isAffirmationGenerated ||
            localGoal.updatedAt != cloudGoal.updatedAt) {
          hasChanges = true;
          debugPrint('📡 检测到目标变化: ${cloudGoal.title}');
          break;
        }
      }
    }

    if (hasChanges) {
      debugPrint('📡 应用数据变化，更新本地缓存');

      // 使用智能合并策略，避免覆盖本地正在编辑的数据
      final mergedGoals = <ManifestationGoal>[];

      // 添加云端的所有目标
      for (final cloudGoal in cloudGoals) {
        final existingLocal = _goals.firstWhere(
          (g) => g.id == cloudGoal.id,
          orElse: () => ManifestationGoal(id: '', title: '', createdAt: DateTime.now())
        );

        // 如果本地已存在且没有待同步的更改，使用云端版本
        if (existingLocal.id.isEmpty || existingLocal.syncStatus == SyncStatus.synced) {
          mergedGoals.add(cloudGoal);
        } else {
          // 保留本地版本（有待同步的更改）
          mergedGoals.add(existingLocal);
        }
      }

      // 添加本地独有的目标（待同步的）
      for (final localGoal in _goals) {
        if (localGoal.syncStatus == SyncStatus.pending &&
            !mergedGoals.any((g) => g.id == localGoal.id)) {
          mergedGoals.add(localGoal);
        }
      }

      // 按创建时间排序（最新的在前）
      mergedGoals.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      _goals = mergedGoals;
      _saveGoalsToStorage();
      notifyListeners();
      debugPrint('✅ UI已更新，当前目标数量: ${_goals.length}');

      // 打印每个目标的当前状态用于调试
      for (final goal in _goals) {
        debugPrint('📊 目标状态: ${goal.title} - ${goal.status.name} - ${goal.syncStatus.name}');
      }
    } else {
      debugPrint('📡 数据无变化，跳过更新');
    }
  }

  /// 检查并迁移本地数据到Supabase
  Future<void> _checkAndMigrateLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isMigrated = prefs.getBool(_migrationKey) ?? false;

      if (!isMigrated && _goals.isNotEmpty) {
        debugPrint('🔄 Migrating ${_goals.length} local goals to Supabase');

        // Upload local goals to Supabase
        await _supabaseService.batchUploadGoals(_goals);

        // Mark as migrated
        await prefs.setBool(_migrationKey, true);

        debugPrint('✅ Migration completed');
      }
    } catch (e) {
      debugPrint('❌ Migration failed: $e');
      // Don't throw - continue with local storage
    }
  }

  /// 执行初始同步
  Future<void> _performInitialSync() async {
    try {
      final cloudGoals = await _supabaseService.fetchGoals();

      if (cloudGoals.isNotEmpty) {
        _goals = cloudGoals;
        await _saveGoalsToStorage(); // Update local cache
        notifyListeners();
        debugPrint('✅ Initial sync completed: ${cloudGoals.length} goals');
      }
    } catch (e) {
      debugPrint('❌ Initial sync failed: $e');
      // Continue with local data
    }
  }

  /// 同步待同步的目标
  Future<void> _syncPendingGoals() async {
    if (!_isOnline) return;

    try {
      final pendingGoals = _goals.where((goal) =>
        goal.syncStatus == SyncStatus.pending).toList();

      if (pendingGoals.isEmpty) return;

      debugPrint('🔄 Syncing ${pendingGoals.length} pending goals');

      for (final goal in pendingGoals) {
        try {
          if (goal.localId != null) {
            // This is a local goal that needs to be created in cloud
            await _supabaseService.createGoal(
              title: goal.title,
              description: goal.description,
              localId: goal.id,
            );
          } else {
            // This is an existing goal that needs to be updated
            await _supabaseService.updateGoal(
              goalId: goal.id,
              title: goal.title,
              description: goal.description,
              status: goal.status,
              affirmation: goal.affirmation,
              isAffirmationGenerated: goal.isAffirmationGenerated,
            );
          }
        } catch (e) {
          debugPrint('❌ Failed to sync goal ${goal.id}: $e');
          // Mark as conflict for manual resolution
          final goalIndex = _goals.indexWhere((g) => g.id == goal.id);
          if (goalIndex != -1) {
            _goals[goalIndex] = goal.copyWith(syncStatus: SyncStatus.conflict);
          }
        }
      }

      await _saveGoalsToStorage();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Failed to sync pending goals: $e');
    }
  }

  /// 强制同步所有数据
  Future<void> forceSyncAll() async {
    if (!_isOnline) {
      _setError('无网络连接，无法同步');
      return;
    }

    try {
      _setSyncing(true);
      debugPrint('🔄 开始强制同步...');
      await _performInitialSync();
      await _syncPendingGoals();
      debugPrint('✅ Force sync completed');
    } catch (e) {
      debugPrint('❌ 强制同步失败: $e');
      _setError('同步失败: $e');
    } finally {
      _setSyncing(false);
    }
  }

  /// 强制刷新数据（用于调试）
  Future<void> forceRefresh() async {
    try {
      debugPrint('🔄 强制刷新数据...');
      _setLoading(true);

      if (_isOnline) {
        final cloudGoals = await _supabaseService.fetchGoals();
        debugPrint('📡 从云端获取到 ${cloudGoals.length} 个目标');
        _goals = cloudGoals;
        await _saveGoalsToStorage();
        notifyListeners();
        debugPrint('✅ 数据刷新完成');
      } else {
        debugPrint('📱 离线状态，从本地存储刷新');
        await _loadGoalsFromStorage();
      }
    } catch (e) {
      debugPrint('❌ 刷新失败: $e');
      _setError('刷新失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 清理资源
  @override
  void dispose() {
    _realtimeSubscription?.cancel();
    _supabaseService.dispose();
    super.dispose();
  }
}
