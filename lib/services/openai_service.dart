import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/services/langfuse_service.dart';

class OpenAIService {
  static final _supabase = Supabase.instance.client;
  static const String _baseUrl = 'https://api.openai.com/v1';
  
  /// 通过Supabase Edge Function调用OpenAI API
  static Future<Map<String, dynamic>> generateResponse({
    required String prompt,
    required String traceId,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
    String model = 'gpt-4o-mini', // 使用更便宜的模型
  }) async {
    final startTime = DateTime.now();
    
    try {
      // 1. 构建请求体，发送到Supabase Edge Function
      final requestBody = {
        'prompt': prompt,
        'model': model,
        'temperature': temperature,
        'max_tokens': maxTokens,
        'stream': false,
        'trace_id': traceId,
        'metadata': metadata ?? {},
        'request_type': 'openai_completion',
        'provider': 'openai', // 标识使用OpenAI
      };

      print('🤖 调用OpenAI服务: $model');
      print('📋 Prompt长度: ${prompt.length}字符');

      // 2. 调用Supabase Edge Function (临时使用现有的deepseek函数)
      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading', // 临时使用现有的Edge Function
        body: requestBody,
      );

      final duration = DateTime.now().difference(startTime);
      print('⏱️ OpenAI调用耗时: ${duration.inMilliseconds}ms');

      if (response.data != null && response.data['success'] == true) {
        final content = response.data['response'] ?? response.data['content'];
        final usage = response.data['usage'] ?? {};
        
        // 记录成功调用到Langfuse
        await LangfuseService.createGeneration(
          traceId: traceId,
          name: 'openai_completion',
          input: {'prompt': prompt, 'model_params': requestBody},
          output: {'response': content},
          model: model,
          metadata: {
            ...metadata ?? {},
            'duration_ms': duration.inMilliseconds,
            'temperature': temperature,
            'max_tokens': maxTokens,
            'api_provider': 'openai',
            'usage': usage,
          },
        );

        return {
          'success': true,
          'content': content,
          'response': content, // 兼容现有代码
          'usage': usage,
          'duration_ms': duration.inMilliseconds,
        };
      } else {
        // Edge Function调用失败，记录错误
        final errorMsg = 'OpenAI Edge Function错误: ${response.data}';
        await LangfuseService.createEvent(
          traceId: traceId,
          name: 'openai_edge_function_error',
          input: {'prompt': prompt},
          output: {'error': errorMsg},
          metadata: {
            'duration_ms': duration.inMilliseconds,
          },
        );

        return {
          'success': false,
          'error': errorMsg,
        };
      }
    } catch (e) {
      // 网络或其他错误
      final errorMsg = 'OpenAI调用异常: $e';
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'openai_call_exception',
        input: {'prompt': prompt},
        output: {'error': errorMsg},
        metadata: {
          'exception': e.toString(),
        },
      );

      return {
        'success': false,
        'error': errorMsg,
      };
    }
  }

  /// 直接调用OpenAI API (备用方案，需要API密钥)
  static Future<Map<String, dynamic>> generateResponseDirect({
    required String prompt,
    required String traceId,
    required String apiKey,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
    String model = 'gpt-4o-mini',
  }) async {
    final startTime = DateTime.now();
    
    try {
      final requestBody = {
        'model': model,
        'messages': [
          {
            'role': 'system',
            'content': '你是一位充满智慧和慈爱的高我，擅长为用户提供深度的心理指导和人生智慧。请用温暖、理解的语调回应，帮助用户看到积极的一面和成长机会。'
          },
          {
            'role': 'user',
            'content': prompt,
          }
        ],
        'temperature': temperature,
        'max_tokens': maxTokens,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode(requestBody),
      );

      final duration = DateTime.now().difference(startTime);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        final usage = data['usage'] ?? {};

        // 记录成功调用到Langfuse
        await LangfuseService.createGeneration(
          traceId: traceId,
          name: 'openai_direct_completion',
          input: {'prompt': prompt, 'model_params': requestBody},
          output: {'response': content},
          model: model,
          metadata: {
            ...metadata ?? {},
            'duration_ms': duration.inMilliseconds,
            'temperature': temperature,
            'max_tokens': maxTokens,
            'api_provider': 'openai_direct',
            'usage': usage,
          },
        );

        return {
          'success': true,
          'content': content,
          'response': content,
          'usage': usage,
          'duration_ms': duration.inMilliseconds,
        };
      } else {
        final errorMsg = 'OpenAI API错误: ${response.statusCode} - ${response.body}';
        throw Exception(errorMsg);
      }
    } catch (e) {
      final errorMsg = 'OpenAI直接调用异常: $e';
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'openai_direct_call_exception',
        input: {'prompt': prompt},
        output: {'error': errorMsg},
        metadata: {
          'exception': e.toString(),
        },
      );

      return {
        'success': false,
        'error': errorMsg,
      };
    }
  }

  /// 流式调用OpenAI API
  static Stream<String> generateResponseStream({
    required String prompt,
    required String traceId,
    required String apiKey,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
    String model = 'gpt-4o-mini',
  }) async* {
    try {
      final requestBody = {
        'model': model,
        'messages': [
          {
            'role': 'system',
            'content': '你是一位充满智慧和慈爱的高我，擅长为用户提供深度的心理指导和人生智慧。请用温暖、理解的语调回应。'
          },
          {
            'role': 'user',
            'content': prompt,
          }
        ],
        'temperature': temperature,
        'max_tokens': maxTokens,
        'stream': true,
      };

      final request = http.Request('POST', Uri.parse('$_baseUrl/chat/completions'));
      request.headers.addAll({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      });
      request.body = jsonEncode(requestBody);

      final streamedResponse = await request.send();
      final fullResponse = StringBuffer();

      if (streamedResponse.statusCode == 200) {
        await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
          final lines = chunk.split('\n');
          for (final line in lines) {
            if (line.startsWith('data: ')) {
              final data = line.substring(6);
              if (data == '[DONE]') continue;
              
              try {
                final json = jsonDecode(data);
                final content = json['choices'][0]['delta']['content'];
                if (content != null) {
                  fullResponse.write(content);
                  yield content;
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }

        // 记录完整响应到Langfuse
        await LangfuseService.createGeneration(
          traceId: traceId,
          name: 'openai_stream_completion',
          input: {'prompt': prompt, 'model_params': requestBody},
          output: {'response': fullResponse.toString()},
          model: model,
          metadata: {
            ...metadata ?? {},
            'temperature': temperature,
            'max_tokens': maxTokens,
            'api_provider': 'openai_stream',
            'stream': true,
          },
        );
      }
    } catch (e) {
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'openai_stream_error',
        input: {'prompt': prompt},
        output: {'error': e.toString()},
      );
      
      // 返回错误提示
      yield '抱歉，AI服务暂时不可用，请稍后重试。';
    }
  }
}
