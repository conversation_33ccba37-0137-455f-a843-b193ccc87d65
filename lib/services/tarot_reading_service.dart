import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

/// 塔罗解读服务 - 处理塔罗解读的后端保存和历史记录
class TarotReadingService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// 保存塔罗解读到后端
  Future<String?> saveTarotReading({
    required String question,
    required List<String> cardNames,
    required String interpretation,
    required String readingType,
    String spreadType = 'three_card',
    String? traceId,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ 用户未登录，无法保存塔罗解读');
        return null;
      }

      // 构建卡牌数据
      final cardsData = cardNames.asMap().entries.map((entry) {
        final index = entry.key;
        final cardName = entry.value;
        String position;
        
        // 根据位置确定牌的含义
        switch (index) {
          case 0:
            position = 'past';
            break;
          case 1:
            position = 'present';
            break;
          case 2:
            position = 'future';
            break;
          default:
            position = 'card_${index + 1}';
        }

        return {
          'name': cardName,
          'position': position,
          'index': index,
        };
      }).toList();

      // 保存到数据库
      final response = await _supabase
          .from('tarot_readings')
          .insert({
            'user_id': user.id,
            'question': question,
            'spread_type': spreadType,
            'cards': cardsData,
            'interpretation': interpretation,
            'reading_type': readingType,
            'cards_drawn': cardNames.length,
            'ai_model': 'deepseek',
            'trace_id': traceId,
            'created_at': DateTime.now().toIso8601String(),
          })
          .select('id')
          .single();

      final readingId = response['id'] as String;
      debugPrint('✅ 塔罗解读已保存，ID: $readingId');
      return readingId;
    } catch (e) {
      debugPrint('❌ 保存塔罗解读失败: $e');
      return null;
    }
  }

  /// 获取用户的塔罗解读历史
  Future<List<Map<String, dynamic>>> getTarotReadingHistory({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ 用户未登录，无法获取塔罗解读历史');
        return [];
      }

      final response = await _supabase
          .from('tarot_readings')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      debugPrint('✅ 获取到 ${response.length} 条塔罗解读历史');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ 获取塔罗解读历史失败: $e');
      return [];
    }
  }

  /// 根据日期获取塔罗解读
  Future<List<Map<String, dynamic>>> getTarotReadingsByDate(DateTime date) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return [];
      }

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final response = await _supabase
          .from('tarot_readings')
          .select('*')
          .eq('user_id', user.id)
          .gte('created_at', startOfDay.toIso8601String())
          .lt('created_at', endOfDay.toIso8601String())
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ 根据日期获取塔罗解读失败: $e');
      return [];
    }
  }

  /// 删除塔罗解读
  Future<bool> deleteTarotReading(String readingId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return false;
      }

      await _supabase
          .from('tarot_readings')
          .delete()
          .eq('id', readingId)
          .eq('user_id', user.id);

      debugPrint('✅ 塔罗解读已删除，ID: $readingId');
      return true;
    } catch (e) {
      debugPrint('❌ 删除塔罗解读失败: $e');
      return false;
    }
  }

  /// 更新塔罗解读的用户反馈
  Future<bool> updateTarotReadingFeedback({
    required String readingId,
    int? accuracy,
    int? usefulness,
    int? satisfaction,
    String? feedback,
    Map<String, dynamic>? userFeedback,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return false;
      }

      final updateData = <String, dynamic>{};
      if (accuracy != null) updateData['accuracy'] = accuracy;
      if (usefulness != null) updateData['usefulness'] = usefulness;
      if (satisfaction != null) updateData['satisfaction'] = satisfaction;
      if (feedback != null) updateData['feedback'] = feedback;
      if (userFeedback != null) updateData['user_feedback'] = userFeedback;

      if (updateData.isEmpty) {
        return false;
      }

      await _supabase
          .from('tarot_readings')
          .update(updateData)
          .eq('id', readingId)
          .eq('user_id', user.id);

      debugPrint('✅ 塔罗解读反馈已更新，ID: $readingId');
      return true;
    } catch (e) {
      debugPrint('❌ 更新塔罗解读反馈失败: $e');
      return false;
    }
  }

  /// 获取塔罗解读统计信息
  Future<Map<String, dynamic>> getTarotReadingStats() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {};
      }

      final response = await _supabase
          .from('tarot_readings')
          .select('reading_type, created_at')
          .eq('user_id', user.id);

      final readings = List<Map<String, dynamic>>.from(response);
      
      return {
        'total_readings': readings.length,
        'this_month': readings.where((r) {
          final createdAt = DateTime.parse(r['created_at']);
          final now = DateTime.now();
          return createdAt.year == now.year && createdAt.month == now.month;
        }).length,
        'reading_types': readings.fold<Map<String, int>>({}, (acc, r) {
          final type = r['reading_type'] as String? ?? 'general';
          acc[type] = (acc[type] ?? 0) + 1;
          return acc;
        }),
      };
    } catch (e) {
      debugPrint('❌ 获取塔罗解读统计失败: $e');
      return {};
    }
  }
}
