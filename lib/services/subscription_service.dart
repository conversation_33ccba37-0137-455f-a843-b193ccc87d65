import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/services/user_data_manager.dart';

enum SubscriptionTier {
  free,      // Free: 5 soul mirror chats per session + basic interface
  basic,     // [DEPRECATED] Basic Member: kept for compatibility, maps to premium
  premium    // Premium Member: unlimited soul mirror chats + personalized features
}

enum UsageType {
  tarotReading,    // 塔罗解读
  soulMirrorChat   // 高我对话
}

enum SubscriptionPeriod {
  weekly,    // Weekly subscription
  monthly,   // Monthly subscription (20% off)
  yearly     // Annual subscription (40% off)
}

class SubscriptionService extends ChangeNotifier {
  // 产品ID定义（美元定价）
  static const String _basicWeeklyId = 'com.G3RHCPDDQR.aitarotreading.basic_weekly_usd';
  static const String _basicMonthlyId = 'com.G3RHCPDDQR.aitarotreading.basic_monthly_usd';
  static const String _basicYearlyId = 'com.G3RHCPDDQR.aitarotreading.basic_yearly_usd';
  static const String _premiumWeeklyId = 'com.G3RHCPDDQR.aitarotreading.p_weekly_usd';
  static const String _premiumMonthlyId = 'com.G3RHCPDDQR.aitarotreading.p_monthly_usd';
  static const String _premiumYearlyId = 'com.G3RHCPDDQR.aitarotreading.p_yearly_usd';

  // 🚀 生产模式 - 正式发布版本
  static bool _isTestFlightMode = false;
  
  // 设置测试模式（仅用于开发调试）
  static void setTestFlightMode(bool enabled) {
    _isTestFlightMode = enabled;
    if (kDebugMode) {
      debugPrint('🧪 TestFlight测试模式: ${enabled ? "启用" : "禁用"}');
    }
  }
  
  // 获取当前测试模式状态
  static bool get isTestFlightMode => _isTestFlightMode;
  
  static const String _subscriptionTierKey = 'subscription_tier';
  static const String _subscriptionPeriodKey = 'subscription_period';
  static const String _subscriptionExpiryKey = 'subscription_expiry';
  static const String _dailyUsageCountKey = 'daily_usage_count';
  static const String _weeklyUsageCountKey = 'weekly_usage_count';
  static const String _lastUsageDateKey = 'last_usage_date';
  static const String _lastWeekStartKey = 'last_week_start';

  // 高我对话相关的存储键
  static const String _weeklySoulMirrorCountKey = 'weekly_soul_mirror_count';
  static const String _dailySoulMirrorCountKey = 'daily_soul_mirror_count';
  static const String _lastSoulMirrorUsageDateKey = 'last_soul_mirror_usage_date';

  // 获取当前用户ID
  String? get _currentUserId => Supabase.instance.client.auth.currentUser?.id;
  
  // 生成用户特定的键名
  String _getUserSpecificKey(String baseKey) {
    final userId = _currentUserId;
    if (userId != null) {
      return UserDataManager.createUserSpecificKey(baseKey, userId);
    }
    // ❌ 修复：如果没有用户ID，使用临时设备键，避免数据污染
    return 'temp_device_$baseKey'; 
  }
  
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  bool _isAvailable = false;
  SubscriptionTier _currentTier = SubscriptionTier.free;
  SubscriptionPeriod _currentPeriod = SubscriptionPeriod.weekly;
  List<ProductDetails> _products = [];
  bool _isLoading = false;
  String? _errorMessage;
  int _dailyUsageCount = 0;
  
  // Getters
  bool get isAvailable => _isAvailable;
  SubscriptionTier get currentTier => _currentTier;
  SubscriptionPeriod get currentPeriod => _currentPeriod;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<ProductDetails> get products => _products;
  int get dailyUsageCount => _dailyUsageCount;
  
  // 会员权限检查
  bool get isSubscribed => _currentTier != SubscriptionTier.free;
  bool get isPremium => _currentTier == SubscriptionTier.premium;
  bool get isBasic => _currentTier == SubscriptionTier.basic;
  
  // 塔罗解读使用次数限制
  int getTarotUsageLimit() {
    switch (_currentTier) {
      case SubscriptionTier.free:
        return 1; // 每周1次塔罗解读
      case SubscriptionTier.basic:
        return 1; // 每天1次塔罗解读
      case SubscriptionTier.premium:
        return 5; // 每天5次塔罗解读
    }
  }

  // 🔮 塔罗探索内心功能权限检查（仅付费会员可用）
  bool get canUseTarotExplore {
    return _currentTier != SubscriptionTier.free; // 只有付费会员可以使用
  }

  // 高我对话使用次数限制
  int getSoulMirrorUsageLimit() {
    switch (_currentTier) {
      case SubscriptionTier.free:
        return 5; // 每次对话5轮高我对话
      case SubscriptionTier.basic:
        return -1; // 无限制 (deprecated, maps to premium)
      case SubscriptionTier.premium:
        return -1; // 无限制
    }
  }

  // 兼容性：保持原有的usageLimit方法（用于塔罗解读）
  int get usageLimit => getTarotUsageLimit();
  
  // 是否按周限制（免费会员）
  bool get isWeeklyLimit => _currentTier == SubscriptionTier.free;
  
  // 检查塔罗解读是否可用
  bool get canUseTarotToday {
    if (isWeeklyLimit) {
      // 免费会员按周限制
      return _weeklyUsageCount < getTarotUsageLimit();
    } else {
      // 付费会员按日限制
      return _dailyUsageCount < getTarotUsageLimit();
    }
  }

  // 检查高我对话是否可用
  bool get canUseSoulMirrorToday {
    final limit = getSoulMirrorUsageLimit();
    if (limit == -1) {
      // 无限制
      return true;
    }

    if (_currentTier == SubscriptionTier.free) {
      // 免费会员按会话限制（每次对话5轮）
      return _sessionSoulMirrorCount < limit;
    } else {
      // 付费会员无限制
      return true;
    }
  }

  // 新增：检查当前会话是否可以继续对话
  bool get canContinueCurrentSession {
    return canUseSoulMirrorToday;
  }

  // 新增：重置会话计数（开始新对话时调用）
  void resetSessionCount() {
    _sessionSoulMirrorCount = 0;
    notifyListeners();
  }

  // 兼容性：保持原有的canUseToday方法（用于塔罗解读）
  bool get canUseToday => canUseTarotToday;
  
  // 添加周使用次数
  int _weeklyUsageCount = 0;
  int get weeklyUsageCount => _weeklyUsageCount;

  // 高我对话使用次数
  int _weeklySoulMirrorCount = 0;
  int _dailySoulMirrorCount = 0;
  int _sessionSoulMirrorCount = 0; // 新增：当前会话的高我对话轮数
  int get weeklySoulMirrorCount => _weeklySoulMirrorCount;
  int get dailySoulMirrorCount => _dailySoulMirrorCount;
  int get sessionSoulMirrorCount => _sessionSoulMirrorCount;
  
  // 获取剩余塔罗解读次数
  int get remainingTarotUsage {
    final limit = getTarotUsageLimit();
    if (isWeeklyLimit) {
      // 免费会员按周限制
      return (limit - _weeklyUsageCount).clamp(0, limit);
    } else {
      // 付费会员按日限制
      return (limit - _dailyUsageCount).clamp(0, limit);
    }
  }

  // 获取剩余高我对话次数
  int get remainingSoulMirrorUsage {
    final limit = getSoulMirrorUsageLimit();
    if (limit == -1) {
      // 无限制
      return 999; // 返回一个大数字表示无限制
    }

    if (_currentTier == SubscriptionTier.free) {
      // 免费会员按会话限制（每次对话5轮）
      return (limit - _sessionSoulMirrorCount).clamp(0, limit);
    } else {
      // 付费会员无限制
      return 999;
    }
  }

  // 兼容性：保持原有的remainingFreeUsage方法（用于塔罗解读）
  int get remainingFreeUsage => remainingTarotUsage;
  
  SubscriptionService() {
    _initialize();
  }
  
  Future<void> _initialize() async {
    if (kDebugMode) {
      debugPrint('🚀 开始初始化订阅服务...');
      debugPrint('📱 当前平台: ${defaultTargetPlatform.name}');
      debugPrint('📱 是否为iOS: ${Platform.isIOS}');
    }

    _isAvailable = await _inAppPurchase.isAvailable();
    if (kDebugMode) {
      debugPrint('📱 应用内购买可用性: $_isAvailable');
    }

    if (_isAvailable) {
      debugPrint('✅ 应用内购买可用，开始设置...');

      // 监听购买更新
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => _handleError('购买监听错误: $error'),
      );
      debugPrint('✅ 购买流监听已设置');

      // 加载产品信息
      debugPrint('🔍 开始加载产品信息...');
      await _loadProducts();

      // 检查当前订阅状态
      debugPrint('🔍 检查当前订阅状态...');
      await _checkSubscriptionStatus();

      // ✅ 新增：检查并重置新用户数据
      debugPrint('🔍 检查新用户数据...');
      await _checkAndResetForNewUser();

      // 检查每日和每周使用次数
      debugPrint('🔍 检查使用次数...');
      await _checkDailyUsage();
      await _checkWeeklyUsage();
      await _checkSoulMirrorUsage();

      // 恢复之前的购买
      debugPrint('🔍 恢复之前的购买...');
      await restorePurchases();

      debugPrint('✅ 订阅服务初始化完成');
    } else {
      debugPrint('❌ 应用内购买不可用');
      _handleError('应用内购买功能不可用，请检查设备设置');
    }

    notifyListeners();
  }
  
  Future<void> _loadProducts() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      // 加载所有订阅产品
      const Set<String> productIds = {
        _basicWeeklyId,
        _basicMonthlyId,
        _basicYearlyId,
        _premiumWeeklyId,
        _premiumMonthlyId,
        _premiumYearlyId,
      };
      
      debugPrint('🔍 查询产品ID列表: $productIds');

      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);

      debugPrint('🔍 查询产品结果:');
      debugPrint('✅ 找到的产品: ${response.productDetails.map((p) => p.id).toList()}');
      debugPrint('❌ 未找到的产品: ${response.notFoundIDs}');
      debugPrint('📦 产品详情:');
      for (final product in response.productDetails) {
        debugPrint('  - ID: ${product.id}');
        debugPrint('  - 标题: ${product.title}');
        debugPrint('  - 价格: ${product.price}');
        debugPrint('  - 描述: ${product.description}');
      }
      
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('⚠️ 未找到的产品: ${response.notFoundIDs}');
        debugPrint('💡 这在沙盒测试中是正常的，产品可能处于"Waiting for Review"状态');
        // 在沙盒环境中，即使产品是"Waiting for Review"状态也可以测试
        // 所以我们不立即报错，而是继续处理找到的产品
        if (response.productDetails.isEmpty) {
          _handleError('找不到任何产品: ${response.notFoundIDs.join(", ")}\n\n可能原因:\n1. 产品仍在审核中("Waiting for Review")\n2. 请在沙盒环境中测试\n3. 确保使用沙盒测试账号');
        }
      }
      
      _products = response.productDetails;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _handleError('加载产品失败: $e');
    }
  }
  
  // 购买指定套餐
  Future<void> purchaseSubscription(SubscriptionTier tier, SubscriptionPeriod period) async {
    if (!_isAvailable) {
      _handleError('订阅服务不可用');
      return;
    }
    
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      final String productId = _getProductId(tier, period);
      final ProductDetails? productDetails = _products.where((p) => p.id == productId).firstOrNull;
      
      debugPrint('🔍 尝试购买产品: $productId');
      debugPrint('🔍 可用产品列表: ${_products.map((p) => p.id).toList()}');
      
      if (productDetails == null) {
        _handleError('找不到对应的产品: $productId\n\n可能原因:\n1. App Store Connect 中未创建此产品\n2. 产品状态不是"Ready for Sale"\n3. 产品ID拼写错误');
        return;
      }
      
      debugPrint('✅ 找到产品: ${productDetails.title} - ${productDetails.price}');
      
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );
      
      debugPrint('🚀 开始执行购买...');

      try {
        // 对于订阅产品，使用 buyNonConsumable 方法
        // 这是iOS订阅产品的正确调用方式
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
        debugPrint('✅ 购买请求已发送');
      } catch (storeKitError) {
        debugPrint('❌ StoreKit购买错误: $storeKitError');

        // 检查是否是特定的StoreKit错误
        if (storeKitError.toString().contains('StoreKitError')) {
          debugPrint('🔧 检测到StoreKit错误，可能的原因:');
          debugPrint('  1. 产品配置问题');
          debugPrint('  2. 沙盒账号问题');
          debugPrint('  3. 网络连接问题');
          debugPrint('  4. App Store Connect配置问题');

          _handleError('购买失败：StoreKit错误\n\n可能原因:\n• 产品配置问题\n• 请确保使用沙盒测试账号\n• 检查网络连接\n• 产品可能仍在审核中');
        } else {
          _handleError('购买失败: $storeKitError');
        }
        return;
      }
    } catch (e) {
      debugPrint('❌ 购买异常: $e');
      _handleError('购买失败: $e');
    }
  }
  
  String _getProductId(SubscriptionTier tier, SubscriptionPeriod period) {
    switch (tier) {
      case SubscriptionTier.basic:
        switch (period) {
          case SubscriptionPeriod.weekly:
            return _basicWeeklyId;
          case SubscriptionPeriod.monthly:
            return _basicMonthlyId;
          case SubscriptionPeriod.yearly:
            return _basicYearlyId;
        }
      case SubscriptionTier.premium:
        switch (period) {
          case SubscriptionPeriod.weekly:
            return _premiumWeeklyId;
          case SubscriptionPeriod.monthly:
            return _premiumMonthlyId;
          case SubscriptionPeriod.yearly:
            return _premiumYearlyId;
        }
      case SubscriptionTier.free:
        return '';
    }
  }
  
  // 获取产品价格
  String getPrice(SubscriptionTier tier, SubscriptionPeriod period) {
    final String productId = _getProductId(tier, period);
    final ProductDetails? product = _products.where((p) => p.id == productId).firstOrNull;
    
    if (product != null) {
      return product.price;
    }
    
         // 默认价格（美元）
     switch (tier) {
       case SubscriptionTier.basic:
         switch (period) {
           case SubscriptionPeriod.weekly:
             return '\$2.99';
           case SubscriptionPeriod.monthly:
             return '\$9.99'; // 2.99 * 4 * 0.8
           case SubscriptionPeriod.yearly:
             return '\$89.99'; // 2.99 * 52 * 0.6
         }
       case SubscriptionTier.premium:
         switch (period) {
           case SubscriptionPeriod.weekly:
             return '\$4.99';
           case SubscriptionPeriod.monthly:
             return '\$17.99'; // 4.99 * 4 * 0.8
           case SubscriptionPeriod.yearly:
             return '\$149.99'; // 4.99 * 52 * 0.6
         }
       case SubscriptionTier.free:
         return 'Free';
     }
  }
  
  // 获取优惠信息
  String getDiscountText(SubscriptionPeriod period) {
    switch (period) {
      case SubscriptionPeriod.weekly:
        return '';
      case SubscriptionPeriod.monthly:
        return '月度8折优惠';
      case SubscriptionPeriod.yearly:
        return '年度6折优惠';
    }
  }
  
  // 记录塔罗解读使用次数
  Future<void> recordTarotUsage() async {
    await _checkDailyUsage(); // 先检查是否是新的一天
    await _checkWeeklyUsage(); // 检查是否是新的一周

    if (!canUseTarotToday) {
      if (isWeeklyLimit) {
        throw Exception('本周塔罗解读次数已达上限');
      } else {
        throw Exception('今日塔罗解读次数已达上限');
      }
    }

    final prefs = await SharedPreferences.getInstance();

    if (isWeeklyLimit) {
      // 免费会员记录周使用次数
      _weeklyUsageCount++;
      await prefs.setInt(_getUserSpecificKey(_weeklyUsageCountKey), _weeklyUsageCount);
    } else {
      // 付费会员记录日使用次数
      _dailyUsageCount++;
      await prefs.setInt(_getUserSpecificKey(_dailyUsageCountKey), _dailyUsageCount);
    }

    await prefs.setString(_getUserSpecificKey(_lastUsageDateKey), DateTime.now().toIso8601String());

    notifyListeners();
  }

  // 记录高我对话使用次数
  Future<void> recordSoulMirrorUsage() async {
    if (!canUseSoulMirrorToday) {
      if (_currentTier == SubscriptionTier.free) {
        throw Exception('本次对话轮数已达上限（5轮）');
      } else {
        throw Exception('使用次数已达上限');
      }
    }

    if (_currentTier == SubscriptionTier.free) {
      // 免费会员记录会话使用次数
      _sessionSoulMirrorCount++;
    }
    // 付费会员无需记录，无限制

    notifyListeners();
  }

  // 兼容性：保持原有的recordUsage方法（用于塔罗解读）
  Future<void> recordUsage() async => await recordTarotUsage();
  
  Future<void> _checkDailyUsage() async {
    final prefs = await SharedPreferences.getInstance();
    final lastUsageString = prefs.getString(_getUserSpecificKey(_lastUsageDateKey));
    final today = DateTime.now();
    
    if (lastUsageString != null) {
      final lastUsageDate = DateTime.parse(lastUsageString);
      if (!_isSameDay(today, lastUsageDate)) {
        // 新的一天，重置计数
        _dailyUsageCount = 0;
        await prefs.setInt(_getUserSpecificKey(_dailyUsageCountKey), 0);
      } else {
        // 同一天，加载已使用次数
        _dailyUsageCount = prefs.getInt(_getUserSpecificKey(_dailyUsageCountKey)) ?? 0;
      }
    } else {
      // 首次使用
      _dailyUsageCount = 0;
    }
  }
  
  Future<void> _checkWeeklyUsage() async {
    final prefs = await SharedPreferences.getInstance();
    final lastWeekStartString = prefs.getString(_getUserSpecificKey(_lastWeekStartKey));
    final today = DateTime.now();
    final weekStart = _getWeekStart(today);
    
    if (lastWeekStartString != null) {
      final lastWeekStart = DateTime.parse(lastWeekStartString);
      if (!_isSameWeek(weekStart, lastWeekStart)) {
        // 新的一周，重置计数
        _weeklyUsageCount = 0;
        await prefs.setInt(_getUserSpecificKey(_weeklyUsageCountKey), 0);
        await prefs.setString(_getUserSpecificKey(_lastWeekStartKey), weekStart.toIso8601String());
      } else {
        // 同一周，加载已使用次数
        _weeklyUsageCount = prefs.getInt(_getUserSpecificKey(_weeklyUsageCountKey)) ?? 0;
      }
    } else {
      // 首次使用
      _weeklyUsageCount = 0;
      await prefs.setString(_getUserSpecificKey(_lastWeekStartKey), weekStart.toIso8601String());
    }
  }

  Future<void> _checkSoulMirrorUsage() async {
    final prefs = await SharedPreferences.getInstance();

    if (_currentTier == SubscriptionTier.free) {
      // 免费会员检查周使用次数
      await _checkWeeklyUsage(); // 复用周检查逻辑
      _weeklySoulMirrorCount = prefs.getInt(_getUserSpecificKey(_weeklySoulMirrorCountKey)) ?? 0;
    } else {
      // 付费会员检查日使用次数
      final lastUsageString = prefs.getString(_getUserSpecificKey(_lastSoulMirrorUsageDateKey));
      final today = DateTime.now();

      if (lastUsageString != null) {
        final lastUsageDate = DateTime.parse(lastUsageString);
        if (!_isSameDay(today, lastUsageDate)) {
          // 新的一天，重置计数
          _dailySoulMirrorCount = 0;
          await prefs.setInt(_getUserSpecificKey(_dailySoulMirrorCountKey), 0);
        } else {
          // 同一天，加载已使用次数
          _dailySoulMirrorCount = prefs.getInt(_getUserSpecificKey(_dailySoulMirrorCountKey)) ?? 0;
        }
      } else {
        // 首次使用
        _dailySoulMirrorCount = 0;
      }
    }
  }

  // ✅ 新增：检查并重置新用户数据
  Future<void> _checkAndResetForNewUser() async {
    final userId = _currentUserId;
    if (userId == null) {
      if (kDebugMode) {
        debugPrint('⚠️ 用户ID为空，跳过新用户检查');
      }
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final userCheckKey = 'user_data_checked_$userId';
      
      // 检查是否已经为这个用户初始化过数据
      final hasChecked = prefs.getBool(userCheckKey) ?? false;
      
      if (!hasChecked) {
        if (kDebugMode) {
          debugPrint('🔄 检测到新用户，重置使用次数数据: $userId');
        }
        
        // 重置所有使用次数相关数据
        _dailyUsageCount = 0;
        _weeklyUsageCount = 0;
        
        // 清除本地存储
        await prefs.remove(_getUserSpecificKey(_dailyUsageCountKey));
        await prefs.remove(_getUserSpecificKey(_weeklyUsageCountKey));
        await prefs.remove(_getUserSpecificKey(_lastUsageDateKey));
        await prefs.remove(_getUserSpecificKey(_lastWeekStartKey));
        
        // 标记已检查
        await prefs.setBool(userCheckKey, true);
        
        if (kDebugMode) {
          debugPrint('✅ 新用户数据重置完成');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ 新用户检查失败: $e');
      }
    }
  }
  
  DateTime _getWeekStart(DateTime date) {
    // 获取本周的开始日期（周一）
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }
  
  bool _isSameWeek(DateTime date1, DateTime date2) {
    final week1Start = _getWeekStart(date1);
    final week2Start = _getWeekStart(date2);
    return _isSameDay(week1Start, week2Start);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
  
  Future<void> restorePurchases() async {
    try {
      _isLoading = true;
      notifyListeners();

      await _inAppPurchase.restorePurchases();
    } catch (e) {
      _handleError('Failed to restore purchases: $e');
    }
  }

  /// 刷新订阅状态（公共方法）
  Future<void> refreshSubscriptionStatus() async {
    try {
      debugPrint('🔄 开始刷新订阅状态...');
      debugPrint('🔍 刷新前状态: Tier=${_currentTier.name}, Period=${_currentPeriod.name}');

      // 重新检查订阅状态
      await _checkSubscriptionStatus();

      debugPrint('🔍 刷新后状态: Tier=${_currentTier.name}, Period=${_currentPeriod.name}');

      // 重新检查使用次数
      await _checkDailyUsage();
      await _checkWeeklyUsage();
      await _checkSoulMirrorUsage();

      debugPrint('✅ 订阅状态刷新完成');
      debugPrint('🔄 通知UI更新...');
      notifyListeners();
      debugPrint('✅ UI通知完成');
    } catch (e) {
      debugPrint('❌ 刷新订阅状态失败: $e');
    }
  }

  /// 🔧 强制刷新订阅状态（用于邀请码激活后）
  Future<void> forceRefreshSubscriptionStatus() async {
    try {
      debugPrint('🔄 强制刷新订阅状态...');

      // 清除缓存状态
      _currentTier = SubscriptionTier.free;
      _currentPeriod = SubscriptionPeriod.weekly;

      // 重新检查所有状态
      await _checkSubscriptionStatus();
      await _checkDailyUsage();
      await _checkWeeklyUsage();
      await _checkSoulMirrorUsage();

      // 强制通知UI更新
      notifyListeners();

      debugPrint('✅ 强制刷新完成: Tier=${_currentTier.name}, Period=${_currentPeriod.name}');
    } catch (e) {
      debugPrint('❌ 强制刷新失败: $e');
    }
  }
  
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _handlePurchase(purchaseDetails);
    }
  }
  
  Future<void> _handlePurchase(PurchaseDetails purchaseDetails) async {
    try {
      debugPrint('🔍 处理购买状态: ${purchaseDetails.status}');
      debugPrint('🔍 产品ID: ${purchaseDetails.productID}');
      debugPrint('🔍 购买ID: ${purchaseDetails.purchaseID}');
      
      if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        
        debugPrint('✅ 购买成功，开始验证...');
        
        // 验证购买
        final bool isValid = await _verifyPurchase(purchaseDetails);
        
        if (isValid) {
          debugPrint('✅ 购买验证成功，激活订阅...');
          await _activateSubscription(purchaseDetails);
          
          // 🔧 修复：显示购买成功消息
          _showPurchaseSuccessMessage();
        } else {
          debugPrint('❌ 购买验证失败');
          _handleError('购买验证失败，请联系客服');
        }
        
        // 完成购买
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
          debugPrint('✅ 购买流程完成');
        }
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        debugPrint('❌ 购买错误: ${purchaseDetails.error}');
        final errorMessage = _getLocalizedErrorMessage(purchaseDetails.error);
        _handleError(errorMessage);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        debugPrint('❌ 用户取消购买');
        _handleError('用户取消了购买');
      } else if (purchaseDetails.status == PurchaseStatus.pending) {
        debugPrint('⏳ 购买待处理...');
        // 不显示错误，等待进一步状态更新
      }
    } catch (e) {
      debugPrint('❌ 处理购买异常: $e');
      _handleError('处理购买失败: $e');
    }
    
    _isLoading = false;
    notifyListeners();
  }
  
  // 🔧 新增：显示购买成功消息
  void _showPurchaseSuccessMessage() {
    debugPrint('🎉 购买成功！订阅已激活');
    // 设置成功状态，让UI层显示消息
    _errorMessage = null;
    notifyListeners();
  }
  
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      debugPrint('🔍 开始验证购买收据...');

      // 获取收据数据
      String? receiptData;

      if (Platform.isIOS) {
        // iOS: 从StoreKit获取收据数据
        debugPrint('📱 获取iOS收据数据...');
        debugPrint('🔍 Purchase ID: ${purchaseDetails.purchaseID}');
        debugPrint('🔍 Product ID: ${purchaseDetails.productID}');
        debugPrint('🔍 Transaction Date: ${purchaseDetails.transactionDate}');

        if (purchaseDetails.verificationData.serverVerificationData.isNotEmpty) {
          receiptData = purchaseDetails.verificationData.serverVerificationData;
          debugPrint('✅ 收据数据获取成功，长度: ${receiptData.length}');
          debugPrint('🔍 收据数据前100字符: ${receiptData.substring(0, receiptData.length > 100 ? 100 : receiptData.length)}...');
        } else {
          debugPrint('❌ 无法获取iOS收据数据');
          debugPrint('❌ serverVerificationData为空');
          return false;
        }
      } else {
        debugPrint('❌ 当前只支持iOS收据验证');
        return false;
      }

      if (receiptData.isEmpty) {
        debugPrint('❌ 收据数据为空');
        return false;
      }

      // 调用Supabase Edge Function验证收据
      debugPrint('📡 调用收据验证服务...');
      debugPrint('📦 收据数据长度: ${receiptData.length}');

      final response = await Supabase.instance.client.functions.invoke(
        'verify-receipt',  // 修正函数名称
        body: {
          'receiptData': receiptData,
          // 如果有App Store Connect共享密钥，可以在这里添加
          // 'password': 'your_shared_secret'
        },
      );

      debugPrint('📡 收据验证服务响应: ${response.status}');
      debugPrint('📦 响应数据: ${response.data}');

      if (response.status == 200 && response.data != null) {
        final verificationResult = response.data as Map<String, dynamic>;
        final bool isValid = verificationResult['success'] == true;
        final String? environment = verificationResult['environment'];
        final int? statusCode = verificationResult['status'];

        debugPrint('🔍 验证结果详情:');
        debugPrint('  - 成功: $isValid');
        debugPrint('  - 环境: $environment');
        debugPrint('  - 状态码: $statusCode');

        // 处理特殊状态码
        if (!isValid && statusCode != null) {
          // 21006: 收据有效但订阅已过期 - 这在生产环境中是正常的
          if (statusCode == 21006) {
            if (kDebugMode) {
              debugPrint('⚠️ 状态码21006：订阅已过期，但收据有效');
            }
            // 订阅过期但收据有效，应该允许用户重新订阅
            return true;
          }

          // 21007: 沙盒收据在生产环境验证 - Edge Function应该已经处理了这种情况
          if (statusCode == 21007) {
            if (kDebugMode) {
              debugPrint('⚠️ 状态码21007：沙盒收据，Edge Function应该已自动切换到沙盒验证');
            }
            return true;
          }

          // 21005: 收据服务器暂时不可用 - 临时错误，应该允许重试
          if (statusCode == 21005) {
            if (kDebugMode) {
              debugPrint('⚠️ 状态码21005：Apple服务器暂时不可用');
            }
            // 临时服务器问题，给用户一次机会
            return true;
          }
        }

        if (isValid) {
          if (kDebugMode) {
            debugPrint('✅ 收据验证成功 - 环境: $environment');
          }
          return true;
        } else {
          final String? error = verificationResult['error'];
          if (kDebugMode) {
            debugPrint('❌ 收据验证失败: $error');
            debugPrint('❌ Apple状态码: $statusCode');
          }

          // 根据状态码决定是否允许购买
          if (statusCode != null) {
            // 严重错误状态码，拒绝购买
            final criticalErrors = [21000, 21002, 21003, 21004, 21008, 21010];
            if (criticalErrors.contains(statusCode)) {
              if (kDebugMode) {
                debugPrint('🚀 生产模式：严重验证错误，拒绝购买 (状态码: $statusCode)');
              }
              _handleError('购买验证失败，请重试或联系客服 (错误码: $statusCode)');
              return false;
            }
          }

          // 其他情况给用户一次机会
          if (kDebugMode) {
            debugPrint('⚠️ 验证失败但允许继续 - 状态码: $statusCode');
          }
          return true;
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ 收据验证服务调用失败: ${response.status}');
          debugPrint('❌ 错误详情: ${response.data}');
        }

        // 网络或服务器错误，给用户一次机会
        if (response.status >= 500) {
          // 服务器错误，可能是临时问题
          if (kDebugMode) {
            debugPrint('⚠️ 服务器错误，允许购买继续 (HTTP ${response.status})');
          }
          return true;
        } else {
          // 客户端错误，拒绝购买
          _handleError('收据验证失败，请重试 (HTTP ${response.status})');
          return false;
        }
      }


    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ 收据验证异常: $e');
      }

      // 检查异常类型
      final errorString = e.toString().toLowerCase();

      // 网络相关异常，给用户一次机会
      if (errorString.contains('network') ||
          errorString.contains('timeout') ||
          errorString.contains('connection') ||
          errorString.contains('socket')) {
        if (kDebugMode) {
          debugPrint('⚠️ 网络异常，允许购买继续: $e');
        }
        return true;
      }

      // 其他异常，拒绝购买
      if (kDebugMode) {
        debugPrint('🚀 生产模式：验证异常，拒绝购买');
      }
      _handleError('网络连接异常，请检查网络后重试');
      return false;
    }
  }
  
  Future<void> _activateSubscription(PurchaseDetails purchaseDetails) async {
    try {
      debugPrint('🔧 开始激活订阅...');
    final productId = purchaseDetails.productID;
      debugPrint('🔍 产品ID: $productId');
    
    // 解析产品ID确定订阅等级和周期
    SubscriptionTier tier = SubscriptionTier.free;
    SubscriptionPeriod period = SubscriptionPeriod.weekly;
    
    if (productId.contains('basic')) {
      tier = SubscriptionTier.basic;
      } else if (productId.contains('premium') || productId.contains('p_')) {
      tier = SubscriptionTier.premium;
      } else {
        debugPrint('❌ 无法识别的产品ID: $productId');
        _handleError('无法识别的订阅产品');
        return;
    }
    
    if (productId.contains('weekly')) {
      period = SubscriptionPeriod.weekly;
    } else if (productId.contains('monthly')) {
      period = SubscriptionPeriod.monthly;
    } else if (productId.contains('yearly')) {
      period = SubscriptionPeriod.yearly;
      } else {
        debugPrint('❌ 无法识别的订阅周期: $productId');
        _handleError('无法识别的订阅周期');
        return;
    }
      
      // 🔧 修复：记录状态变化
      final oldTier = _currentTier;
      final oldPeriod = _currentPeriod;
    
    _currentTier = tier;
    _currentPeriod = period;
    
    // 保存订阅状态到本地
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_getUserSpecificKey(_subscriptionTierKey), tier.name);
    await prefs.setString(_getUserSpecificKey(_subscriptionPeriodKey), period.name);
    
    // 设置过期时间
    DateTime expiryTime;
    switch (period) {
      case SubscriptionPeriod.weekly:
        expiryTime = DateTime.now().add(const Duration(days: 7));
        break;
      case SubscriptionPeriod.monthly:
        expiryTime = DateTime.now().add(const Duration(days: 30));
        break;
      case SubscriptionPeriod.yearly:
        expiryTime = DateTime.now().add(const Duration(days: 365));
        break;
    }
    
    await prefs.setString(_getUserSpecificKey(_subscriptionExpiryKey), expiryTime.toIso8601String());
    
      // 更新数据库
    await _updateUserSubscriptionInDatabase(tier, period);

    // 🔧 重要：通知UI更新会员状态
    notifyListeners();

      debugPrint('✅ 订阅激活成功!');
      debugPrint('🔄 状态更新: ${oldTier.name} -> ${_currentTier.name}, ${oldPeriod.name} -> ${_currentPeriod.name}');
    debugPrint('🔄 UI状态已通知更新');
      
      // 🔧 修复：清除错误消息
      _errorMessage = null;
      
    } catch (e) {
      debugPrint('❌ 激活订阅失败: $e');
      _handleError('激活订阅失败: $e');
    }
  }
  
  Future<void> _updateUserSubscriptionInDatabase(SubscriptionTier tier, SubscriptionPeriod period) async {
    // ✅ 订阅状态由Apple管理，本地存储已足够
    // 不需要同步到Supabase，保持架构简单
    debugPrint('✅ 订阅激活成功 - Apple管理订阅状态，本地存储用户权限');
  }
  
  Future<void> _checkSubscriptionStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tierKey = _getUserSpecificKey(_subscriptionTierKey);
      final periodKey = _getUserSpecificKey(_subscriptionPeriodKey);
      final tierString = prefs.getString(tierKey);
      final periodString = prefs.getString(periodKey);

      debugPrint('🔑 检查键名: $tierKey');
      debugPrint('🔑 检查键名: $periodKey');
      debugPrint('🔍 检查本地存储: tierString=$tierString, periodString=$periodString');

      if (tierString != null && periodString != null) {
        final oldTier = _currentTier;
        final oldPeriod = _currentPeriod;

        _currentTier = SubscriptionTier.values.firstWhere(
          (e) => e.name == tierString,
          orElse: () => SubscriptionTier.free,
        );
        _currentPeriod = SubscriptionPeriod.values.firstWhere(
          (e) => e.name == periodString,
          orElse: () => SubscriptionPeriod.weekly,
        );

        debugPrint('🔄 状态更新: ${oldTier.name} -> ${_currentTier.name}, ${oldPeriod.name} -> ${_currentPeriod.name}');

        // 检查订阅是否过期
        final expiryString = prefs.getString(_getUserSpecificKey(_subscriptionExpiryKey));
        if (expiryString != null) {
          final expiryTime = DateTime.parse(expiryString);
          debugPrint('🕐 过期时间检查: $expiryTime vs ${DateTime.now()}');
          if (DateTime.now().isAfter(expiryTime)) {
            // 订阅已过期
            debugPrint('⏰ 订阅已过期，重置为免费会员');
            _currentTier = SubscriptionTier.free;
            await prefs.setString(_getUserSpecificKey(_subscriptionTierKey), SubscriptionTier.free.name);
            await _updateUserSubscriptionInDatabase(SubscriptionTier.free, SubscriptionPeriod.weekly);
          } else {
            debugPrint('✅ 订阅仍有效');
          }
        } else {
          debugPrint('⚠️ 未找到过期时间，可能是永久会员或数据异常');
        }
      } else {
        debugPrint('📝 本地存储中无订阅信息，保持免费会员状态');
      }

      debugPrint('✅ 最终状态: Tier=${_currentTier.name}, Period=${_currentPeriod.name}');
    } catch (e) {
      debugPrint('❌ 检查订阅状态失败: $e');
    }
  }
  
  void _handleError(String message) {
    _errorMessage = message;
    _isLoading = false;
    debugPrint('❌ Subscription service error: $message');
    notifyListeners();
  }

  String _getLocalizedErrorMessage(IAPError? error) {
    if (error == null) {
      return '购买失败，请重试';
    }

    // 根据错误代码返回本地化消息
    switch (error.code) {
      case 'storekit_duplicate_product_object':
        return '产品重复，请稍后重试';
      case 'storekit_invalid_payment':
        return '支付信息无效';
      case 'storekit_invalid_product_id':
        return '产品不存在或已下架';
      case 'storekit_payment_cancelled':
        return '用户取消了支付';
      case 'storekit_payment_invalid':
        return '支付请求无效';
      case 'storekit_payment_not_allowed':
        return '当前设备不允许支付';
      case 'storekit_store_product_not_available':
        return '产品暂时不可用';
      case 'storekit_cloud_service_permission_denied':
        return '云服务权限被拒绝';
      case 'storekit_cloud_service_network_connection_failed':
        return '网络连接失败，请检查网络';
      case 'storekit_cloud_service_revoked':
        return '云服务已被撤销';
      case 'storekit_privacy_acknowledgement_required':
        return '需要确认隐私协议';
      case 'storekit_unauthorized_request_data':
        return '请求数据未授权';
      case 'storekit_invalid_offer_identifier':
        return '优惠标识符无效';
      case 'storekit_invalid_signature':
        return '签名验证失败';
      case 'storekit_missing_offer_params':
        return '缺少优惠参数';
      case 'storekit_invalid_offer_price':
        return '优惠价格无效';
      default:
        return error.message.isNotEmpty
            ? '购买失败: ${error.message}'
            : '购买失败，请重试';
    }
  }
  
  // 获取会员等级文本 - 返回翻译键
  String getTierTranslationKey() {
    switch (_currentTier) {
      case SubscriptionTier.free:
        return 'free_member';
      case SubscriptionTier.basic:
        return 'basic_member';
      case SubscriptionTier.premium:
        return 'premium_member';
    }
  }
  




  // ✅ 新增：强制重置当前用户的使用次数
  Future<void> forceResetUsageForCurrentUser() async {
    final userId = _currentUserId;
    if (userId == null) {
      debugPrint('❌ 无法重置：用户未登录');
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();

      debugPrint('🔄 开始强制重置用户使用次数: $userId');
      
      // 重置内存中的计数
      _dailyUsageCount = 0;
      _weeklyUsageCount = 0;
      
      // 清除本地存储
      await prefs.remove(_getUserSpecificKey(_dailyUsageCountKey));
      await prefs.remove(_getUserSpecificKey(_weeklyUsageCountKey));
      await prefs.remove(_getUserSpecificKey(_lastUsageDateKey));
      await prefs.remove(_getUserSpecificKey(_lastWeekStartKey));
      
      // 重新标记为已检查，避免重复重置
      final userCheckKey = 'user_data_checked_$userId';
      await prefs.setBool(userCheckKey, true);
      
      notifyListeners();
      debugPrint('✅ 用户使用次数强制重置完成');
    } catch (e) {
      debugPrint('❌ 强制重置失败: $e');
    }
  }



  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
} 