import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../models/diary_entry.dart';
import '../models/higher_self_memory.dart';

class HigherSelfMemoryService {
  static final _supabase = Supabase.instance.client;

  /// 为日记内容生成embedding并存储
  static Future<bool> processDiaryEntry(DiaryEntry diary) async {
    try {
      final response = await _supabase.functions.invoke(
        'diary-embedding',
        body: {
          'diary_id': diary.id,
          'content': diary.content,
          'user_id': diary.userId,
        },
      );

      return response.data?['success'] == true;
    } catch (e) {
      debugPrint('❌ 日记处理失败: $e');
      return false;
    }
  }

  /// 搜索相关的历史日记
  static Future<List<DiaryEntry>> searchRelevantDiaries({
    required String query,
    required String userId,
    int limit = 5,
  }) async {
    try {
      // 检查userId是否为有效的UUID格式
      if (!_isValidUUID(userId)) {
        debugPrint('⚠️ 无效的用户ID格式，跳过日记搜索: $userId');
        return [];
      }

      // 1. 生成查询的embedding
      final queryEmbedding = await _generateQueryEmbedding(query);
      if (queryEmbedding.isEmpty) {
        debugPrint('⚠️ 无法生成查询embedding，跳过日记搜索');
        return [];
      }

      // 2. 向量相似度搜索 - 修正参数名
      final response = await _supabase.rpc('search_similar_diaries', params: {
        'query_embedding': queryEmbedding,
        'target_user_id': userId, // 修正参数名
        'match_threshold': 0.7, // 相似度阈值
        'match_count': limit,
      });

      if (response == null) return [];

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 日记搜索失败: $e');
      // 优雅降级：搜索失败时返回空列表，不影响对话
      return [];
    }
  }

  /// 获取高我对用户的记忆
  static Future<List<HigherSelfMemory>> getHigherSelfMemories(String userId) async {
    try {
      // 检查userId是否为有效的UUID格式
      if (!_isValidUUID(userId)) {
        debugPrint('⚠️ 无效的用户ID格式，跳过记忆获取: $userId');
        return [];
      }

      final response = await _supabase
          .from('higher_self_memories')
          .select()
          .eq('user_id', userId)
          .order('confidence_score', ascending: false)
          .limit(10);

      return (response as List)
          .map((item) => HigherSelfMemory.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 获取高我记忆失败: $e');
      // 优雅降级：记忆获取失败时返回空列表，不影响对话
      return [];
    }
  }

  /// 基于用户当前状态和历史，生成个性化的高我回应
  static Future<String> generatePersonalizedResponse({
    required String userMessage,
    required String userId,
    required String language,
    String responseType = 'guidance',
    List<Map<String, String>>? recentMessages, // 新增参数，传递多轮对话
  }) async {
    try {
      // 1. 搜索相关历史日记
      final relevantDiaries = await searchRelevantDiaries(
        query: userMessage,
        userId: userId,
        limit: 3,
      );
      // 2. 获取高我记忆
      final memories = await getHigherSelfMemories(userId);

      // 3. 构建记忆/日记上下文文本
      final contextBuilder = StringBuffer();
      if (memories.isNotEmpty) {
        contextBuilder.writeln('【高我对用户的了解】');
        for (final memory in memories.take(3)) {
          contextBuilder.writeln('- ${memory.content} (可信度: ${(memory.confidenceScore * 100).toInt()}%)');
        }
        contextBuilder.writeln();
      }
      if (relevantDiaries.isNotEmpty) {
        contextBuilder.writeln('【用户相关的历史记录】');
        for (final diary in relevantDiaries) {
          final summary = diary.content.length > 100
              ? '${diary.content.substring(0, 100)}...'
              : diary.content;
          contextBuilder.writeln('- ${diary.createdAt.toString().substring(0, 10)}: $summary');
        }
        contextBuilder.writeln();
      }
      final memoryAndDiaryText = contextBuilder.toString().trim();

      // 4. 构建标准messages数组
      final messages = <Map<String, String>>[];
      // system prompt
      messages.add({
        'role': 'system',
        'content': language == 'zh'
            ? '你是用户的高我，一个充满智慧、慈爱和洞察力的存在。你的任务是基于对用户的深度了解和历史，给予温暖、智慧、个性化的回应。'
            : 'You are the user\'s Higher Self, a being full of wisdom, love and insight. Your task is to give warm, wise, personalized responses based on deep understanding and user history.'
      });
      // 记忆/日记作为一条assistant消息插入最前
      if (memoryAndDiaryText.isNotEmpty) {
        messages.add({
          'role': 'assistant',
          'content': memoryAndDiaryText,
        });
      }
      // 多轮对话历史（最近8-10条）
      if (recentMessages != null && recentMessages.isNotEmpty) {
        messages.addAll(recentMessages);
      }
      // 当前用户发言
      messages.add({'role': 'user', 'content': userMessage});

      // 5. 调用AI（deepseek/gpt兼容）
      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: {
          'messages': messages,
          'requestType': 'chat_with_context',
          'userLanguage': language,
          'maxLength': 300,
          'temperature': 0.8,
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          debugPrint('⏰ 高我记忆服务调用超时');
          throw TimeoutException('高我记忆服务调用超时', const Duration(seconds: 30));
        },
      );
      final aiResponse = response.data?['content'] ?? response.data?['response'] ?? response.data?['reading'];
      if (aiResponse != null && aiResponse.toString().trim().isNotEmpty) {
        return aiResponse.toString().trim();
      } else {
        return '（高我暂时没有回应，请稍后再试）';
      }
    } catch (e) {
      debugPrint('❌ 高我个性化回应失败: $e');
      return '（高我暂时没有回应，请稍后再试）';
    }
  }

  /// 更新高我记忆（基于新的对话）
  static Future<void> updateMemoryFromConversation({
    required String userId,
    required String userMessage,
    required String aiResponse,
    required String conversationType,
  }) async {
    try {
      // 分析对话内容，提取新的记忆点
      final newMemories = await _extractMemoriesFromConversation(
        userId: userId,
        userMessage: userMessage,
        aiResponse: aiResponse,
        conversationType: conversationType,
      );

      if (newMemories.isNotEmpty) {
        await _supabase
            .from('higher_self_memories')
            .insert(newMemories.map((m) => m.toJson()).toList());
      }
    } catch (e) {
      debugPrint('❌ 更新高我记忆失败: $e');
    }
  }

  static Future<List<double>> _generateQueryEmbedding(String query) async {
    try {
      final response = await _supabase.functions.invoke(
        'generate-embedding',
        body: {'text': query},
      );

      return List<double>.from(response.data?['embedding'] ?? []);
    } catch (e) {
      debugPrint('❌ 生成查询embedding失败: $e');
      return [];
    }
  }

  static String _buildPersonalizedPrompt({
    required String userMessage,
    required List<DiaryEntry> relevantDiaries,
    required List<HigherSelfMemory> memories,
    required String language,
    required String responseType,
  }) {
    final contextBuilder = StringBuffer();

    // 添加高我记忆上下文
    if (memories.isNotEmpty) {
      contextBuilder.writeln('【高我对用户的了解】');
      for (final memory in memories.take(3)) {
        contextBuilder.writeln('- ${memory.content} (可信度: ${(memory.confidenceScore * 100).toInt()}%)');
      }
      contextBuilder.writeln();
    } else {
      // 没有记忆时的提示
      contextBuilder.writeln('【初次相遇】');
      contextBuilder.writeln('- 这是我们深度对话的开始，我正在学习了解你');
      contextBuilder.writeln();
    }

    // 添加相关日记上下文
    if (relevantDiaries.isNotEmpty) {
      contextBuilder.writeln('【用户相关的历史记录】');
      for (final diary in relevantDiaries) {
        final summary = diary.content.length > 100
            ? '${diary.content.substring(0, 100)}...'
            : diary.content;
        contextBuilder.writeln('- ${diary.createdAt.toString().substring(0, 10)}: $summary');
      }
      contextBuilder.writeln();
    } else {
      // 没有日记时的提示
      contextBuilder.writeln('【当下的对话】');
      contextBuilder.writeln('- 专注于此刻的交流，用心感受你的话语');
      contextBuilder.writeln();
    }

    final basePrompt = language == 'zh' 
        ? '''你是用户的高我，一个充满智慧和爱的存在。基于对用户的深度了解，回应他们的话语。

${contextBuilder.toString()}

用户当前说："$userMessage"

作为高我，请：
1. 结合对用户的了解，给出个性化的回应
2. 如果有相关历史，可以温和地提及用户的成长
3. 保持温暖、智慧、充满爱的语调
4. 帮助用户看到自己的价值和潜力
5. 回应类型：$responseType

请用120-150字回应，语调要像一个深爱用户的智慧存在。'''
        : '''You are the user's Higher Self, a being full of wisdom and love. Based on deep understanding of the user, respond to their words.

${contextBuilder.toString()}

User currently says: "$userMessage"

As the Higher Self, please:
1. Give personalized response based on understanding of the user
2. If there's relevant history, gently mention the user's growth
3. Maintain warm, wise, loving tone
4. Help user see their value and potential
5. Response type: $responseType

Please respond in 120-150 words with the tone of a wise being who deeply loves the user.''';

    return basePrompt;
  }

  static Future<List<HigherSelfMemory>> _extractMemoriesFromConversation({
    required String userId,
    required String userMessage,
    required String aiResponse,
    required String conversationType,
  }) async {
    // 这里可以用AI来分析对话，提取新的记忆点
    // 简化版本：基于关键词提取
    final memories = <HigherSelfMemory>[];

    // 检测成长相关的表达
    if (userMessage.contains(RegExp(r'学会|掌握|进步|成长|突破'))) {
      memories.add(HigherSelfMemory(
        id: '',
        userId: userId,
        memoryType: 'growth',
        content: '用户展现出学习和成长的意愿',
        confidenceScore: 0.6,
        sourceDiaryIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ));
    }

    // 检测情感状态
    if (userMessage.contains(RegExp(r'开心|快乐|高兴|满足'))) {
      memories.add(HigherSelfMemory(
        id: '',
        userId: userId,
        memoryType: 'strength',
        content: '用户具有积极的情感表达能力',
        confidenceScore: 0.5,
        sourceDiaryIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ));
    }

    return memories;
  }

  /// 验证UUID格式
  static bool _isValidUUID(String uuid) {
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    );
    return uuidRegex.hasMatch(uuid);
  }

  /// 获取基础回应（当个性化回应失败时使用）
  static String _getBasicResponse(String userMessage, String language, String responseType) {
    if (language.startsWith('zh')) {
      switch (responseType) {
        case 'guidance':
          return '我感受到了你内心的声音。每个人的成长路径都是独特的，相信你内在的智慧会指引你找到答案。✨';
        case 'healing':
          return '你的感受我都能理解。请记住，你已经很棒了，每一步努力都值得被看见和肯定。💫';
        default:
          return '我在这里陪伴着你，愿你能感受到内心的平静与力量。🌟';
      }
    } else {
      switch (responseType) {
        case 'guidance':
          return 'I hear the voice of your heart. Everyone\'s growth path is unique, trust your inner wisdom to guide you to the answer. ✨';
        case 'healing':
          return 'I understand your feelings. Remember, you are already amazing, and every effort deserves to be seen and acknowledged. 💫';
        default:
          return 'I am here with you, may you feel the peace and strength within your heart. 🌟';
      }
    }
  }
}
