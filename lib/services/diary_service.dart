import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/diary_entry.dart';
import 'higher_self_memory_service.dart';

class DiaryService {
  static final _supabase = Supabase.instance.client;

  /// 保存聊天摘要到日记
  static Future<DiaryEntry?> saveChatSummary({
    required String userId,
    required String summary,
    required String fullConversation,
    String? sessionId,
    String chatSource = 'soul_mirror',
  }) async {
    try {
      debugPrint('💾 保存聊天摘要到日记');
      debugPrint('📊 用户ID: $userId');
      debugPrint('📊 摘要: $summary');
      debugPrint('📊 对话长度: ${fullConversation.length}字符');
      debugPrint('📊 会话ID: $sessionId');
      debugPrint('📊 来源: $chatSource');

      final insertData = {
        'user_id': userId,
        'content': fullConversation,
        'chat_summary': summary,
        'chat_source': chatSource,
        'chat_session_id': sessionId,
        'tags': ['soul_mirror', 'chat'],
        'mood_score': null, // 聊天摘要不设置心情评分
      };

      debugPrint('📊 插入数据: $insertData');

      // 保存聊天摘要到数据库
      final response = await _supabase
          .from('diary_entries')
          .insert(insertData)
          .select()
          .single();

      debugPrint('✅ 聊天摘要保存成功');
      debugPrint('📊 返回数据: $response');

      final diaryEntry = DiaryEntry.fromJson(response);
      debugPrint('📊 解析后的日记条目: chatSummary=${diaryEntry.chatSummary}, chatSource=${diaryEntry.chatSource}');

      return diaryEntry;
    } catch (e) {
      debugPrint('❌ 保存聊天摘要失败: $e');
      debugPrint('❌ 错误类型: ${e.runtimeType}');
      return null;
    }
  }

  /// 获取指定日期的所有日记（包括聊天摘要）
  static Future<List<DiaryEntry>> getDiariesByDate({
    required String userId,
    required DateTime date,
  }) async {
    try {
      debugPrint('📅 查询日期日记: ${date.toString().substring(0, 10)}');

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', userId)
          .gte('created_at', startOfDay.toIso8601String())
          .lt('created_at', endOfDay.toIso8601String())
          .order('created_at', ascending: false);

      final diaries = (response as List)
          .map((json) => DiaryEntry.fromJson(json))
          .toList();

      debugPrint('📚 找到 ${diaries.length} 条日记记录');
      return diaries;
    } catch (e) {
      debugPrint('❌ 查询日期日记失败: $e');
      return [];
    }
  }

  /// 获取指定日期的聊天摘要
  static Future<List<DiaryEntry>> getChatSummariesByDate({
    required String userId,
    required DateTime date,
  }) async {
    try {
      debugPrint('📅 查询日期聊天摘要: ${date.toString().substring(0, 10)}');

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', userId)
          .not('chat_summary', 'is', null) // 只查询有聊天摘要的记录
          .gte('created_at', startOfDay.toIso8601String())
          .lt('created_at', endOfDay.toIso8601String())
          .order('created_at', ascending: false);

      final chatSummaries = (response as List)
          .map((json) => DiaryEntry.fromJson(json))
          .toList();

      debugPrint('💬 找到 ${chatSummaries.length} 条聊天摘要记录');
      return chatSummaries;
    } catch (e) {
      debugPrint('❌ 查询日期聊天摘要失败: $e');
      return [];
    }
  }

  /// 获取聊天摘要列表
  static Future<List<DiaryEntry>> getChatSummaries({
    required String userId,
    int limit = 20,
  }) async {
    try {
      debugPrint('💬 查询聊天摘要');

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', userId)
          .inFilter('chat_source', ['soul_mirror', 'tarot'])
          .not('chat_summary', 'is', null)
          .order('created_at', ascending: false)
          .limit(limit);

      final summaries = (response as List)
          .map((json) => DiaryEntry.fromJson(json))
          .toList();

      debugPrint('💭 找到 ${summaries.length} 条聊天摘要');
      return summaries;
    } catch (e) {
      debugPrint('❌ 查询聊天摘要失败: $e');
      return [];
    }
  }

  /// 保存日记条目
  static Future<DiaryEntry?> saveDiaryEntry({
    required String content,
    int? moodScore,
    List<String>? tags,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ 用户未登录');
        return null;
      }

      // 1. 保存日记到数据库
      final response = await _supabase
          .from('diary_entries')
          .insert({
            'user_id': user.id,
            'content': content,
            'mood_score': moodScore,
            'tags': tags ?? [],
          })
          .select()
          .single();

      final diaryEntry = DiaryEntry.fromJson(response);

      // 2. 异步处理embedding（不阻塞用户操作）
      _processEmbeddingAsync(diaryEntry);

      return diaryEntry;
    } catch (e) {
      debugPrint('❌ 保存日记失败: $e');
      return null;
    }
  }

  /// 保存或更新显化日记（同一天覆盖）
  static Future<DiaryEntry?> saveOrUpdateManifestationJournal({
    required String content,
    DateTime? date,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ 用户未登录');
        return null;
      }

      final targetDate = date ?? DateTime.now();
      final startOfDay = DateTime(targetDate.year, targetDate.month, targetDate.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      // 1. 查找当天是否已有显化日记
      final existingDiaries = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .gte('created_at', startOfDay.toIso8601String())
          .lt('created_at', endOfDay.toIso8601String())
          .inFilter('tags', ['manifestation', '显化']);

      DiaryEntry? diaryEntry;

      if (existingDiaries.isNotEmpty) {
        // 2. 如果存在，更新第一条记录
        final existingId = existingDiaries.first['id'];
        debugPrint('📝 更新现有显化日记: $existingId');

        final response = await _supabase
            .from('diary_entries')
            .update({
              'content': content,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', existingId)
            .select()
            .single();

        diaryEntry = DiaryEntry.fromJson(response);
      } else {
        // 3. 如果不存在，创建新记录
        debugPrint('📝 创建新的显化日记');

        final response = await _supabase
            .from('diary_entries')
            .insert({
              'user_id': user.id,
              'content': content,
              'tags': ['manifestation', '显化', 'journal'],
            })
            .select()
            .single();

        diaryEntry = DiaryEntry.fromJson(response);
      }

      // 4. 异步处理embedding（不阻塞用户操作）
      _processEmbeddingAsync(diaryEntry);

      return diaryEntry;
    } catch (e) {
      debugPrint('❌ 保存显化日记失败: $e');
      return null;
    }
  }

  /// 获取用户的日记列表
  static Future<List<DiaryEntry>> getUserDiaries({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 获取日记列表失败: $e');
      return [];
    }
  }

  /// 获取最近的日记（用于高我回忆）
  static Future<List<DiaryEntry>> getRecentDiaries({
    int days = 7,
    int limit = 5,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final cutoffDate = DateTime.now().subtract(Duration(days: days));

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .gte('created_at', cutoffDate.toIso8601String())
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 获取最近日记失败: $e');
      return [];
    }
  }

  /// 根据心情评分获取日记
  static Future<List<DiaryEntry>> getDiariesByMood({
    int? minMood,
    int? maxMood,
    int limit = 10,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      var query = _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .not('mood_score', 'is', null);

      if (minMood != null) {
        query = query.gte('mood_score', minMood);
      }
      if (maxMood != null) {
        query = query.lte('mood_score', maxMood);
      }

      final response = await query
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 按心情获取日记失败: $e');
      return [];
    }
  }

  /// 搜索日记内容
  static Future<List<DiaryEntry>> searchDiaries({
    required String keyword,
    int limit = 10,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final response = await _supabase
          .from('diary_entries')
          .select()
          .eq('user_id', user.id)
          .textSearch('content', keyword)
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((item) => DiaryEntry.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('❌ 搜索日记失败: $e');
      return [];
    }
  }

  /// 获取日记统计信息
  static Future<Map<String, dynamic>> getDiaryStats() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return {};

      // 获取总数
      final countResponse = await _supabase
          .from('diary_entries')
          .select('id')
          .eq('user_id', user.id)
          .count();

      // 获取平均心情
      final moodResponse = await _supabase
          .rpc('get_average_mood', params: {'user_id': user.id});

      // 获取最近7天的日记数量
      final recentDate = DateTime.now().subtract(const Duration(days: 7));
      final recentResponse = await _supabase
          .from('diary_entries')
          .select('id')
          .eq('user_id', user.id)
          .gte('created_at', recentDate.toIso8601String())
          .count();

      return {
        'total_entries': countResponse.count,
        'average_mood': moodResponse ?? 0.0,
        'recent_entries': recentResponse.count,
        'days_active': await _getDaysActive(user.id),
      };
    } catch (e) {
      debugPrint('❌ 获取日记统计失败: $e');
      return {};
    }
  }

  /// 删除日记条目
  static Future<bool> deleteDiaryEntry(String diaryId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase
          .from('diary_entries')
          .delete()
          .eq('id', diaryId)
          .eq('user_id', user.id);

      return true;
    } catch (e) {
      debugPrint('❌ 删除日记失败: $e');
      return false;
    }
  }

  /// 异步处理embedding（不阻塞主线程）
  static void _processEmbeddingAsync(DiaryEntry diary) {
    // 在后台处理embedding
    Future.microtask(() async {
      try {
        await HigherSelfMemoryService.processDiaryEntry(diary);
        debugPrint('✅ 日记embedding处理完成: ${diary.id}');
      } catch (e) {
        debugPrint('❌ 日记embedding处理失败: $e');
      }
    });
  }

  /// 获取用户活跃天数
  static Future<int> _getDaysActive(String userId) async {
    try {
      final response = await _supabase
          .rpc('get_active_days', params: {'user_id': userId});
      return response ?? 0;
    } catch (e) {
      debugPrint('❌ 获取活跃天数失败: $e');
      return 0;
    }
  }

  /// 创建示例日记（用于演示）
  static Future<void> createSampleDiaries() async {
    final sampleEntries = [
      {
        'content': '今天完成了一个重要的项目，虽然过程很辛苦，但看到最终结果时真的很有成就感。团队合作也很愉快，大家都很努力。',
        'mood_score': 8,
        'tags': ['工作', '成就感', '团队合作'],
      },
      {
        'content': '最近总是感到有些焦虑，不知道是不是因为工作压力太大了。希望能找到更好的平衡方式。',
        'mood_score': 4,
        'tags': ['焦虑', '工作压力', '平衡'],
      },
      {
        'content': '和朋友聊天后心情好了很多，有时候分享真的很重要。感谢身边有这样的朋友。',
        'mood_score': 7,
        'tags': ['友谊', '分享', '感恩'],
      },
      {
        'content': '学会了一个新技能，虽然还不熟练，但进步的感觉真好。坚持学习真的会有收获。',
        'mood_score': 7,
        'tags': ['学习', '进步', '坚持'],
      },
      {
        'content': '今天心情有点低落，可能是天气的原因。不过相信明天会更好。',
        'mood_score': 5,
        'tags': ['低落', '天气', '希望'],
      },
    ];

    for (final entry in sampleEntries) {
      await saveDiaryEntry(
        content: entry['content'] as String,
        moodScore: entry['mood_score'] as int,
        tags: List<String>.from(entry['tags'] as List),
      );
      
      // 添加一些延迟，模拟不同时间写的日记
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }
}
