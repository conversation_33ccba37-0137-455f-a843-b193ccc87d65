import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

/// VisionKit背景移除服务
/// 使用iOS原生VisionKit框架进行图像背景移除
class VisionKitService {
  static const MethodChannel _channel = MethodChannel('vision_kit_service');

  /// 移除图片背景
  /// 
  /// [imagePath] 输入图片路径
  /// 返回处理后的图片路径，如果失败返回null
  static Future<String?> removeBackground(String imagePath) async {
    try {
      debugPrint('🔮 开始VisionKit背景移除: $imagePath');
      
      // 检查平台支持
      if (!Platform.isIOS) {
        debugPrint('❌ VisionKit仅支持iOS平台');
        return null;
      }

      // 检查文件是否存在
      final file = File(imagePath);
      if (!await file.exists()) {
        debugPrint('❌ 输入文件不存在: $imagePath');
        return null;
      }

      // 调用原生方法
      final result = await _channel.invokeMethod('removeBackground', {
        'imagePath': imagePath,
      });

      if (result != null && result is String) {
        debugPrint('✅ VisionKit背景移除成功: $result');
        
        // 验证输出文件是否存在
        final outputFile = File(result);
        if (await outputFile.exists()) {
          return result;
        } else {
          debugPrint('❌ 输出文件不存在: $result');
          return null;
        }
      } else {
        debugPrint('❌ VisionKit返回无效结果: $result');
        return null;
      }
    } on PlatformException catch (e) {
      debugPrint('❌ VisionKit平台异常: ${e.code} - ${e.message}');
      
      // 根据错误代码提供更友好的错误信息
      switch (e.code) {
        case 'VISION_KIT_NOT_AVAILABLE':
          debugPrint('VisionKit在此设备上不可用（需要iOS 17+）');
          break;
        case 'IMAGE_PROCESSING_FAILED':
          debugPrint('图像处理失败，可能是图片格式不支持');
          break;
        case 'NO_SUBJECT_FOUND':
          debugPrint('未检测到可提取的主体对象');
          break;
        default:
          debugPrint('未知VisionKit错误');
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ VisionKit调用异常: $e');
      return null;
    }
  }

  /// 检查VisionKit是否可用
  /// 
  /// 返回true如果VisionKit在当前设备上可用
  static Future<bool> isAvailable() async {
    try {
      if (!Platform.isIOS) {
        return false;
      }

      final result = await _channel.invokeMethod('isVisionKitAvailable');
      return result == true;
    } catch (e) {
      debugPrint('❌ 检查VisionKit可用性失败: $e');
      return false;
    }
  }

  /// 获取VisionKit版本信息
  static Future<String?> getVersion() async {
    try {
      if (!Platform.isIOS) {
        return null;
      }

      final result = await _channel.invokeMethod('getVisionKitVersion');
      return result as String?;
    } catch (e) {
      debugPrint('❌ 获取VisionKit版本失败: $e');
      return null;
    }
  }

  /// 批量处理图片背景移除
  /// 
  /// [imagePaths] 输入图片路径列表
  /// 返回处理结果列表，成功的项目包含输出路径，失败的为null
  static Future<List<String?>> removeBackgroundBatch(List<String> imagePaths) async {
    final results = <String?>[];
    
    for (final imagePath in imagePaths) {
      final result = await removeBackground(imagePath);
      results.add(result);
    }
    
    return results;
  }

  /// 预处理图片以提高背景移除效果
  /// 
  /// [imagePath] 输入图片路径
  /// [maxSize] 最大尺寸限制
  /// [quality] 图片质量 (0.0-1.0)
  /// 返回预处理后的图片路径
  static Future<String?> preprocessImage(
    String imagePath, {
    int maxSize = 1024,
    double quality = 0.9,
  }) async {
    try {
      final result = await _channel.invokeMethod('preprocessImage', {
        'imagePath': imagePath,
        'maxSize': maxSize,
        'quality': quality,
      });

      return result as String?;
    } catch (e) {
      debugPrint('❌ 图片预处理失败: $e');
      return null;
    }
  }
}
