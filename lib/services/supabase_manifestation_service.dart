import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/manifestation_goal.dart';

/// Supabase service for manifestation goals with real-time sync
class SupabaseManifestationService {
  static const String _tableName = 'manifestation_goals';
  
  final SupabaseClient _supabase = Supabase.instance.client;
  StreamSubscription<List<Map<String, dynamic>>>? _realtimeSubscription;
  
  /// Stream controller for real-time updates
  final StreamController<List<ManifestationGoal>> _goalsStreamController = 
      StreamController<List<ManifestationGoal>>.broadcast();
  
  /// Stream of manifestation goals with real-time updates
  Stream<List<ManifestationGoal>> get goalsStream => _goalsStreamController.stream;
  
  /// Initialize real-time subscription
  Future<void> initializeRealtimeSync() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        debugPrint('❌ No authenticated user for real-time sync');
        return;
      }

      debugPrint('🔄 初始化实时订阅，用户ID: $userId');

      // Subscribe to real-time changes
      _realtimeSubscription = _supabase
          .from(_tableName)
          .stream(primaryKey: ['id'])
          .eq('user_id', userId)
          .listen(
            (data) {
              debugPrint('📡 收到实时数据: ${data.length} 条记录');

              // 打印原始数据用于调试
              for (var item in data) {
                debugPrint('📡 数据项: ${item['title']} - 状态: ${item['status']} - 更新时间: ${item['updated_at']}');
              }

              final goals = data
                  .where((json) => json['deleted_at'] == null)
                  .map((json) {
                    try {
                      return ManifestationGoal.fromSupabaseJson(json);
                    } catch (e) {
                      debugPrint('❌ 解析目标数据失败: $e, 数据: $json');
                      return null;
                    }
                  })
                  .where((goal) => goal != null)
                  .cast<ManifestationGoal>()
                  .toList();

              // Sort by creation date (newest first)
              goals.sort((a, b) => b.createdAt.compareTo(a.createdAt));

              debugPrint('📡 解析后的目标数量: ${goals.length}');

              // 打印每个目标的状态用于调试
              for (final goal in goals) {
                debugPrint('📡 目标: ${goal.title} - 状态: ${goal.status.name}');
              }

              _goalsStreamController.add(goals);
            },
            onError: (error) {
              debugPrint('❌ Real-time subscription error: $error');
            },
          );

      debugPrint('✅ Real-time sync initialized for user: $userId');

      // 立即获取一次数据以确保初始状态正确
      await _fetchAndEmitInitialData(userId);
    } catch (e) {
      debugPrint('❌ Failed to initialize real-time sync: $e');
    }
  }

  /// 获取并发送初始数据
  Future<void> _fetchAndEmitInitialData(String userId) async {
    try {
      debugPrint('🔄 获取初始数据...');
      final goals = await fetchGoals();
      debugPrint('📡 发送初始数据: ${goals.length} 个目标');
      _goalsStreamController.add(goals);
    } catch (e) {
      debugPrint('❌ 获取初始数据失败: $e');
    }
  }

  /// Fetch all manifestation goals for current user
  Future<List<ManifestationGoal>> fetchGoals() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _supabase
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .isFilter('deleted_at', null)
          .order('created_at', ascending: false);

      final goals = (response as List<dynamic>)
          .map((json) => ManifestationGoal.fromSupabaseJson(json))
          .toList();

      debugPrint('✅ Fetched ${goals.length} goals from Supabase');
      return goals;
    } catch (e) {
      debugPrint('❌ Failed to fetch goals: $e');
      rethrow;
    }
  }

  /// Create a new manifestation goal
  Future<ManifestationGoal> createGoal({
    required String title,
    String? description,
    String? localId,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final now = DateTime.now();
      final goalData = {
        'user_id': userId,
        'title': title,
        'description': description,
        'status': ManifestationStatus.pending.name,
        'is_affirmation_generated': false,
        'local_id': localId,
        'sync_status': SyncStatus.synced.name,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
        'last_synced_at': now.toIso8601String(),
      };

      final response = await _supabase
          .from(_tableName)
          .insert(goalData)
          .select()
          .single();

      final goal = ManifestationGoal.fromSupabaseJson(response);
      debugPrint('✅ Created goal: ${goal.title}');
      return goal;
    } catch (e) {
      debugPrint('❌ Failed to create goal: $e');
      rethrow;
    }
  }

  /// Update an existing manifestation goal
  Future<ManifestationGoal> updateGoal({
    required String goalId,
    String? title,
    String? description,
    ManifestationStatus? status,
    String? affirmation,
    bool? isAffirmationGenerated,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final now = DateTime.now();
      final updateData = <String, dynamic>{
        'updated_at': now.toIso8601String(),
        'last_synced_at': now.toIso8601String(),
        'sync_status': SyncStatus.synced.name,
      };

      if (title != null) updateData['title'] = title;
      if (description != null) updateData['description'] = description;
      if (status != null) updateData['status'] = status.name;
      if (affirmation != null) updateData['affirmation'] = affirmation;
      if (isAffirmationGenerated != null) {
        updateData['is_affirmation_generated'] = isAffirmationGenerated;
      }

      final response = await _supabase
          .from(_tableName)
          .update(updateData)
          .eq('id', goalId)
          .eq('user_id', userId)
          .select()
          .single();

      final goal = ManifestationGoal.fromSupabaseJson(response);
      debugPrint('✅ Updated goal: ${goal.title}');
      return goal;
    } catch (e) {
      debugPrint('❌ Failed to update goal: $e');
      rethrow;
    }
  }

  /// Soft delete a manifestation goal
  Future<bool> deleteGoal(String goalId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      await _supabase.rpc('soft_delete_manifestation_goal', 
          params: {'goal_id': goalId});

      debugPrint('✅ Deleted goal: $goalId');
      return true;
    } catch (e) {
      debugPrint('❌ Failed to delete goal: $e');
      return false;
    }
  }

  /// Batch upload local goals to Supabase (for migration)
  Future<List<ManifestationGoal>> batchUploadGoals(
      List<ManifestationGoal> localGoals) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uploadData = localGoals.map((goal) {
        final data = goal.toSupabaseJson();
        data['user_id'] = userId;
        data['local_id'] = goal.id; // Store original local ID
        data['sync_status'] = SyncStatus.synced.name;
        data['last_synced_at'] = DateTime.now().toIso8601String();
        return data;
      }).toList();

      final response = await _supabase
          .from(_tableName)
          .insert(uploadData)
          .select();

      final uploadedGoals = (response as List<dynamic>)
          .map((json) => ManifestationGoal.fromSupabaseJson(json))
          .toList();

      debugPrint('✅ Batch uploaded ${uploadedGoals.length} goals');
      return uploadedGoals;
    } catch (e) {
      debugPrint('❌ Failed to batch upload goals: $e');
      rethrow;
    }
  }

  /// Check for sync conflicts and resolve them
  Future<List<ManifestationGoal>> resolveSyncConflicts() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Fetch goals with conflicts
      final response = await _supabase
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .eq('sync_status', SyncStatus.conflict.name)
          .isFilter('deleted_at', null);

      final conflictGoals = (response as List<dynamic>)
          .map((json) => ManifestationGoal.fromSupabaseJson(json))
          .toList();

      // For now, resolve conflicts by marking them as synced
      // In a more sophisticated implementation, you might want to
      // present conflict resolution options to the user
      for (final goal in conflictGoals) {
        await updateGoal(goalId: goal.id);
      }

      debugPrint('✅ Resolved ${conflictGoals.length} sync conflicts');
      return conflictGoals;
    } catch (e) {
      debugPrint('❌ Failed to resolve sync conflicts: $e');
      rethrow;
    }
  }

  /// Get sync statistics
  Future<Map<String, int>> getSyncStats() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        return {'total': 0, 'synced': 0, 'pending': 0, 'conflicts': 0};
      }

      final response = await _supabase
          .from(_tableName)
          .select('sync_status')
          .eq('user_id', userId)
          .isFilter('deleted_at', null);

      final stats = <String, int>{
        'total': 0,
        'synced': 0,
        'pending': 0,
        'conflicts': 0,
      };

      for (final row in response as List<dynamic>) {
        final syncStatus = row['sync_status'] as String;
        stats['total'] = (stats['total'] ?? 0) + 1;
        stats[syncStatus] = (stats[syncStatus] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      debugPrint('❌ Failed to get sync stats: $e');
      return {'total': 0, 'synced': 0, 'pending': 0, 'conflicts': 0};
    }
  }

  /// Dispose resources
  void dispose() {
    _realtimeSubscription?.cancel();
    _goalsStreamController.close();
  }
}
