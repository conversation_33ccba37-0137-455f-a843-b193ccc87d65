import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

class ChatSummary {
  final String id;
  final String userId;
  final String content; // 摘要内容
  final String? chatSessionId;
  final String? fullConversation;
  final DateTime createdAt;

  ChatSummary({
    required this.id,
    required this.userId,
    required this.content,
    this.chatSessionId,
    this.fullConversation,
    required this.createdAt,
  });

  factory ChatSummary.fromJson(Map<String, dynamic> json) {
    return ChatSummary(
      id: json['id'],
      userId: json['user_id'],
      content: json['content'],
      chatSessionId: json['chat_session_id'],
      fullConversation: json['full_conversation'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

class ChatSummaryService {
  static final _supabase = Supabase.instance.client;

  /// 获取指定日期的聊天摘要
  static Future<List<ChatSummary>> getChatSummariesByDate({
    required String userId,
    required DateTime date,
  }) async {
    try {
      debugPrint('📅 查询日期聊天摘要: ${date.toString().substring(0, 10)}');

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final response = await _supabase
          .from('higher_self_memories')
          .select()
          .eq('user_id', userId)
          .eq('memory_type', 'chat_summary')
          .gte('created_at', startOfDay.toIso8601String())
          .lt('created_at', endOfDay.toIso8601String())
          .order('created_at', ascending: false);

      final summaries = (response as List)
          .map((json) => ChatSummary.fromJson(json))
          .toList();

      debugPrint('💬 找到 ${summaries.length} 条聊天摘要记录');
      return summaries;
    } catch (e) {
      debugPrint('❌ 查询日期聊天摘要失败: $e');
      return [];
    }
  }

  /// 获取最近的聊天摘要
  static Future<List<ChatSummary>> getRecentChatSummaries({
    required String userId,
    int limit = 10,
  }) async {
    try {
      final response = await _supabase
          .from('higher_self_memories')
          .select()
          .eq('user_id', userId)
          .eq('memory_type', 'chat_summary')
          .order('created_at', ascending: false)
          .limit(limit);

      return (response as List)
          .map((json) => ChatSummary.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ 获取最近聊天摘要失败: $e');
      return [];
    }
  }

  /// 根据会话ID获取聊天摘要
  static Future<ChatSummary?> getChatSummaryBySessionId({
    required String userId,
    required String sessionId,
  }) async {
    try {
      final response = await _supabase
          .from('higher_self_memories')
          .select()
          .eq('user_id', userId)
          .eq('memory_type', 'chat_summary')
          .eq('chat_session_id', sessionId)
          .single();

      return ChatSummary.fromJson(response);
    } catch (e) {
      debugPrint('❌ 根据会话ID获取聊天摘要失败: $e');
      return null;
    }
  }
}
