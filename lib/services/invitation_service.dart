import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 邀请码服务
class InvitationService extends ChangeNotifier {
  static const String _myInvitationCodeKey = 'my_invitation_code';

  final SupabaseClient _supabase = Supabase.instance.client;

  String? _myInvitationCode;
  String? get myInvitationCode => _myInvitationCode;

  /// 初始化邀请码服务
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // 移除全局的已使用标志，改为检查具体邀请码
      _myInvitationCode = prefs.getString(_myInvitationCodeKey);

      // 如果用户已登录但没有邀请码，生成一个
      if (_supabase.auth.currentUser != null && _myInvitationCode == null) {
        await _generateMyInvitationCode();
      }

      debugPrint('🚀 邀请码服务初始化完成');
      debugPrint('📝 我的邀请码: $_myInvitationCode');

      notifyListeners();
    } catch (e) {
      debugPrint('❌ 邀请码服务初始化失败: $e');
    }
  }

  /// 验证并使用邀请码
  Future<InvitationResult> useInvitationCode(String code, {String? language}) async {
    try {
      debugPrint('🔍 开始验证邀请码: ${code.toUpperCase()}');

      // 检查这个特定的邀请码是否已经使用过
      if (await _hasUsedSpecificInvitationCode(code.toUpperCase())) {
        return InvitationResult(
          success: false,
          message: _getLocalizedErrorMessage('already_used', language),
          rewardType: null,
          rewardDays: null,
        );
      }

      // 验证邀请码格式
      if (!_isValidInvitationCode(code)) {
        return InvitationResult(
          success: false,
          message: _getLocalizedErrorMessage('invalid_format', language),
          rewardType: null,
          rewardDays: null,
        );
      }

      final user = _supabase.auth.currentUser;
      if (user == null) {
        return InvitationResult(
          success: false,
          message: _getLocalizedErrorMessage('login_required', language),
          rewardType: null,
          rewardDays: null,
        );
      }

      // 调用Edge Function验证邀请码
      try {
        debugPrint('🚀 ===== 邀请码验证开始 =====');
        debugPrint('🔍 验证邀请码: ${code.toUpperCase()}');
        debugPrint('👤 用户ID: ${user.id}');
        debugPrint('📧 用户邮箱: ${user.email}');

        final requestBody = {
          'p_invitation_code': code.toUpperCase(),
          'p_user_id': user.id,
          'p_ip_address': null,
          'p_user_agent': 'Flutter App',
          'p_language': language ?? 'zh-CN',
        };

        debugPrint('📤 请求体: $requestBody');
        debugPrint('🌐 调用 Edge Function: use-invitation-code');

        final response = await _supabase.functions.invoke('use-invitation-code', body: requestBody);

        debugPrint('📡 Edge Function 原始响应:');
        debugPrint('  - Status: ${response.status}');
        debugPrint('  - Data: ${response.data}');
        debugPrint('  - Data Type: ${response.data.runtimeType}');

        if (response.status >= 400) {
          debugPrint('❌ Edge Function HTTP 错误: ${response.status}');
          throw Exception('Edge Function HTTP 错误: ${response.status}');
        }

        if (response.data != null) {
          final result = response.data as Map<String, dynamic>;
          debugPrint('✅ 解析响应数据: $result');

          if (result['success'] == true) {
            debugPrint('🎉 邀请码验证成功！');
            debugPrint('🎁 奖励类型: ${result['reward_type']}');
            debugPrint('📝 描述: ${result['description']}');

            // 记录成功使用的邀请码（但不设置全局限制）
            final prefs = await SharedPreferences.getInstance();
            final usedCodes = prefs.getStringList('used_invitation_codes') ?? [];
            if (!usedCodes.contains(code.toUpperCase())) {
              usedCodes.add(code.toUpperCase());
              await prefs.setStringList('used_invitation_codes', usedCodes);
            }
            debugPrint('📝 已记录使用的邀请码: $usedCodes');

            // 激活会员权益
            await _activateInvitationReward(result['reward_type'], result['reward_value'] ?? 7);

            // 重新获取个人邀请码
            await _generateMyInvitationCode();

            notifyListeners();

            debugPrint('✅ 本地状态更新完成');

            return InvitationResult(
              success: true,
              message: result['message'] ?? '邀请码使用成功！',
              rewardType: result['reward_type'],
              rewardDays: result['reward_value'] ?? 7,
            );
          } else {
            debugPrint('❌ 邀请码验证失败');
            debugPrint('📝 错误消息: ${result['message']}');
            debugPrint('🔍 错误代码: ${result['error_code']}');
            debugPrint('🐛 调试信息: ${result['debug']}');

            return InvitationResult(
              success: false,
              message: result['message'] ?? '邀请码使用失败',
              rewardType: null,
              rewardDays: null,
            );
          }
        } else {
          debugPrint('❌ Edge Function 返回空响应');
          throw Exception('Edge Function 返回空响应');
        }

      } catch (dbError) {
        debugPrint('⚠️ 数据库操作失败，使用本地验证: $dbError');

        // 数据库操作失败，使用本地预设邀请码验证
        final localResult = await _validateLocalInvitationCode(code.toUpperCase());
        if (localResult != null) {
          // 记录成功使用的邀请码（但不设置全局限制）
          final prefs = await SharedPreferences.getInstance();
          final usedCodes = prefs.getStringList('used_invitation_codes') ?? [];
          if (!usedCodes.contains(code.toUpperCase())) {
            usedCodes.add(code.toUpperCase());
            await prefs.setStringList('used_invitation_codes', usedCodes);
          }
          debugPrint('📝 已记录使用的邀请码: $usedCodes');

          // 激活会员权益
          await _activateInvitationReward(localResult['reward'], 7);

          // 重新获取个人邀请码
          await _generateMyInvitationCode();

          notifyListeners();

          return InvitationResult(
            success: true,
            message: '恭喜！您已成功获得${localResult['description']}',
            rewardType: localResult['reward'],
            rewardDays: localResult['reward_value'] ?? 7,
          );
        } else {
          return InvitationResult(
            success: false,
            message: '邀请码不存在或已失效',
            rewardType: null,
            rewardDays: null,
          );
        }
      }
    } catch (e) {
      debugPrint('❌ 使用邀请码失败: $e');
      return InvitationResult(
        success: false,
        message: '使用邀请码时发生错误，请稍后重试',
        rewardType: null,
        rewardDays: null,
      );
    }
  }

  /// 生成我的邀请码
  Future<void> _generateMyInvitationCode() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return;

      // 尝试从数据库获取用户邀请统计（如果表存在）
      String code;
      try {
        final response = await _supabase
            .from('user_invitation_stats')
            .select('personal_invitation_code')
            .eq('user_id', user.id)
            .maybeSingle();

        if (response != null) {
          // 用户已有邀请码
          code = response['personal_invitation_code'];
        } else {
          // 创建新的邀请统计记录
          final newCode = _generateCodeFromUserId(user.id);
          await _supabase.from('user_invitation_stats').insert({
            'user_id': user.id,
            'personal_invitation_code': newCode,
          });
          code = newCode;
        }
      } catch (e) {
        debugPrint('⚠️ user_invitation_stats表不存在，使用本地生成: $e');
        // 如果表不存在，直接生成本地邀请码
        code = _generateCodeFromUserId(user.id);
      }

      _myInvitationCode = code;

      // 保存到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_myInvitationCodeKey, code);

      notifyListeners();
    } catch (e) {
      debugPrint('❌ 生成邀请码失败: $e');
      // 如果数据库操作失败，使用本地生成的方式
      final user = _supabase.auth.currentUser;
      if (user != null) {
        final code = _generateCodeFromUserId(user.id);
        _myInvitationCode = code;

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_myInvitationCodeKey, code);

        notifyListeners();
      }
    }
  }

  /// 从用户ID生成邀请码
  String _generateCodeFromUserId(String userId) {
    // 使用用户ID的哈希值生成6位字母数字组合
    final hash = userId.hashCode.abs();
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    String code = '';

    int temp = hash;
    for (int i = 0; i < 6; i++) {
      code += chars[temp % chars.length];
      temp ~/= chars.length;
    }

    return code;
  }

  /// 验证邀请码格式
  bool _isValidInvitationCode(String code) {
    // 6位字母数字组合
    final regex = RegExp(r'^[A-Z0-9]{6}$');
    return regex.hasMatch(code.toUpperCase());
  }

  /// 本地邀请码验证（作为数据库验证的备用方案）
  Future<Map<String, dynamic>?> _validateLocalInvitationCode(String code) async {
    // 预设的有效邀请码
    final validCodes = {
      'HELLO1': {
        'type': 'new_user',
        'reward': 'weekly_membership',
        'description': '新用户专享周会员',
      },
      'LUCK88': {
        'type': 'activity',
        'reward': 'weekly_membership',
        'description': '幸运活动周会员',
      },
      'VIP024': {
        'type': 'special',
        'reward': 'weekly_membership',
        'description': '2024特别版周会员',
      },
      'SPRING': {
        'type': 'seasonal',
        'reward': 'weekly_membership',
        'description': '春季限定周会员',
      },
      'FRIEND': {
        'type': 'referral',
        'reward': 'weekly_membership',
        'description': '好友推荐周会员',
      },
      'BETA01': {
        'type': 'beta_test',
        'reward': 'weekly_membership',
        'description': '内测用户专享',
      },
      'GIFT99': {
        'type': 'gift',
        'reward': 'weekly_membership',
        'description': '礼品码周会员',
      },
      'TEST01': {
        'type': 'test',
        'reward': 'weekly_membership',
        'description': '测试专用邀请码',
      },
    };

    debugPrint('🔍 验证本地邀请码: $code');
    debugPrint('📋 可用邀请码: ${validCodes.keys.join(', ')}');

    return validCodes[code];
  }

  /// 检查特定邀请码是否已经使用过
  Future<bool> _hasUsedSpecificInvitationCode(String code) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usedCodes = prefs.getStringList('used_invitation_codes') ?? [];
      final hasUsed = usedCodes.contains(code.toUpperCase());
      debugPrint('🔍 检查邀请码 $code 是否已使用: $hasUsed');
      debugPrint('📝 已使用的邀请码列表: $usedCodes');
      return hasUsed;
    } catch (e) {
      debugPrint('❌ 检查邀请码使用状态失败: $e');
      return false;
    }
  }

  /// 激活邀请码奖励
  Future<void> _activateInvitationReward(String rewardType, int rewardValue) async {
    try {
      debugPrint('🎁 开始激活邀请码奖励: $rewardType, 天数: $rewardValue');

      final prefs = await SharedPreferences.getInstance();
      final user = _supabase.auth.currentUser;

      if (user == null) {
        debugPrint('❌ 用户未登录，无法激活奖励');
        return;
      }

      // 🔧 修复：使用与订阅服务一致的键名格式
      final userSpecificKey = 'subscription_tier_${user.id}';
      final userSpecificPeriodKey = 'subscription_period_${user.id}';
      final userSpecificExpiryKey = 'subscription_expiry_${user.id}';

      debugPrint('🔑 使用键名: $userSpecificKey');
      debugPrint('🔑 使用键名: $userSpecificPeriodKey');
      debugPrint('🔑 使用键名: $userSpecificExpiryKey');

      if (rewardType == 'weekly_membership') {
        // 激活基础会员权益（7天）
        final expiryTime = DateTime.now().add(Duration(days: rewardValue));

        await prefs.setString(userSpecificKey, 'basic');
        await prefs.setString(userSpecificPeriodKey, 'weekly');
        await prefs.setString(userSpecificExpiryKey, expiryTime.toIso8601String());

        debugPrint('✅ 基础会员权益已激活，到期时间: $expiryTime');

      } else if (rewardType == 'monthly_membership') {
        // 激活基础会员权益（30天）
        final expiryTime = DateTime.now().add(Duration(days: rewardValue));

        await prefs.setString(userSpecificKey, 'basic');
        await prefs.setString(userSpecificPeriodKey, 'monthly');
        await prefs.setString(userSpecificExpiryKey, expiryTime.toIso8601String());

        debugPrint('✅ 基础会员权益已激活（月度），到期时间: $expiryTime');

      } else if (rewardType == 'premium_membership') {
        // 激活高级会员权益
        final expiryTime = DateTime.now().add(Duration(days: rewardValue));

        await prefs.setString(userSpecificKey, 'premium');
        await prefs.setString(userSpecificPeriodKey, 'weekly');
        await prefs.setString(userSpecificExpiryKey, expiryTime.toIso8601String());

        debugPrint('✅ 高级会员权益已激活，到期时间: $expiryTime');

      } else {
        debugPrint('⚠️ 未知的奖励类型: $rewardType，默认激活基础会员');
        // 默认激活基础会员
        final expiryTime = DateTime.now().add(Duration(days: rewardValue));

        await prefs.setString(userSpecificKey, 'basic');
        await prefs.setString(userSpecificPeriodKey, 'weekly');
        await prefs.setString(userSpecificExpiryKey, expiryTime.toIso8601String());
      }

      debugPrint('💾 会员状态已保存到本地存储');

      // 🔧 修复：重置使用次数，使用与订阅服务一致的键名格式
      final dailyUsageKey = 'daily_usage_count_${user.id}';
      final weeklyUsageKey = 'weekly_usage_count_${user.id}';
      await prefs.setInt(dailyUsageKey, 0);
      await prefs.setInt(weeklyUsageKey, 0);

      debugPrint('🔄 使用次数已重置，用户可立即享受会员权益');

      // 创建后端订阅记录
      await _createBackendSubscription(user.id, rewardType, rewardValue);

      // 更新用户邀请统计到后端
      await _updateUserInvitationStats(rewardValue);

      debugPrint('🎉 邀请码奖励激活完成！');

    } catch (e) {
      debugPrint('❌ 激活邀请码奖励失败: $e');
    }
  }





  /// 获取推荐统计
  Future<InvitationStats> getInvitationStats() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return InvitationStats(
          totalInvitations: 0,
          successfulInvitations: 0,
          totalRewardDays: 0,
        );
      }

      // 从数据库获取用户邀请统计
      final response = await _supabase
          .from('user_invitation_stats')
          .select('successful_invitations, total_reward_days, personal_invitation_code, has_used_invitation_code')
          .eq('user_id', user.id)
          .maybeSingle();

      debugPrint('📊 获取邀请统计数据: $response');

      if (response != null) {
        return InvitationStats(
          totalInvitations: 1, // 如果有记录说明至少使用过一次邀请码
          successfulInvitations: response['successful_invitations'] ?? 0,
          totalRewardDays: response['total_reward_days'] ?? 0,
        );
      } else {
        return InvitationStats(
          totalInvitations: 0,
          successfulInvitations: 0,
          totalRewardDays: 0,
        );
      }
    } catch (e) {
      debugPrint('❌ 获取邀请统计失败: $e');
      return InvitationStats(
        totalInvitations: 0,
        successfulInvitations: 0,
        totalRewardDays: 0,
      );
    }
  }

  /// 创建后端订阅记录
  Future<void> _createBackendSubscription(String userId, String rewardType, int rewardValue) async {
    try {
      debugPrint('📊 开始创建后端订阅记录');
      debugPrint('👤 用户ID: $userId');
      debugPrint('🎁 奖励类型: $rewardType');
      debugPrint('📅 奖励天数: $rewardValue');

      // 根据奖励类型确定订阅计划
      String planName;
      switch (rewardType) {
        case 'weekly_membership':
          planName = 'basic_weekly';
          break;
        case 'monthly_membership':
          planName = 'basic_monthly';
          break;
        case 'premium_membership':
          planName = 'premium_weekly';
          break;
        default:
          planName = 'basic_weekly';
      }

      // 获取订阅计划ID
      final planResponse = await _supabase
          .from('subscription_plans')
          .select('id')
          .eq('name', planName)
          .maybeSingle();

      if (planResponse == null) {
        debugPrint('❌ 找不到订阅计划: $planName');
        return;
      }

      final planId = planResponse['id'];
      final now = DateTime.now();
      final endDate = now.add(Duration(days: rewardValue));

      debugPrint('📋 订阅计划ID: $planId');
      debugPrint('⏰ 开始时间: ${now.toIso8601String()}');
      debugPrint('⏰ 结束时间: ${endDate.toIso8601String()}');

      // 先取消现有的活跃订阅
      await _supabase
          .from('user_subscriptions')
          .update({
            'status': 'cancelled',
            'cancelled_at': now.toIso8601String(),
            'updated_at': now.toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('status', 'active');

      debugPrint('🔄 已取消现有活跃订阅');

      // 创建新的订阅记录
      final subscriptionResponse = await _supabase
          .from('user_subscriptions')
          .insert({
            'user_id': userId,
            'plan_id': planId,
            'status': 'active',
            'start_date': now.toIso8601String(),
            'end_date': endDate.toIso8601String(),
            'payment_method': 'invitation_code',
            'payment_status': 'completed',
            'amount_paid': 0.00,
            'auto_renew': false,
          })
          .select()
          .single();

      debugPrint('✅ 后端订阅记录创建成功');
      debugPrint('📊 订阅记录: $subscriptionResponse');

    } catch (e) {
      debugPrint('❌ 创建后端订阅记录失败: $e');
    }
  }

  /// 更新用户邀请统计到后端
  Future<void> _updateUserInvitationStats(int rewardDays) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        debugPrint('❌ 用户未登录，无法更新统计');
        return;
      }

      debugPrint('📊 开始更新用户邀请统计到后端');
      debugPrint('👤 用户ID: ${user.id}');
      debugPrint('🎁 奖励天数: $rewardDays');

      // 先获取现有的统计数据
      final existingStats = await _supabase
          .from('user_invitation_stats')
          .select('total_reward_days')
          .eq('user_id', user.id)
          .maybeSingle();

      int currentRewardDays = 0;
      if (existingStats != null) {
        currentRewardDays = existingStats['total_reward_days'] ?? 0;
      }

      // 累加奖励天数
      final newTotalRewardDays = currentRewardDays + rewardDays;
      debugPrint('📊 当前奖励天数: $currentRewardDays, 新增: $rewardDays, 总计: $newTotalRewardDays');

      // 更新或插入用户邀请统计
      await _supabase
          .from('user_invitation_stats')
          .upsert({
            'user_id': user.id,
            'total_reward_days': newTotalRewardDays,
            'has_used_invitation_code': true,
            'updated_at': DateTime.now().toIso8601String(),
          }, onConflict: 'user_id');

      debugPrint('✅ 用户邀请统计更新成功，总奖励天数: $newTotalRewardDays');

      // 如果用户还没有个人邀请码，生成一个
      if (_myInvitationCode == null) {
        await _generateMyInvitationCode();
      }

    } catch (e) {
      debugPrint('❌ 更新用户邀请统计失败: $e');
    }
  }

  /// 获取本地化错误消息
  String _getLocalizedErrorMessage(String key, String? language) {
    final lang = language ?? 'zh-CN';
    final messages = {
      'already_used': {
        'zh-CN': '您已经使用过这个邀请码了',
        'zh-TW': '您已經使用過這個邀請碼了',
        'en-US': 'You have already used this invitation code',
        'es-ES': 'Ya has usado este código de invitación',
        'ja-JP': 'この招待コードは既に使用済みです',
        'ko-KR': '이미 이 초대 코드를 사용했습니다',
      },
      'invalid_format': {
        'zh-CN': '邀请码格式不正确',
        'zh-TW': '邀請碼格式不正確',
        'en-US': 'Invalid invitation code format',
        'es-ES': 'Formato de código de invitación inválido',
        'ja-JP': '招待コードの形式が正しくありません',
        'ko-KR': '초대 코드 형식이 올바르지 않습니다',
      },
      'login_required': {
        'zh-CN': '请先登录后再使用邀请码',
        'zh-TW': '請先登錄後再使用邀請碼',
        'en-US': 'Please login first to use invitation code',
        'es-ES': 'Por favor inicia sesión primero para usar el código de invitación',
        'ja-JP': '招待コードを使用するには、まずログインしてください',
        'ko-KR': '초대 코드를 사용하려면 먼저 로그인하세요',
      },
      'invalid_code': {
        'zh-CN': '邀请码不存在或已失效',
        'zh-TW': '邀請碼不存在或已失效',
        'en-US': 'Invitation code does not exist or has expired',
        'es-ES': 'El código de invitación no existe o ha expirado',
        'ja-JP': '招待コードが存在しないか期限切れです',
        'ko-KR': '초대 코드가 존재하지 않거나 만료되었습니다',
      },
      'network_error': {
        'zh-CN': '网络错误，请稍后重试',
        'zh-TW': '網絡錯誤，請稍後重試',
        'en-US': 'Network error, please try again later',
        'es-ES': 'Error de red, por favor inténtalo de nuevo más tarde',
        'ja-JP': 'ネットワークエラーです。後でもう一度お試しください',
        'ko-KR': '네트워크 오류입니다. 나중에 다시 시도하세요',
      },
    };

    return messages[key]?[lang] ?? messages[key]?['zh-CN'] ?? key;
  }
}

/// 邀请码使用结果
class InvitationResult {
  final bool success;
  final String message;
  final String? rewardType;
  final int? rewardDays;

  InvitationResult({
    required this.success,
    required this.message,
    required this.rewardType,
    this.rewardDays,
  });
}

/// 邀请统计
class InvitationStats {
  final int totalInvitations;
  final int successfulInvitations;
  final int totalRewardDays;

  InvitationStats({
    required this.totalInvitations,
    required this.successfulInvitations,
    required this.totalRewardDays,
  });
}
