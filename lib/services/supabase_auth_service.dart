import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:ai_tarot_reading/services/user_data_manager.dart';

/// Supabase认证服务
/// 处理用户注册、登录、注销等认证相关功能
class SupabaseAuthService extends ChangeNotifier {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  // 当前用户
  User? get currentUser => _supabase.auth.currentUser;
  bool get isSignedIn => currentUser != null;
  
  // 用户信息
  String? get userEmail => currentUser?.email;
  String? get userId => currentUser?.id;
  String? get userName => currentUser?.userMetadata?['full_name'] ?? currentUser?.email?.split('@').first;
  String? get userAvatar => currentUser?.userMetadata?['avatar_url'];
  
  /// 初始化认证服务
  Future<void> initialize() async {
    // 监听认证状态变化
    _supabase.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;
      
      print('🔐 认证状态变化: $event');
      if (session != null) {
        print('👤 用户: ${session.user.email}');
      }
      
      notifyListeners();
    });
  }
  
  /// 邮箱密码注册
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    String? fullName,
  }) async {
    try {
      // 验证邮箱格式
      if (!_isValidEmail(email)) {
        throw const AuthException('邮箱格式不正确，请输入有效的邮箱地址');
      }
      
      // 验证密码强度
      if (!_isValidPassword(password)) {
        throw const AuthException('密码必须至少6位，包含字母和数字');
      }
      
      final response = await _supabase.auth.signUp(
        email: email.trim().toLowerCase(), // 标准化邮箱格式
        password: password,
        data: {
          'full_name': fullName,
          'avatar_url': '',
        },
      );
      
      if (response.user != null) {
        print('✅ 注册成功: ${response.user!.email}');
        // 检查并清理其他用户的数据
        await UserDataManager.checkAndClearDataForNewUser(response.user!.id);
        await _createUserProfile(response.user!);
      }
      
      return response;
    } catch (e) {
      print('❌ 注册失败: $e');
      rethrow;
    }
  }
  
  /// 邮箱密码登录
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      // 验证邮箱格式
      if (!_isValidEmail(email)) {
        throw const AuthException('邮箱格式不正确，请输入有效的邮箱地址');
      }
      
      final response = await _supabase.auth.signInWithPassword(
        email: email.trim().toLowerCase(), // 标准化邮箱格式
        password: password,
      );
      
      if (response.user != null) {
        print('✅ 登录成功: ${response.user!.email}');
        // 检查并清理其他用户的数据
        await UserDataManager.checkAndClearDataForNewUser(response.user!.id);
      }
      
      return response;
    } catch (e) {
      print('❌ 登录失败: $e');
      rethrow;
    }
  }
  
  /// Apple ID登录（使用已获取的凭证）
  Future<AuthResponse> signInWithAppleCredential(AuthorizationCredentialAppleID credential) async {
    try {
      // 检查idToken是否存在
      final idToken = credential.identityToken;
      if (idToken == null) {
        throw const AuthException('Could not find ID Token from generated credential.');
      }

      // 构建用户全名
      String? fullName;
      if (credential.givenName != null && credential.familyName != null) {
        fullName = '${credential.givenName} ${credential.familyName}';
      }

      // 检查Apple ID Token是否包含nonce
      // 如果Apple的id_token已经包含nonce，我们就不需要传递额外的nonce
      final response = await _supabase.auth.signInWithIdToken(
        provider: OAuthProvider.apple,
        idToken: idToken,
        // 不传递nonce，让Supabase自动处理
      );
      
      if (response.user != null) {
        print('✅ Apple登录成功: ${response.user!.email}');
        
        // 检查并清理其他用户的数据
        await UserDataManager.checkAndClearDataForNewUser(response.user!.id);
        
        // 更新用户资料
        if (fullName != null) {
          await _updateUserProfile({
            'full_name': fullName,
          });
        }
      }
      
      return response;
    } catch (e) {
      print('❌ Apple登录失败: $e');
      rethrow;
    }
  }
  
  /// Apple ID登录
  Future<AuthResponse> signInWithApple() async {
    try {
      // 使用Supabase官方方法生成nonce
      final rawNonce = _supabase.auth.generateRawNonce();
      final hashedNonce = sha256.convert(utf8.encode(rawNonce)).toString();

      // 请求Apple ID凭证
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: hashedNonce,
      );

      // 使用已获取的凭证进行登录
      return await signInWithAppleCredential(credential);
    } catch (e) {
      print('❌ Apple登录失败: $e');
      rethrow;
    }
  }
  
  /// Google登录 (可选)
  Future<bool> signInWithGoogle() async {
    try {
      await _supabase.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'io.supabase.flutterquickstart://login-callback/',
      );

      return true;
    } catch (e) {
      print('❌ Google登录失败: $e');
      return false;
    }
  }
  
  /// 注销
  Future<void> signOut() async {
    try {
      // 清理本地用户数据
      await UserDataManager.clearDataOnSignOut();
      
      await _supabase.auth.signOut();
      print('✅ 注销成功');
    } catch (e) {
      print('❌ 注销失败: $e');
      rethrow;
    }
  }
  
  /// 重置密码
  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
      print('✅ 密码重置邮件已发送');
    } catch (e) {
      print('❌ 密码重置失败: $e');
      rethrow;
    }
  }
  
  /// 更新用户资料
  Future<UserResponse> updateProfile({
    String? fullName,
    String? avatarUrl,
  }) async {
    try {
      final updates = <String, dynamic>{};
      if (fullName != null) updates['full_name'] = fullName;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;
      
      final response = await _supabase.auth.updateUser(
        UserAttributes(data: updates),
      );
      
      if (response.user != null) {
        print('✅ 用户资料更新成功');
        await _updateUserProfile(updates);
      }
      
      return response;
    } catch (e) {
      print('❌ 用户资料更新失败: $e');
      rethrow;
    }
  }
  
  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      caseSensitive: false,
    );
    return emailRegex.hasMatch(email.trim());
  }
  
  /// 验证密码强度
  bool _isValidPassword(String password) {
    // 至少6位，包含字母和数字
    if (password.length < 6) return false;
    
    final hasLetter = RegExp(r'[a-zA-Z]').hasMatch(password);
    final hasNumber = RegExp(r'[0-9]').hasMatch(password);
    
    return hasLetter && hasNumber;
  }

  /// 删除账号
  Future<void> deleteAccount() async {
    try {
      if (currentUser == null) {
        throw Exception('用户未登录');
      }
      
      // 删除用户数据
      await _deleteUserData(currentUser!.id);
      
      // 注销用户
      await signOut();
      
      print('✅ 账号删除成功');
    } catch (e) {
      print('❌ 账号删除失败: $e');
      rethrow;
    }
  }
  
  /// 创建用户资料
  Future<void> _createUserProfile(User user) async {
    try {
      await _supabase.from('users').insert({
        'id': user.id,
        'email': user.email,
        'full_name': user.userMetadata?['full_name'],
        'avatar_url': user.userMetadata?['avatar_url'],
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      print('✅ 用户资料创建成功');
    } catch (e) {
      print('❌ 用户资料创建失败: $e');
    }
  }
  
  /// 更新用户资料
  Future<void> _updateUserProfile(Map<String, dynamic> updates) async {
    try {
      if (currentUser == null) return;
      
      updates['updated_at'] = DateTime.now().toIso8601String();
      
      await _supabase.from('users').update(updates).eq('id', currentUser!.id);
      
      print('✅ 用户资料数据库更新成功');
    } catch (e) {
      print('❌ 用户资料数据库更新失败: $e');
    }
  }
  
  /// 删除用户数据
  Future<void> _deleteUserData(String userId) async {
    try {
      // 删除用户相关的所有数据
      await Future.wait([
        _supabase.from('tarot_readings').delete().eq('user_id', userId),
        _supabase.from('daily_tarot').delete().eq('user_id', userId),
        _supabase.from('user_preferences').delete().eq('user_id', userId),
        _supabase.from('users').delete().eq('id', userId),
      ]);
      
      print('✅ 用户数据删除成功');
    } catch (e) {
      print('❌ 用户数据删除失败: $e');
    }
  }
  

  
  /// 获取用户显示名称
  String getDisplayName() {
    if (userName != null && userName!.isNotEmpty) {
      return userName!;
    } else if (userEmail != null) {
      return userEmail!.split('@').first;
    } else {
      return '用户';
    }
  }
  
  /// 获取登录状态文本
  String getStatusText() {
    return isSignedIn ? '已登录' : '未登录';
  }

  /// 生成随机nonce
  String generateNonce() {
    return _supabase.auth.generateRawNonce();
  }

  /// 哈希nonce
  String hashNonce(String nonce) {
    return sha256.convert(utf8.encode(nonce)).toString();
  }
}
