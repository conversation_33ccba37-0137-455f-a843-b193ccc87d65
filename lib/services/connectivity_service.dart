import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Network connectivity service for handling online/offline states
class ConnectivityService extends ChangeNotifier {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = true;
  bool _hasInternetAccess = true;
  
  bool get isOnline => _isOnline && _hasInternetAccess;
  bool get hasNetworkConnection => _isOnline;
  bool get hasInternetAccess => _hasInternetAccess;

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    // Check initial connectivity
    await _checkConnectivity();
    
    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        // 取第一个结果，或者检查是否有任何连接
        final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
        _handleConnectivityChange(result);
      },
    );
    
    // Periodically check internet access
    _startInternetAccessCheck();
  }

  /// Handle connectivity changes
  void _handleConnectivityChange(ConnectivityResult result) async {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;
    
    if (_isOnline) {
      // Check if we actually have internet access
      await _checkInternetAccess();
    } else {
      _hasInternetAccess = false;
    }
    
    if (wasOnline != isOnline) {
      debugPrint('🌐 Connectivity changed: ${isOnline ? 'Online' : 'Offline'}');
      notifyListeners();
    }
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      // 处理返回List<ConnectivityResult>的情况
      final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      _isOnline = result != ConnectivityResult.none;
      
      if (_isOnline) {
        await _checkInternetAccess();
      } else {
        _hasInternetAccess = false;
      }
    } catch (e) {
      debugPrint('❌ Failed to check connectivity: $e');
      _isOnline = false;
      _hasInternetAccess = false;
    }
  }

  /// Check if device has actual internet access
  Future<void> _checkInternetAccess() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      _hasInternetAccess = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      _hasInternetAccess = false;
    }
  }

  /// Start periodic internet access check
  void _startInternetAccessCheck() {
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (_isOnline) {
        final hadAccess = _hasInternetAccess;
        await _checkInternetAccess();
        
        if (hadAccess != _hasInternetAccess) {
          debugPrint('🌐 Internet access changed: ${_hasInternetAccess ? 'Available' : 'Unavailable'}');
          notifyListeners();
        }
      }
    });
  }

  /// Manually refresh connectivity status
  Future<void> refresh() async {
    await _checkConnectivity();
    notifyListeners();
  }

  /// Test connection to a specific host
  Future<bool> testConnection(String host, {int port = 443}) async {
    try {
      final socket = await Socket.connect(host, port, timeout: const Duration(seconds: 5));
      socket.destroy();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Test Supabase connectivity specifically
  Future<bool> testSupabaseConnection() async {
    try {
      // Replace with your actual Supabase URL
      return await testConnection('your-project.supabase.co');
    } catch (e) {
      return false;
    }
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
