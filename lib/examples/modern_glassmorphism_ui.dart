import 'package:flutter/material.dart';
import 'dart:ui';

/// 🎨 现代玻璃拟态UI示例
/// 学习自您展示的精美UI设计
class ModernGlassmorphismUI extends StatefulWidget {
  const ModernGlassmorphismUI({super.key});

  @override
  State<ModernGlassmorphismUI> createState() => _ModernGlassmorphismUIState();
}

class _ModernGlassmorphismUIState extends State<ModernGlassmorphismUI> {
  String selectedReading = 'Single Card';
  String selectedTheme = 'Classic';
  String question = '';
  
  final List<Map<String, dynamic>> spreadTypes = [
    {
      'name': 'Single Card',
      'description': 'Quick insight for immediate guidance',
      'cards': 1,
      'price': '0.05',
      'icon': Icons.filter_1,
    },
    {
      'name': 'Three Card',
      'description': 'Past, Present, Future reading',
      'cards': 3,
      'price': '0.15',
      'icon': Icons.filter_3,
    },
    {
      'name': 'Celtic Cross',
      'description': 'Comprehensive life analysis',
      'cards': 10,
      'price': '0.35',
      'icon': Icons.add,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFAFBFC), // 透明度较高的白色
              Color(0xFFFFFFFF), // 纯白色
              Color(0xFFF8F9FA), // 极浅灰白
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 🔙 现代化返回按钮
                _buildModernBackButton(),
                
                const SizedBox(height: 24),
                
                // 📱 双栏布局（参考原UI设计）
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 左侧：配置面板
                    Expanded(
                      flex: 1,
                      child: _buildConfigurationPanel(),
                    ),
                    
                    const SizedBox(width: 20),
                    
                    // 右侧：预览面板
                    Expanded(
                      flex: 1,
                      child: _buildPreviewPanel(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 🔙 玻璃拟态返回按钮（学习原UI设计）
  Widget _buildModernBackButton() {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          // 玻璃拟态效果
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.arrow_back_ios,
              color: Color(0xFF666666),
              size: 16,
            ),
            SizedBox(width: 4),
            Text(
              'CREATE',
              style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 14,
                fontWeight: FontWeight.w600,
                letterSpacing: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// ⚙️ 左侧配置面板（学习原UI的结构布局）
  Widget _buildConfigurationPanel() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        // 透明度较低的白色毛玻璃效果
        color: Colors.white.withOpacity(0.6),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withOpacity(0.4),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 40,
            offset: const Offset(0, 12),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主标题渐变按钮
          _buildGradientButton(
            text: 'Tarot Reading',
            isSelected: true,
            width: double.infinity,
          ),
          
          const SizedBox(height: 32),
          
          // 问题输入区域
          _buildSectionTitle('Minimum bid'),
          const SizedBox(height: 12),
          _buildMinimumBidSection(),
          
          const SizedBox(height: 32),
          
          // 牌阵选择
          _buildSectionTitle('Starting Date'),
          const SizedBox(height: 12),
          _buildDropdownSelector('Right after listing', [
            'Right after listing',
            'Tomorrow',
            'Next week',
            'Custom date'
          ]),
          
          const SizedBox(height: 32),
          
          // 到期时间
          _buildSectionTitle('Expiration Date'),
          const SizedBox(height: 12),
          _buildDropdownSelector('1 Day', [
            '1 Day',
            '3 Days',
            '1 Week',
            '1 Month'
          ]),
          
          const SizedBox(height: 40),
          
          // 创建按钮
          _buildGradientButton(
            text: 'LIST NFT',
            isSelected: true,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  /// 🎴 右侧预览面板（学习原UI的卡片设计）
  Widget _buildPreviewPanel() {
    return Column(
      children: [
        // 上方大卡片
        _buildPreviewCard(
          title: 'At Home',
          subtitle: 'Floor price 0.50 ETH',
          isLarge: true,
        ),
        
        const SizedBox(height: 20),
        
        // 下方小卡片
        _buildPreviewCard(
          title: 'Daily Guidance',
          subtitle: 'Floor price 0.15 ETH',
          isLarge: false,
        ),
      ],
    );
  }

  /// 🃏 预览卡片（完全模仿原UI的卡片设计）
  Widget _buildPreviewCard({
    required String title,
    required String subtitle,
    required bool isLarge,
  }) {
    return Container(
      height: isLarge ? 300 : 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 25,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // 透明度较低的白色毛玻璃背景
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isLarge
                        ? [
                            Colors.white.withOpacity(0.6),
                            Colors.white.withOpacity(0.5),
                            Colors.white.withOpacity(0.55),
                          ]
                        : [
                            Colors.white.withOpacity(0.65),
                            Colors.white.withOpacity(0.5),
                          ],
                  ),
                ),
              ),
            ),
            
            // 毛玻璃模糊效果
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
            
            // 玻璃边框
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.8),
                    width: 1,
                  ),
                ),
              ),
            ),
            
            // 内容层
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 顶部工具栏（模仿原UI）
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(0xFFE9ECEF),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Icons.auto_awesome,
                          color: Color(0xFF6C5CE7),
                          size: 18,
                        ),
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.favorite_border,
                        color: Color(0xFF666666),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.more_horiz,
                        color: Color(0xFF666666),
                        size: 20,
                      ),
                    ],
                  ),
                  
                  const Spacer(),
                  
                  // 底部信息（模仿原UI文本样式）
                  Text(
                    title,
                    style: const TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Color(0xFF6C757D),
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 📝 区块标题样式
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Color(0xFF333333),
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// 💰 最小出价区域（模仿原UI的ETH输入框）
  Widget _buildMinimumBidSection() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE9ECEF),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // ETH图标和价格
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFE9ECEF),
                width: 1,
              ),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.currency_bitcoin,
                  color: Color(0xFF333333),
                  size: 16,
                ),
                SizedBox(width: 4),
                Text(
                  '0.00',
                  style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          // 说明文字
          const Expanded(
            child: Text(
              'Service fee 2.5%. You will receive 2.5% if sold.',
              style: TextStyle(
                color: Color(0xFF6C757D),
                fontSize: 11,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 下拉选择器（模仿原UI样式）
  Widget _buildDropdownSelector(String value, List<String> options) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE9ECEF),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Color(0xFF333333),
                fontSize: 14,
              ),
            ),
          ),
          const Icon(
            Icons.keyboard_arrow_down,
            color: Color(0xFF6C757D),
            size: 20,
          ),
        ],
      ),
    );
  }

  /// 🌈 渐变按钮（核心设计元素）
  Widget _buildGradientButton({
    required String text,
    required bool isSelected,
    double? width,
  }) {
    return Container(
      width: width,
      height: 48,
      decoration: BoxDecoration(
        // 彩虹多彩色马卡龙渐变 - 极简苹果超酷风格
        gradient: isSelected
            ? const LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFF74C0FC), // 天蓝马卡龙
                  Color(0xFF9775FA), // 紫色马卡龙
                  Color(0xFFFF8CC8), // 粉红马卡龙
                  Color(0xFFFFD43B), // 黄色马卡龙
                  Color(0xFF51CF66), // 绿色马卡龙
                ],
              )
            : const LinearGradient(
                colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFF8F9FA),
                ],
              ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: const Color(0xFF74C0FC).withOpacity(0.25),
                  blurRadius: 25,
                  offset: const Offset(0, 10),
                ),
                BoxShadow(
                  color: const Color(0xFF9775FA).withOpacity(0.20),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: 15,
                  offset: const Offset(0, 3),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(24),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('$text button pressed!'),
                backgroundColor: const Color(0xFF667eea),
                behavior: SnackBarBehavior.floating,
              ),
            );
          },
          child: Container(
            alignment: Alignment.center,
            child: Text(
              text,
              style: TextStyle(
                color: isSelected ? Colors.white : const Color(0xFF1A1A1A),
                fontSize: 14,
                fontWeight: FontWeight.w600,
                letterSpacing: isSelected ? 1 : 0.5,
              ),
            ),
          ),
        ),
      ),
    );
  }
} 