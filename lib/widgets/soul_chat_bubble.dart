import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/soul_message.dart';
import '../models/diary_entry.dart';
import '../services/diary_service.dart';
import '../utils/language_manager.dart';
import '../utils/language_manager_extension.dart';
import 'soul_choice_buttons.dart';
import 'liquid_glass_dialog.dart';

class SoulChatBubble extends StatelessWidget {
  final SoulMessage message;
  final VoidCallback? onTarotRequest;
  final Function(String)? onSendMessage;

  const SoulChatBubble({
    Key? key,
    required this.message,
    this.onTarotRequest,
    this.onSendMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: message.isUser 
          ? MainAxisAlignment.end 
          : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) _buildAvatar(),
          if (!message.isUser) const SizedBox(width: 12),
          Flexible(
            child: Column(
              crossAxisAlignment: message.isUser 
                ? CrossAxisAlignment.end 
                : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context),
                if (message.messageType == SoulMessageType.welcome)
                  _buildChoiceButtons(context),
              ],
            ),
          ),
          if (message.isUser) const SizedBox(width: 12),
          if (message.isUser) _buildUserAvatar(),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: _getEnergyGradient(message.energyLevel),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: _getEnergyColor(message.energyLevel).withValues(alpha: 0.3),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: const Icon(
        Icons.auto_awesome,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          child: Text(
            message.content,
            style: TextStyle(
              color: Colors.black87.withValues(alpha: 0.8),
              fontSize: 16,
              height: 1.5,
              fontWeight: FontWeight.w400,
              fontStyle: FontStyle.italic,
            ),
            softWrap: true,
            overflow: TextOverflow.visible,
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.purple.withValues(alpha: 0.6),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: message.isUser
          ? const LinearGradient(
              colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
            )
          : null,
        color: message.isUser
          ? null
          : Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: message.isUser
          ? null
          : Border.all(
              color: Colors.white.withValues(alpha: 0.3), // 统一使用白色边框
              width: 1.5,
            ),
        boxShadow: [
          BoxShadow(
            color: message.isUser
              ? Colors.purple.withValues(alpha: 0.3)
              : Colors.black.withValues(alpha: 0.1), // 统一使用黑色阴影
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.messageType != SoulMessageType.normal)
            _buildMessageTypeIndicator(context),
          message.isLoading
            ? _buildLoadingIndicator()
            : Flexible(
                child: Text(
                  message.content,
                  style: TextStyle(
                    color: message.isUser ? Colors.white : Colors.black87,
                    fontSize: 16,
                    height: 1.5,
                    fontWeight: message.isUser
                      ? FontWeight.w500
                      : FontWeight.w400,
                    shadows: message.isUser
                      ? [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 1,
                          ),
                        ]
                      : null,
                  ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ),
          if (message.tarotCards != null && message.tarotCards!.isNotEmpty)
            _buildTarotCards(),
        ],
      ),
    );
  }

  Widget _buildMessageTypeIndicator(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    String indicator;
    switch (message.messageType) {
      case SoulMessageType.welcome:
        indicator = '🌌 ${languageManager.translateWithPatch('soul_connection')}';
        break;
      case SoulMessageType.tarotRequest:
        indicator = '🔮 ${languageManager.translateWithPatch('tarot_guidance')}';
        break;
      case SoulMessageType.tarotReading:
        indicator = '✨ ${languageManager.translateWithPatch('soul_reading')}';
        break;
      case SoulMessageType.insight:
        indicator = '💎 ${languageManager.translateWithPatch('deep_insight')}';
        break;
      case SoulMessageType.healing:
        indicator = '💝 ${languageManager.translateWithPatch('healing_praise')}';
        break;
      case SoulMessageType.guidance:
        indicator = '🌟 ${languageManager.translateWithPatch('wisdom_guidance')}';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: message.isUser
          ? Colors.white.withValues(alpha: 0.2)
          : _getEnergyColor(message.energyLevel).withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        indicator,
        style: TextStyle(
          color: message.isUser
            ? Colors.white
            : _getEnergyColor(message.energyLevel),
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTarotCards() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔮 选中的塔罗牌',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: message.tarotCards!.map((card) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                card,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildChoiceButtons(BuildContext context) {
    return SoulChoiceButtons(
      onChoice: (choice) {
        // 处理选择
        _handleChoice(context, choice);
      },
    );
  }

  void _handleChoice(BuildContext context, String choice) {
    debugPrint('🎯 用户选择: $choice');
    debugPrint('⏰ 按钮点击时间: ${DateTime.now()}');

    // 根据选择类型处理
    switch (choice) {
      case 'explore':
        debugPrint('🔍 触发探索内心模式');
        // 发送探索请求消息，而不是调用旧的塔罗请求
        if (onSendMessage != null) {
          debugPrint('📞 发送探索请求消息');
          onSendMessage!('EXPLORE_INNER_REQUEST');
        } else {
          debugPrint('❌ onSendMessage回调为null');
        }
        break;
      case 'share':
      case 'happy':
      case 'growth':
      case 'effort':
      case 'grateful':
        debugPrint('🌟 触发分享美好模式: $choice');
        // 进入夸夸模式
        _triggerPraiseMode(context, choice);
        break;
      case 'diary':
        debugPrint('📖 触发查看日记模式');
        // 查看日记
        _viewDiary(context);
        break;
      case 'endpoint':
        debugPrint('🎯 触发站在终点模式');
        // 站在终点模式
        _triggerEndpointMode(context);
        break;
      default:
        debugPrint('⚠️ 未知选择类型: $choice');
    }
  }

  void _triggerPraiseMode(BuildContext context, String type) async {
    debugPrint('🌟 触发AI记忆分享模式: $type');
    debugPrint('📊 开始查询用户日记...');

    // 获取最近的日记
    final recentDiaries = await DiaryService.getRecentDiaries(days: 30, limit: 10);
    debugPrint('📚 查询到 ${recentDiaries.length} 条日记');

    if (!context.mounted) {
      debugPrint('⚠️ Context已卸载，停止处理');
      return;
    }

    if (recentDiaries.isEmpty) {
      debugPrint('📝 没有日记，引导用户直接分享');
      // 没有日记时，引导用户直接分享
      _promptUserToShare(context, type);
      return;
    }

    debugPrint('🤖 使用AI记忆功能分析日记');
    // 使用AI记忆功能自动分析相关日记
    _processAIMemoryShare(context, type, recentDiaries);
  }

  void _viewDiary(BuildContext context) async {
    // 获取最近的日记并显示
    final recentDiaries = await DiaryService.getRecentDiaries(days: 7, limit: 3);

    if (!context.mounted) return;

    if (recentDiaries.isEmpty) {
      // 如果没有日记，引导用户创建第一篇日记
      _showCreateFirstDiaryDialog(context);
      return;
    }

    // 显示日记列表对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📝 最近的日记'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: recentDiaries.length,
            itemBuilder: (context, index) {
              final diary = recentDiaries[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Text(
                    diary.moodEmoji,
                    style: const TextStyle(fontSize: 20),
                  ),
                  title: Text(
                    diary.summary,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    '${diary.createdAt.toString().substring(0, 10)} • 心情: ${diary.moodScore ?? "未评分"}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showDiaryDetail(context, diary);
                  },
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showDiaryDetail(BuildContext context, diary) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('📖 ${diary.createdAt.toString().substring(0, 10)}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (diary.moodScore != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Text(diary.moodEmoji, style: const TextStyle(fontSize: 20)),
                      const SizedBox(width: 8),
                      Text('心情评分: ${diary.moodScore}/10'),
                    ],
                  ),
                ),
              if (diary.tags.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Wrap(
                    spacing: 4,
                    children: diary.tags.map<Widget>((tag) => Chip(
                      label: Text(tag, style: const TextStyle(fontSize: 10)),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    )).toList(),
                  ),
                ),
              Text(diary.content),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Color _getEnergyColor(EnergyLevel level) {
    switch (level) {
      case EnergyLevel.divine:
        return const Color(0xFFFFD700); // 恢复金色，用于标签显示
      case EnergyLevel.mystical:
        return const Color(0xFF9333EA); // 紫色
      case EnergyLevel.healing:
        return const Color(0xFF10B981); // 绿色
      case EnergyLevel.wisdom:
        return const Color(0xFF3B82F6); // 蓝色
      case EnergyLevel.love:
        return const Color(0xFFEC4899); // 粉色
      case EnergyLevel.neutral:
        return const Color(0xFFE5E7EB); // 白色
    }
  }

  Gradient _getEnergyGradient(EnergyLevel level) {
    switch (level) {
      case EnergyLevel.divine:
        return const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)], // 恢复金色渐变，用于标签
        );
      case EnergyLevel.mystical:
        return const LinearGradient(
          colors: [Color(0xFF9333EA), Color(0xFF6B46C1)],
        );
      case EnergyLevel.healing:
        return const LinearGradient(
          colors: [Color(0xFF10B981), Color(0xFF059669)],
        );
      case EnergyLevel.wisdom:
        return const LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
        );
      case EnergyLevel.love:
        return const LinearGradient(
          colors: [Color(0xFFEC4899), Color(0xFFDB2777)],
        );
      case EnergyLevel.neutral:
        return const LinearGradient(
          colors: [Color(0xFFE5E7EB), Color(0xFFD1D5DB)],
        );
    }
  }

  void _showCreateFirstDiaryDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.auto_stories, color: Colors.purple),
            SizedBox(width: 8),
            Text('✨ 开始记录美好'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '看起来你还没有写过日记呢！',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 12),
            Text(
              '日记是与高我对话的重要方式，通过记录生活中的点点滴滴，高我能更好地了解你，给出更贴心的指引。',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            SizedBox(height: 12),
            Text(
              '💡 你可以记录：',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 4),
            Text('• 今天发生的有趣事情\n• 内心的感受和想法\n• 学到的新知识\n• 感恩的人和事',
              style: TextStyle(fontSize: 13, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('稍后再说'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showQuickDiaryInput(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
            child: const Text('现在开始'),
          ),
        ],
      ),
    );
  }

  void _showQuickDiaryInput(BuildContext context) {
    final TextEditingController diaryController = TextEditingController();
    int selectedMood = 5;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('📝 写下今天的心情'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: diaryController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: '今天发生了什么有趣的事情？\n你现在的心情如何？\n有什么想要记录的吗？',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              const Text('今天的心情评分：'),
              Slider(
                value: selectedMood.toDouble(),
                min: 1,
                max: 10,
                divisions: 9,
                label: selectedMood.toString(),
                onChanged: (value) {
                  setState(() {
                    selectedMood = value.round();
                  });
                },
              ),
              Text('$selectedMood/10'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (diaryController.text.trim().isNotEmpty) {
                  await DiaryService.saveDiaryEntry(
                    content: diaryController.text.trim(),
                    moodScore: selectedMood,
                    tags: ['第一篇日记'],
                  );
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('🎉 第一篇日记保存成功！高我正在学习了解你...'),
                        backgroundColor: Colors.purple,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
              child: const Text('保存'),
            ),
          ],
        ),
      ),
    );
  }

  void _showShareGoodDialog(BuildContext context, String type, List<DiaryEntry> diaries) {
    List<DiaryEntry> selectedDiaries = [];

    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.2),
                      Colors.white.withValues(alpha: 0.1),
                    ],
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getShareTypeEmoji(type),
                            style: const TextStyle(fontSize: 20),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            '✨ 今天想要分享什么呢？',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                              shadows: [
                                Shadow(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  blurRadius: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // 描述文字
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getShareTypeDescription(type),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // 分享方式选择
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: TextButton.icon(
                              onPressed: () {
                                Navigator.of(context).pop();
                                _sendDirectShareMessage(context, type);
                              },
                              icon: const Icon(Icons.chat_bubble_outline, color: Colors.black87),
                              label: Text(
                                '直接分享',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: TextButton.icon(
                              onPressed: () {
                                Navigator.of(context).pop();
                                _showDiarySelectionDialog(context, type);
                              },
                              icon: const Icon(Icons.book_outlined, color: Colors.black87),
                              label: Text(
                                '选择日记',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // 日记列表
                    Container(
                      height: 300,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: ListView.builder(
                        itemCount: diaries.length,
                        padding: const EdgeInsets.all(8),
                        itemBuilder: (context, index) {
                          final diary = diaries[index];
                          final isSelected = selectedDiaries.contains(diary);

                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            decoration: BoxDecoration(
                              color: isSelected
                                ? Colors.purple.withValues(alpha: 0.2)
                                : Colors.white.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected
                                  ? Colors.purple.withValues(alpha: 0.4)
                                  : Colors.white.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: ListTile(
                              leading: Checkbox(
                                value: isSelected,
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      selectedDiaries.add(diary);
                                    } else {
                                      selectedDiaries.remove(diary);
                                    }
                                  });
                                },
                                activeColor: Colors.purple,
                                fillColor: WidgetStateProperty.resolveWith((states) {
                                  if (states.contains(WidgetState.selected)) {
                                    return Colors.purple;
                                  }
                                  return Colors.white.withValues(alpha: 0.8);
                                }),
                              ),
                              title: Text(
                                diary.summary,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                  color: Colors.black87,
                                  fontSize: 14,
                                ),
                              ),
                              subtitle: Text(
                                '${diary.createdAt.toString().substring(0, 10)} • 心情: ${diary.moodScore ?? "未评分"}/10',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                              trailing: Text(
                                diary.moodEmoji,
                                style: const TextStyle(fontSize: 18),
                              ),
                              onTap: () {
                                setState(() {
                                  if (selectedDiaries.contains(diary)) {
                                    selectedDiaries.remove(diary);
                                  } else {
                                    selectedDiaries.add(diary);
                                  }
                                });
                              },
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 20),
                    // 按钮区域
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: Text(
                                '取消',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: selectedDiaries.isEmpty
                                ? LinearGradient(
                                    colors: [
                                      Colors.grey.withValues(alpha: 0.3),
                                      Colors.grey.withValues(alpha: 0.2),
                                    ],
                                  )
                                : const LinearGradient(
                                    colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
                                  ),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: TextButton(
                              onPressed: selectedDiaries.isEmpty
                                ? null
                                : () {
                                    debugPrint('🌟 发送选中的日记: ${selectedDiaries.length}条');
                                    Navigator.of(context).pop();
                                    _sendShareGoodMessage(context, type, selectedDiaries);
                                  },
                              child: Text(
                                selectedDiaries.isEmpty
                                  ? '请选择日记'
                                  : '发送 (${selectedDiaries.length})',
                                style: TextStyle(
                                  color: selectedDiaries.isEmpty
                                    ? Colors.black54
                                    : Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getShareTypeEmoji(String type) {
    switch (type) {
      case 'happy': return '🎉';
      case 'growth': return '🌱';
      case 'effort': return '💪';
      case 'grateful': return '🙏';
      default: return '✨';
    }
  }

  String _getShareTypeDescription(String type) {
    switch (type) {
      case 'happy': return '选择让你开心快乐的美好时光';
      case 'growth': return '选择让你成长进步的珍贵经历';
      case 'effort': return '选择你努力奋斗的闪光时刻';
      case 'grateful': return '选择让你心怀感恩的温暖回忆';
      default: return '选择你想要分享的美好回忆';
    }
  }

  void _sendShareGoodMessage(BuildContext context, String type, List<DiaryEntry> selectedDiaries) {
    debugPrint('🌟 准备发送分享美好消息');

    // 构建分享消息
    final shareMessage = _buildShareMessage(type, selectedDiaries);

    // 通过回调函数发送消息
    if (onSendMessage != null) {
      onSendMessage!(shareMessage);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✨ 已发送 ${selectedDiaries.length} 条美好回忆'),
          backgroundColor: Colors.purple,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // 如果没有回调函数，显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ 无法发送消息，请稍后再试'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  String _buildShareMessage(String type, List<DiaryEntry> diaries) {
    final typeText = _getShareTypeText(type);
    final diaryTexts = diaries.map((d) =>
      '📝 ${d.createdAt.toString().substring(0, 10)}: ${d.summary}'
    ).join('\n\n');

    return '''$typeText

$diaryTexts

请高我为我分析这些美好的回忆，给我一些温暖的鼓励和指引 ✨''';
  }

  String _getShareTypeText(String type) {
    switch (type) {
      case 'happy': return '🎉 分享我的快乐时光';
      case 'growth': return '🌱 分享我的成长历程';
      case 'effort': return '💪 分享我的努力时刻';
      case 'grateful': return '🙏 分享我的感恩回忆';
      default: return '✨ 分享我的美好回忆';
    }
  }

  void _sendDirectShareMessage(BuildContext context, String type) {
    debugPrint('🌟 准备发送直接分享消息');

    // 构建直接分享消息
    final shareMessage = _buildDirectShareMessage(type);

    // 通过回调函数发送消息
    if (onSendMessage != null) {
      onSendMessage!(shareMessage);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✨ 已发送${_getShareTypeEmoji(type)} ${_getShareTypeDescription(type)}'),
          backgroundColor: Colors.purple,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // 如果没有回调函数，显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ 无法发送消息，请稍后再试'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  String _buildDirectShareMessage(String type) {
    final typeText = _getShareTypeText(type);

    switch (type) {
      case 'happy':
        return '''$typeText

今天我想分享一些让我开心快乐的事情！最近有很多美好的时刻让我感到幸福和满足。

请高我为我分析这些快乐的能量，给我一些温暖的鼓励，帮我看到生活中更多的美好 ✨''';
      case 'growth':
        return '''$typeText

我想分享最近的成长和进步！虽然过程中有挑战，但我感受到了自己的变化和提升。

请高我为我分析这些成长的意义，给我一些智慧的指引，帮我继续前进 ✨''';
      case 'effort':
        return '''$typeText

我想分享最近努力奋斗的时刻！虽然有时候很累，但我为自己的坚持感到骄傲。

请高我为我分析这些努力的价值，给我一些力量的鼓励，帮我保持动力 ✨''';
      case 'grateful':
        return '''$typeText

我想分享心中的感恩！生活中有太多值得感谢的人和事，让我的心充满温暖。

请高我为我分析这些感恩的能量，给我一些爱的指引，帮我传递更多正能量 ✨''';
      default:
        return '''$typeText

我想分享一些美好的回忆和感受！生活中总有一些特别的时刻值得珍藏。

请高我为我分析这些美好的意义，给我一些温暖的鼓励和指引 ✨''';
    }
  }

  void _showDiarySelectionDialog(BuildContext context, String type) {
    // 显示手动日记选择对话框（备选功能）
    _showManualDiarySelection(context, type);
  }

  void _promptUserToShare(BuildContext context, String type) {
    final shareMessage = _buildPromptShareMessage(type);

    // 直接发送引导消息
    if (onSendMessage != null) {
      onSendMessage!(shareMessage);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✨ 请告诉我你想分享的${_getShareTypeDescription(type)}'),
          backgroundColor: Colors.purple,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _processAIMemoryShare(BuildContext context, String type, List<DiaryEntry> diaries) async {
    debugPrint('🔍 开始AI记忆分析处理');
    // 使用AI分析相关日记，自动生成分享内容
    final relevantDiaries = _filterRelevantDiaries(diaries, type);
    debugPrint('📊 筛选出 ${relevantDiaries.length} 条相关日记');

    final shareMessage = _buildAIMemoryShareMessage(type, relevantDiaries);
    debugPrint('📝 构建分享消息: ${shareMessage.length > 50 ? shareMessage.substring(0, 50) + "..." : shareMessage}');

    // 发送AI记忆分享消息
    if (onSendMessage != null) {
      debugPrint('📤 发送分享消息到聊天界面');
      onSendMessage!(shareMessage);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🤖 AI已自动分析你的${relevantDiaries.length}条相关日记'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      debugPrint('❌ onSendMessage回调为null，无法发送消息');
    }
  }

  List<DiaryEntry> _filterRelevantDiaries(List<DiaryEntry> diaries, String type) {
    // 根据分享类型筛选相关日记
    switch (type) {
      case 'happy':
        return diaries.where((diary) =>
          (diary.moodScore ?? 0) >= 7 ||
          diary.content.contains('开心') ||
          diary.content.contains('快乐') ||
          diary.content.contains('高兴') ||
          diary.moodEmoji.contains('😊') ||
          diary.moodEmoji.contains('😄') ||
          diary.moodEmoji.contains('🎉')
        ).take(5).toList();
      case 'growth':
        return diaries.where((diary) =>
          diary.content.contains('成长') ||
          diary.content.contains('进步') ||
          diary.content.contains('学习') ||
          diary.content.contains('提升') ||
          diary.content.contains('改变')
        ).take(5).toList();
      case 'effort':
        return diaries.where((diary) =>
          diary.content.contains('努力') ||
          diary.content.contains('坚持') ||
          diary.content.contains('奋斗') ||
          diary.content.contains('拼搏') ||
          diary.content.contains('加油')
        ).take(5).toList();
      case 'grateful':
        return diaries.where((diary) =>
          diary.content.contains('感谢') ||
          diary.content.contains('感恩') ||
          diary.content.contains('感激') ||
          diary.content.contains('谢谢') ||
          diary.content.contains('幸福')
        ).take(5).toList();
      default:
        return diaries.take(3).toList();
    }
  }

  String _buildPromptShareMessage(String type) {
    switch (type) {
      case 'happy':
        return '''SHARE_HAPPY_REQUEST'''; // 特殊标识，让AI识别并回应
      case 'growth':
        return '''SHARE_GROWTH_REQUEST'''; // 特殊标识，让AI识别并回应
      case 'effort':
        return '''SHARE_EFFORT_REQUEST'''; // 特殊标识，让AI识别并回应
      case 'grateful':
        return '''SHARE_GRATEFUL_REQUEST'''; // 特殊标识，让AI识别并回应
      default:
        return '''SHARE_GENERAL_REQUEST'''; // 特殊标识，让AI识别并回应
    }
  }

  String _buildAIMemoryShareMessage(String type, List<DiaryEntry> relevantDiaries) {
    final typeText = _getShareTypeText(type);

    // 只发送简单的分享类型，不暴露内部AI分析过程
    return typeText;
  }

  void _showManualDiarySelection(BuildContext context, String type) async {
    // 保留原有的手动选择功能作为备选
    final diaries = await DiaryService.getRecentDiaries(days: 30, limit: 10);
    if (diaries.isEmpty) return;

    if (!context.mounted) return;
    _showShareGoodDialog(context, type, diaries);
  }

  // 🔧 新增：站在终点模式
  void _triggerEndpointMode(BuildContext context) {
    debugPrint('🎯 开始站在终点模式');

    // 显示目标输入对话框
    _showGoalInputDialog(context);
  }

  // 🔧 显示目标输入对话框 - 苹果液态毛玻璃风格
  void _showGoalInputDialog(BuildContext context) {
    final TextEditingController goalController = TextEditingController();
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    LiquidGlassDialogHelper.show(
      context: context,
      title: TranslationHelper.safeTranslate(languageManager, 'standing_at_endpoint'),
      icon: const Text('♾️', style: TextStyle(fontSize: 24)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            TranslationHelper.safeTranslate(languageManager, 'endpoint_description'),
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: TextField(
              controller: goalController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: languageManager.currentLanguage.startsWith('zh')
                    ? '例如：我想要在一年内升职加薪\n我想要找到理想的伴侣\n我想要身体更健康...'
                    : 'For example: I want to get promoted within a year\nI want to find my ideal partner\nI want to be healthier...',
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
                labelText: TranslationHelper.safeTranslate(languageManager, 'your_manifestation_goal'),
                labelStyle: const TextStyle(
                  color: Color(0xFF10B981),
                  fontWeight: FontWeight.w500,
                ),
                hintStyle: const TextStyle(
                  color: Colors.grey,
                  fontSize: 13,
                ),
              ),
            ),
          ),
        ],
      ),
      actions: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              TranslationHelper.safeTranslate(languageManager, 'cancel'),
              style: const TextStyle(
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF10B981), Color(0xFF059669)],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF10B981).withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: () {
              final goal = goalController.text.trim();
              if (goal.isNotEmpty) {
                Navigator.of(context).pop();
                _sendEndpointMessage(context, goal);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              TranslationHelper.safeTranslate(languageManager, 'start_conversation'),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 🔧 发送站在终点消息
  void _sendEndpointMessage(BuildContext context, String goal) {
    debugPrint('🎯 发送站在终点消息: $goal');

    // 构建站在终点消息
    final endpointMessage = 'ENDPOINT_REQUEST:$goal';

    // 通过回调函数发送消息
    if (onSendMessage != null) {
      onSendMessage!(endpointMessage);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🎯 已开启站在终点模式：$goal'),
          backgroundColor: const Color(0xFF10B981),
          duration: const Duration(seconds: 3),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ 无法发送消息，请稍后再试'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
