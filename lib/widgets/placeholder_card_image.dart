import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';

/// 占位符塔罗牌图片组件
/// 当Figma图片资源不可用时使用
class PlaceholderCardImage extends StatelessWidget {
  final TarotCard card;
  final double width;
  final double height;
  final bool isRevealed;

  const PlaceholderCardImage({
    super.key,
    required this.card,
    this.width = 120,
    this.height = 180,
    this.isRevealed = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isRevealed) {
      return _buildCardBack();
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: _getCardGradient(),
        borderRadius: BorderRadius.circular(FigmaTheme.radiusMedium),
        border: Border.all(
          color: _getCardColor(),
          width: 2,
        ),
        boxShadow: FigmaTheme.cardShadow,
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: <PERSON>umn(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 卡牌编号
            _buildCardNumber(),
            
            // 卡牌图标
            _buildCardIcon(),
            
            // 卡牌名称
            _buildCardName(),
            
            // 卡牌关键词
            _buildKeywords(),
          ],
        ),
      ),
    );
  }

  Widget _buildCardBack() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: FigmaTheme.glassGradient,
        borderRadius: BorderRadius.circular(FigmaTheme.radiusMedium),
        border: Border.all(
          color: FigmaTheme.primaryPink,
          width: 2,
        ),
        boxShadow: FigmaTheme.cardShadow,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.auto_awesome,
              size: 48,
              color: FigmaTheme.textPrimary.withValues(alpha: 0.8),
            ),
            const SizedBox(height: 8),
            Text(
              '✦',
              style: TextStyle(
                fontSize: 32,
                color: FigmaTheme.textPrimary.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '神秘塔罗',
              style: FigmaTheme.bodySmall.copyWith(
                color: FigmaTheme.textPrimary.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardNumber() {
    return Align(
      alignment: Alignment.topLeft,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: _getCardColor().withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          card.id.split('-').last,
          style: FigmaTheme.bodySmall.copyWith(
            fontWeight: FontWeight.bold,
            color: FigmaTheme.textPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildCardIcon() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: _getCardColor().withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(FigmaTheme.radiusSmall),
        border: Border.all(
          color: _getCardColor().withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Icon(
        _getCardIcon(),
        size: 32,
        color: FigmaTheme.textPrimary,
      ),
    );
  }

  Widget _buildCardName() {
    return Text(
      card.name,
      style: FigmaTheme.labelMedium.copyWith(
        fontWeight: FontWeight.bold,
        color: FigmaTheme.textPrimary,
      ),
      textAlign: TextAlign.center,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildKeywords() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: card.keywords.take(3).map((keyword) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: FigmaTheme.textPrimary.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            keyword,
            style: FigmaTheme.bodySmall.copyWith(
              fontSize: 9,
              color: FigmaTheme.textPrimary,
            ),
          ),
        );
      }).toList(),
    );
  }

  Color _getCardColor() {
    if (card.isMajorArcana) {
      return FigmaTheme.primaryPink;
    }
    
    // 根据牌组确定颜色
    final cardName = card.name.toLowerCase();
    if (cardName.contains('cups')) {
      return const Color(0xFF3B82F6); // 蓝色
    } else if (cardName.contains('wands')) {
      return const Color(0xFFEF4444); // 红色
    } else if (cardName.contains('swords')) {
      return const Color(0xFF6B7280); // 灰色
    } else if (cardName.contains('pentacles')) {
      return const Color(0xFF10B981); // 绿色
    }
    
    return FigmaTheme.primaryPink;
  }

  LinearGradient _getCardGradient() {
    final cardColor = _getCardColor();
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        cardColor.withValues(alpha: 0.1),
        cardColor.withValues(alpha: 0.3),
        FigmaTheme.surfaceWhite,
      ],
    );
  }

  IconData _getCardIcon() {
    if (card.isMajorArcana) {
      // 大阿卡纳的特殊图标
      switch (card.name) {
        case 'The Fool':
          return Icons.hiking;
        case 'The Magician':
          return Icons.auto_fix_high;
        case 'The High Priestess':
          return Icons.psychology;
        case 'The Empress':
          return Icons.nature_people;
        case 'The Emperor':
          return Icons.account_balance;
        case 'The Hierophant':
          return Icons.school;
        case 'The Lovers':
          return Icons.favorite;
        case 'The Chariot':
          return Icons.directions_car;
        case 'Strength':
          return Icons.fitness_center;
        case 'The Hermit':
          return Icons.lightbulb;
        case 'Wheel of Fortune':
          return Icons.casino;
        case 'Justice':
          return Icons.balance;
        case 'The Hanged Man':
          return Icons.self_improvement;
        case 'Death':
          return Icons.transform;
        case 'Temperance':
          return Icons.water_drop;
        case 'The Devil':
          return Icons.warning;
        case 'The Tower':
          return Icons.flash_on;
        case 'The Star':
          return Icons.star;
        case 'The Moon':
          return Icons.nightlight;
        case 'The Sun':
          return Icons.wb_sunny;
        case 'Judgment':
          return Icons.gavel;
        case 'The World':
          return Icons.public;
        default:
          return Icons.auto_awesome;
      }
    }
    
    // 小阿卡纳的图标
    final cardName = card.name.toLowerCase();
    if (cardName.contains('cups')) {
      return Icons.local_drink;
    } else if (cardName.contains('wands')) {
      return Icons.whatshot;
    } else if (cardName.contains('swords')) {
      return Icons.flash_on;
    } else if (cardName.contains('pentacles')) {
      return Icons.monetization_on;
    }
    
    return Icons.auto_awesome;
  }
}
