import 'package:flutter/material.dart';
import '../models/diary_entry.dart';

class ChatSummaryCard extends StatelessWidget {
  final DiaryEntry chatEntry;
  final VoidCallback? onTap;

  const ChatSummaryCard({
    super.key,
    required this.chatEntry,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部：来源图标和时间
              Row(
                children: [
                  _buildSourceIcon(),
                  const SizedBox(width: 8),
                  Text(
                    _getSourceText(),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getSourceColor(),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatTime(chatEntry.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // 摘要内容
              if (chatEntry.chatSummary != null) ...[
                Text(
                  chatEntry.chatSummary!,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // 底部：查看详情提示
              Row(
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '点击查看完整对话',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Colors.grey[400],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSourceIcon() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: const Icon(
            Icons.psychology,
            size: 16,
            color: Colors.purple,
          ),
        );
      case 'tarot':
        return Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.amber.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: const Icon(
            Icons.auto_awesome,
            size: 16,
            color: Colors.amber,
          ),
        );
      default:
        return Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: const Icon(
            Icons.chat,
            size: 16,
            color: Colors.blue,
          ),
        );
    }
  }

  String _getSourceText() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return '灵魂之镜';
      case 'tarot':
        return '塔罗解读';
      default:
        return '聊天记录';
    }
  }

  Color _getSourceColor() {
    switch (chatEntry.chatSource) {
      case 'soul_mirror':
        return Colors.purple;
      case 'tarot':
        return Colors.amber;
      default:
        return Colors.blue;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.month}/${dateTime.day}';
    }
  }
}
