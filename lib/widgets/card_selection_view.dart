import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/widgets/placeholder_card_image.dart';

class CardSelectionView extends StatefulWidget {
  final SpreadType spreadType;
  final List<TarotCard> selectedCards;
  final Function(TarotCard) onCardSelected;
  final VoidCallback onConfirm;
  final VoidCallback onReset;
  final bool isConfirmationStage;

  const CardSelectionView({
    super.key,
    required this.spreadType,
    required this.selectedCards,
    required this.onCardSelected,
    required this.onConfirm,
    required this.onReset,
    required this.isConfirmationStage,
  });

  @override
  State<CardSelectionView> createState() => _CardSelectionViewState();
}

class _CardSelectionViewState extends State<CardSelectionView> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final List<TarotCard> _shuffledDeck = [];
  bool _isShuffling = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _animationController.forward();
    
    // Shuffle the deck
    _shuffleDeck();
    
    // Simulate shuffling animation
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        setState(() {
          _isShuffling = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _shuffleDeck() {
    // Create a copy of all cards
    final allCards = [...TarotCard.majorArcana, ...TarotCard.minorArcana];
    
    // Shuffle the deck
    allCards.shuffle();
    
    // Take the first 21 cards for the selection
    _shuffledDeck.addAll(allCards.take(21));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            widget.isConfirmationStage
                ? 'Confirm Your Selection'
                : 'Select ${_getRequiredCardCountText()}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: widget.isConfirmationStage
              ? _buildConfirmationView()
              : _isShuffling
                  ? _buildShufflingAnimation()
                  : _buildCardSelectionView(),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (widget.isConfirmationStage) ...[
                ElevatedButton.icon(
                  onPressed: widget.onReset,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Redraw'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: widget.onConfirm,
                  icon: const Icon(Icons.check),
                  label: const Text('Confirm'),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShufflingAnimation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: List.generate(
              10,
              (index) => Positioned(
                left: 150 + (index * 3) * (index % 2 == 0 ? 1 : -1),
                top: 150 + (index * 2) * (index % 3 == 0 ? 1 : -1),
                child: Transform.rotate(
                  angle: (index * 0.05) * (index % 2 == 0 ? 1 : -1),
                  child: Container(
                    width: 100,
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.indigo.shade900,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      image: const DecorationImage(
                        image: AssetImage('images/card_back.png'),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ).animate(
            onComplete: (controller) => controller.repeat(),
          ).shake(
            duration: 2.seconds,
            hz: 2,
            curve: Curves.easeInOut,
          ),
          const SizedBox(height: 32),
          const Text(
            'Shuffling the deck...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardSelectionView() {
    // Calculate the angle between cards in the half-circle
    final totalCards = _shuffledDeck.length;
    final angleStep = 150 / (totalCards - 1);
    const startAngle = -75.0;
    
    return Stack(
      children: [
        // Half-circle of cards
        ...List.generate(totalCards, (index) {
          final angle = startAngle + (angleStep * index);
          final radians = angle * (3.14159 / 180);
          
          // Calculate position on the half-circle
          final radius = MediaQuery.of(context).size.width * 0.8;
          final x = radius * 0.5 * -1 * (angle / 75);
          final y = radius * 0.5 - (radius * 0.5 * (1 - (angle * angle) / (75 * 75)));
          
          return Positioned(
            left: MediaQuery.of(context).size.width / 2 + x - 40,
            top: MediaQuery.of(context).size.height / 2 + y - 200,
            child: Transform.rotate(
              angle: radians * 0.5, // Slight rotation for visual effect
              child: GestureDetector(
                onTap: () {
                  if (widget.selectedCards.length < _getRequiredCardCount()) {
                    widget.onCardSelected(_shuffledDeck[index]);
                  }
                },
                child: Container(
                  width: 80,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.indigo.shade900,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 5,
                        offset: const Offset(0, 3),
                      ),
                    ],
                    image: const DecorationImage(
                      image: AssetImage('images/card_back.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
          ).animate().fadeIn(
                delay: Duration(milliseconds: 50 * index),
                duration: 300.ms,
              ).slideY(
                begin: 0.2,
                end: 0,
                delay: Duration(milliseconds: 50 * index),
                duration: 300.ms,
                curve: Curves.easeOutQuad,
              );
        }),
        
        // Selected cards display at the bottom
        Positioned(
          bottom: 20,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _getRequiredCardCount(),
              (index) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: index < widget.selectedCards.length
                    ? _buildSelectedCard(widget.selectedCards[index], index)
                    : _buildEmptyCardSlot(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedCard(TarotCard card, int index) {
    return PlaceholderCardImage(
      card: card,
      width: 70,
      height: 100,
      isRevealed: true,
    ).animate().fadeIn(duration: 300.ms).scale(
          begin: const Offset(0.8, 0.8),
          end: const Offset(1.0, 1.0),
          duration: 300.ms,
        );
  }

  Widget _buildEmptyCardSlot() {
    return Container(
      width: 70,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.withOpacity(0.5),
          width: 2,
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.add,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildConfirmationView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Your Selected Cards',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Wrap(
            spacing: 16,
            runSpacing: 16,
            alignment: WrapAlignment.center,
            children: widget.selectedCards.map((card) {
              return Column(
                children: [
                  PlaceholderCardImage(
                    card: card,
                    width: 100,
                    height: 150,
                    isRevealed: true,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    card.name,
                    style: FigmaTheme.labelMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ).animate().fadeIn(duration: 300.ms).scale(
                    begin: const Offset(0.9, 0.9),
                    end: const Offset(1.0, 1.0),
                    duration: 300.ms,
                  );
            }).toList(),
          ),
        ],
      ),
    );
  }

  int _getRequiredCardCount() {
    switch (widget.spreadType) {
      case SpreadType.single:
        return 1;
      case SpreadType.three:
        return 3;
      case SpreadType.celtic:
        return 10;
      default:
        return 0;
    }
  }

  String _getRequiredCardCountText() {
    final count = _getRequiredCardCount();
    final remaining = count - widget.selectedCards.length;
    
    return '${remaining > 0 ? remaining : count} ${remaining == 1 ? 'Card' : 'Cards'}';
  }
}
