import 'package:flutter/material.dart';
import '../models/tarot_card.dart';
import '../utils/tarot_image_manager.dart';
import '../services/tarot_data_service.dart';
import '../utils/language_manager.dart';
import 'package:provider/provider.dart';

class ManualCardSelectionSheet extends StatefulWidget {
  final Function(TarotCard) onCardSelected;

  const ManualCardSelectionSheet({
    super.key,
    required this.onCardSelected,
  });

  @override
  State<ManualCardSelectionSheet> createState() => _ManualCardSelectionSheetState();
}

class _ManualCardSelectionSheetState extends State<ManualCardSelectionSheet> {
  String searchQuery = '';
  String selectedCategory = 'all';
  List<TarotCard> _allCards = [];
  List<TarotCard> _filteredCards = [];
  bool _isLoading = true;
  int selectedTabIndex = 0; // 0: 正位, 1: 逆位
  
  @override
  void initState() {
    super.initState();
    _loadCards();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    final filteredCards = _filterCards(_allCards);
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 🎯 顶部标题和搜索区域
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // 标题
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          languageManager.translate('manual_select'),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF333333),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    );
                  },
                ),
                
                const SizedBox(height: 16),
                
                // 搜索框
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return TextField(
                      onChanged: (value) {
                        setState(() {
                          searchQuery = value;
                        });
                      },
                      decoration: InputDecoration(
                        hintText: languageManager.translate('search_tarot_cards'),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 16),
                
                // 分类筛选
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Row(
                      children: [
                        _buildCategoryChip('all', languageManager.translate('all')),
                        const SizedBox(width: 8),
                        _buildCategoryChip('major', languageManager.translate('major_arcana')),
                        const SizedBox(width: 8),
                        _buildCategoryChip('minor', languageManager.translate('minor_arcana')),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 16),

                // 正位/逆位标签选择器
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => setState(() => selectedTabIndex = 0),
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  color: selectedTabIndex == 0
                                      ? Colors.green.withOpacity(0.2)
                                      : Colors.transparent,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    bottomLeft: Radius.circular(16),
                                  ),
                                  border: selectedTabIndex == 0
                                      ? Border.all(color: Colors.green, width: 2)
                                      : null,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.arrow_upward,
                                      color: selectedTabIndex == 0 ? Colors.green : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      languageManager.translate('upright_position'),
                                      style: TextStyle(
                                        color: selectedTabIndex == 0 ? Colors.green : Colors.grey[600],
                                        fontWeight: selectedTabIndex == 0 ? FontWeight.w600 : FontWeight.normal,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.grey[400],
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => setState(() => selectedTabIndex = 1),
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  color: selectedTabIndex == 1
                                      ? Colors.red.withOpacity(0.2)
                                      : Colors.transparent,
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(16),
                                    bottomRight: Radius.circular(16),
                                  ),
                                  border: selectedTabIndex == 1
                                      ? Border.all(color: Colors.red, width: 2)
                                      : null,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.arrow_downward,
                                      color: selectedTabIndex == 1 ? Colors.red : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      languageManager.translate('reversed_position'),
                                      style: TextStyle(
                                        color: selectedTabIndex == 1 ? Colors.red : Colors.grey[600],
                                        fontWeight: selectedTabIndex == 1 ? FontWeight.w600 : FontWeight.normal,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          
          // 📝 正逆位说明
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF667eea).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFF667eea).withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Color(0xFF667eea),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        languageManager.translate('manual_selection_instruction'),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF667eea),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          
          // 🎴 卡牌网格
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.all(20),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 0.7,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: filteredCards.length,
              itemBuilder: (context, index) {
                final card = filteredCards[index];
                // 使用选中的正逆位标签
                final isReversed = selectedTabIndex == 1;

                return GestureDetector(
                  onTap: () {
                    // 创建带有正逆位信息的卡牌
                    final cardWithOrientation = TarotCard(
                      id: card.id,
                      name: card.name,
                      description: card.description,
                      meaning: card.meaning,
                      keywords: card.keywords,
                      imageUrl: card.imageUrl,
                      isMajorArcana: card.isMajorArcana,
                      isReversed: isReversed,
                      nameKey: card.nameKey,
                      descriptionKey: card.descriptionKey,
                      meaningKey: card.meaningKey,
                      reversedMeaningKey: card.reversedMeaningKey,
                      keywordKeys: card.keywordKeys,
                    );

                    widget.onCardSelected(cardWithOrientation);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // 🎨 卡牌背景容器
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.white.withOpacity(0.95),
                                  Colors.white.withOpacity(0.85),
                                ],
                              ),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.5),
                                width: 1,
                              ),
                            ),
                            child: Column(
                              children: [
                                // 卡牌图片区域
                                Expanded(
                                  flex: 3,
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(8),
                                    child: Transform(
                                      alignment: Alignment.center,
                                      transform: Matrix4.identity()
                                        ..rotateZ(isReversed ? 3.14159 : 0),
                                      child: TarotImageManager.buildCardImage(
                                        cardName: card.name,
                                        width: 60,
                                        height: 80,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),
                                
                                // 卡牌信息区域
                                Expanded(
                                  flex: 1,
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: isReversed
                                            ? [Colors.red.withOpacity(0.8), Colors.red.withOpacity(0.6)]
                                            : [const Color(0xFF667eea).withOpacity(0.8), const Color(0xFF764ba2).withOpacity(0.6)],
                                      ),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          card.name,
                                          style: const TextStyle(
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        // 🔄 正逆位指示器
                        if (isReversed)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.white, width: 1),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.red.withOpacity(0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.rotate_left,
                                    color: Colors.white,
                                    size: 8,
                                  ),
                                  const SizedBox(width: 2),
                                  Consumer<LanguageManager>(
                                    builder: (context, languageManager, child) {
                                      return Text(
                                        languageManager.translate('reversed'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 8,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),

                        // ✨ 正位指示器
                        if (!isReversed)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                              decoration: BoxDecoration(
                                color: const Color(0xFF667eea),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.white, width: 1),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFF667eea).withOpacity(0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    languageManager.translate('upright'),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 8,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<TarotCard> _filterCards(List<TarotCard> cards) {
    var filtered = cards;
    
    // 按分类筛选
    if (selectedCategory != 'all') {
      filtered = filtered.where((card) {
        if (selectedCategory == 'major') {
          return card.isMajorArcana;
        } else {
          return !card.isMajorArcana;
        }
      }).toList();
    }
    
    // 按搜索关键词筛选
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((card) {
        return card.name.toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();
    }
    
    return filtered;
  }

  Widget _buildCategoryChip(String category, String label) {
    final isSelected = selectedCategory == category;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedCategory = category;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF667eea) : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _loadCards() {
    setState(() {
      _isLoading = true;
    });

    // 模拟加载延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final allCards = TarotDataService.instance.getAllCards('zh');
        setState(() {
          _allCards = allCards;
          _filteredCards = List.from(allCards);
          _isLoading = false;
        });
      }
    });
  }
}
