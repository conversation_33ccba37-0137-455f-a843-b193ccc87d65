import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/manifestation_goal_service.dart';
import '../services/connectivity_service.dart';
import '../utils/language_manager.dart';

/// Widget to display sync status and allow manual sync
class SyncStatusWidget extends StatelessWidget {
  final bool showDetails;
  final VoidCallback? onSyncPressed;

  const SyncStatusWidget({
    super.key,
    this.showDetails = false,
    this.onSyncPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<ManifestationGoalService, ConnectivityService>(
      builder: (context, manifestationService, connectivityService, child) {
        final languageManager = LanguageManager();
        
        if (!showDetails) {
          return _buildCompactStatus(
            context, 
            manifestationService, 
            connectivityService, 
            languageManager
          );
        }
        
        return _buildDetailedStatus(
          context, 
          manifestationService, 
          connectivityService, 
          languageManager
        );
      },
    );
  }

  /// Build compact sync status indicator
  Widget _buildCompactStatus(
    BuildContext context,
    ManifestationGoalService manifestationService,
    ConnectivityService connectivityService,
    LanguageManager languageManager,
  ) {
    final isOnline = connectivityService.isOnline;
    final isSyncing = manifestationService.isSyncing;
    
    IconData icon;
    Color color;
    String tooltip;
    
    if (isSyncing) {
      icon = Icons.sync;
      color = Colors.orange;
      tooltip = languageManager.translate('syncing');
    } else if (isOnline) {
      icon = Icons.cloud_done;
      color = Colors.green;
      tooltip = languageManager.translate('synced');
    } else {
      icon = Icons.cloud_off;
      color = Colors.grey;
      tooltip = languageManager.translate('offline');
    }
    
    return GestureDetector(
      onTap: onSyncPressed ?? () => _showSyncDialog(context),
      child: Tooltip(
        message: tooltip,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: isSyncing
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                )
              : Icon(icon, color: color, size: 16),
        ),
      ),
    );
  }

  /// Build detailed sync status card
  Widget _buildDetailedStatus(
    BuildContext context,
    ManifestationGoalService manifestationService,
    ConnectivityService connectivityService,
    LanguageManager languageManager,
  ) {
    final isOnline = connectivityService.isOnline;
    final isSyncing = manifestationService.isSyncing;
    final hasError = manifestationService.error != null;
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isOnline ? Icons.cloud : Icons.cloud_off,
                  color: isOnline ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  languageManager.translate('sync_status'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (isSyncing)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Connection status
            _buildStatusRow(
              languageManager.translate('connection'),
              isOnline 
                  ? languageManager.translate('online')
                  : languageManager.translate('offline'),
              isOnline ? Colors.green : Colors.red,
            ),
            
            // Sync status
            _buildStatusRow(
              languageManager.translate('sync'),
              isSyncing 
                  ? languageManager.translate('syncing')
                  : (hasError 
                      ? languageManager.translate('error')
                      : languageManager.translate('up_to_date')),
              isSyncing 
                  ? Colors.orange 
                  : (hasError ? Colors.red : Colors.green),
            ),
            
            // Error message
            if (hasError) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  manifestationService.error!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              children: [
                if (isOnline) ...[
                  ElevatedButton.icon(
                    onPressed: isSyncing 
                        ? null 
                        : () => manifestationService.forceSyncAll(),
                    icon: const Icon(Icons.sync),
                    label: Text(languageManager.translate('sync_now')),
                  ),
                  const SizedBox(width: 8),
                ],
                TextButton.icon(
                  onPressed: () => connectivityService.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: Text(languageManager.translate('refresh')),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build status row
  Widget _buildStatusRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(color: color, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  /// Show sync dialog
  void _showSyncDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LanguageManager().translate('sync_status')),
        content: SyncStatusWidget(showDetails: true),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LanguageManager().translate('close')),
          ),
        ],
      ),
    );
  }
}
