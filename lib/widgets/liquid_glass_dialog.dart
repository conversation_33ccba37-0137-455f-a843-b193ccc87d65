import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';

/// 苹果液体玻璃风格弹窗组件
class LiquidGlassDialog extends StatelessWidget {
  final String? title;
  final Widget? content;
  final List<Widget>? actions;
  final bool barrierDismissible;
  final VoidCallback? onDismiss;
  final double? maxWidth;
  final double? maxHeight;
  final EdgeInsets? contentPadding;
  final Widget? icon;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const LiquidGlassDialog({
    super.key,
    this.title,
    this.content,
    this.actions,
    this.barrierDismissible = true,
    this.onDismiss,
    this.maxWidth,
    this.maxHeight,
    this.contentPadding,
    this.icon,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
        child: _buildDialogContent(context),
      ),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? 320,
        maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(24),
        color: backgroundColor ?? Colors.white.withOpacity(0.25),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(24),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: borderRadius ?? BorderRadius.circular(24),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题区域
                if (title != null || icon != null) _buildTitleSection(context),
                
                // 内容区域
                if (content != null) _buildContentSection(context),
                
                // 操作按钮区域
                if (actions != null && actions!.isNotEmpty) _buildActionsSection(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitleSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
                               child: icon ?? const SizedBox.shrink(),
            ),
            const SizedBox(width: 12),
          ],
          if (title != null)
            Expanded(
              child: Text(
                title!,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black.withOpacity(0.9),
                  letterSpacing: -0.3,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContentSection(BuildContext context) {
    return Flexible(
      child: Container(
        padding: contentPadding ?? const EdgeInsets.fromLTRB(24, 0, 24, 24),
        child: content!,
      ),
    );
  }

  Widget _buildActionsSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: actions!.map((action) {
          if (action is TextButton) {
            return Container(
              margin: const EdgeInsets.only(left: 8),
              child: _buildLiquidGlassButton(
                context,
                onPressed: action.onPressed,
                child: action.child ?? const Text(''),
                isPrimary: false,
              ),
            );
          }
          return action;
        }).toList(),
      ),
    );
  }

  Widget _buildLiquidGlassButton(
    BuildContext context, {
    required VoidCallback? onPressed,
    required Widget child,
    bool isPrimary = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: isPrimary 
            ? const Color(0xFF007AFF).withOpacity(0.9)
            : Colors.white.withOpacity(0.2),
        border: Border.all(
          color: isPrimary 
              ? const Color(0xFF007AFF).withOpacity(0.3)
              : Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isPrimary 
                ? const Color(0xFF007AFF).withOpacity(0.2)
                : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onPressed,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: DefaultTextStyle(
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isPrimary ? Colors.white : Colors.black.withOpacity(0.8),
              ),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}

/// 液体玻璃风格的选择项组件
class LiquidGlassSelectionItem extends StatelessWidget {
  final String emoji;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final Color? accentColor;
  final bool isSelected;

  const LiquidGlassSelectionItem({
    super.key,
    required this.emoji,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.accentColor,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            HapticFeedback.lightImpact();
            onTap();
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isSelected 
                  ? (accentColor ?? const Color(0xFF007AFF)).withOpacity(0.1)
                  : Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected 
                    ? (accentColor ?? const Color(0xFF007AFF)).withOpacity(0.3)
                    : Colors.white.withOpacity(0.3),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: isSelected 
                      ? (accentColor ?? const Color(0xFF007AFF)).withOpacity(0.1)
                      : Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: (accentColor ?? const Color(0xFF007AFF)).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: (accentColor ?? const Color(0xFF007AFF)).withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      emoji,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? (accentColor ?? const Color(0xFF007AFF))
                              : Colors.black.withOpacity(0.9),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.black.withOpacity(0.6),
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: accentColor ?? const Color(0xFF007AFF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 显示液体玻璃风格弹窗的便捷方法
class LiquidGlassDialogHelper {
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    Widget? content,
    List<Widget>? actions,
    bool barrierDismissible = true,
    VoidCallback? onDismiss,
    double? maxWidth,
    double? maxHeight,
    EdgeInsets? contentPadding,
    Widget? icon,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => LiquidGlassDialog(
        title: title,
        content: content,
        actions: actions,
        barrierDismissible: barrierDismissible,
        onDismiss: onDismiss,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        contentPadding: contentPadding,
        icon: icon,
        backgroundColor: backgroundColor,
        borderRadius: borderRadius,
      ),
    );
  }
} 