import 'package:flutter/material.dart';
import 'dart:math' as math;

class GoldenStarsBackground extends StatefulWidget {
  final int particleCount;
  final double particleSpread;
  final double speed;
  final bool moveParticlesOnHover;
  final double particleHoverFactor;
  final bool alphaParticles;
  final double particleBaseSize;
  final double sizeRandomness;
  final bool disableRotation;

  const GoldenStarsBackground({
    super.key,
    this.particleCount = 200,
    this.particleSpread = 10.0,
    this.speed = 0.1,
    this.moveParticlesOnHover = false,
    this.particleHoverFactor = 1.0,
    this.alphaParticles = true,
    this.particleBaseSize = 3.0,
    this.sizeRandomness = 1.0,
    this.disableRotation = false,
  });

  @override
  State<GoldenStarsBackground> createState() => _GoldenStarsBackgroundState();
}

class _GoldenStarsBackgroundState extends State<GoldenStarsBackground>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<StarParticle> _particles;
  Offset _mousePosition = Offset.zero;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    )..repeat();
    
    _initializeParticles();
  }

  void _initializeParticles() {
    _particles = List.generate(widget.particleCount, (index) {
      return StarParticle(
        position: Offset(
          math.Random().nextDouble() * 2 - 1,
          math.Random().nextDouble() * 2 - 1,
        ),
        velocity: Offset(
          (math.Random().nextDouble() - 0.5) * 0.02,
          (math.Random().nextDouble() - 0.5) * 0.02,
        ),
        size: widget.particleBaseSize * 
              (1.0 + widget.sizeRandomness * (math.Random().nextDouble() - 0.5)),
        opacity: widget.alphaParticles 
          ? math.Random().nextDouble() * 0.8 + 0.2
          : 1.0,
        rotationSpeed: (math.Random().nextDouble() - 0.5) * 0.05,
        rotation: math.Random().nextDouble() * 2 * math.pi,
        phase: math.Random().nextDouble() * 2 * math.pi,
        twinkleSpeed: math.Random().nextDouble() * 2 + 1,
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: widget.moveParticlesOnHover 
        ? (details) {
            setState(() {
              final RenderBox box = context.findRenderObject() as RenderBox;
              final localPosition = box.globalToLocal(details.globalPosition);
              _mousePosition = Offset(
                (localPosition.dx / box.size.width) * 2 - 1,
                (localPosition.dy / box.size.height) * 2 - 1,
              );
            });
          }
        : null,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return CustomPaint(
            painter: GoldenStarsPainter(
              particles: _particles,
              animationValue: _animationController.value,
              mousePosition: _mousePosition,
              moveParticlesOnHover: widget.moveParticlesOnHover,
              particleHoverFactor: widget.particleHoverFactor,
              speed: widget.speed,
              disableRotation: widget.disableRotation,
            ),
            size: Size.infinite,
          );
        },
      ),
    );
  }
}

class StarParticle {
  Offset position;
  Offset velocity;
  double size;
  double opacity;
  double rotationSpeed;
  double rotation;
  double phase;
  double twinkleSpeed;

  StarParticle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.opacity,
    required this.rotationSpeed,
    required this.rotation,
    required this.phase,
    required this.twinkleSpeed,
  });
}

class GoldenStarsPainter extends CustomPainter {
  final List<StarParticle> particles;
  final double animationValue;
  final Offset mousePosition;
  final bool moveParticlesOnHover;
  final double particleHoverFactor;
  final double speed;
  final bool disableRotation;

  GoldenStarsPainter({
    required this.particles,
    required this.animationValue,
    required this.mousePosition,
    required this.moveParticlesOnHover,
    required this.particleHoverFactor,
    required this.speed,
    required this.disableRotation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final time = animationValue * 60 * speed; // 60 seconds * speed
    
    for (final particle in particles) {
      _drawStar(canvas, size, particle, time, center);
    }
  }

  void _drawStar(Canvas canvas, Size size, StarParticle particle, double time, Offset center) {
    // 计算粒子位置
    var pos = particle.position;
    
    // 添加动画运动
    pos = Offset(
      pos.dx + math.sin(time * 0.001 + particle.phase) * 0.1,
      pos.dy + math.cos(time * 0.001 + particle.phase * 1.3) * 0.1,
    );
    
    // 鼠标悬停效果
    if (moveParticlesOnHover) {
      pos = Offset(
        pos.dx - mousePosition.dx * particleHoverFactor * 0.1,
        pos.dy - mousePosition.dy * particleHoverFactor * 0.1,
      );
    }
    
    // 转换到屏幕坐标
    final screenPos = Offset(
      center.dx + pos.dx * size.width * 0.4,
      center.dy + pos.dy * size.height * 0.4,
    );
    
    // 边界检查，让粒子在屏幕边缘循环
    final wrappedPos = Offset(
      screenPos.dx % size.width,
      screenPos.dy % size.height,
    );
    
    // 计算闪烁效果
    final twinkle = (math.sin(time * 0.001 * particle.twinkleSpeed + particle.phase) + 1) * 0.5;
    final currentOpacity = particle.opacity * (0.3 + 0.7 * twinkle);
    
    // 计算旋转
    final rotation = disableRotation 
      ? particle.rotation 
      : particle.rotation + time * 0.001 * particle.rotationSpeed;
    
    // 绘制星星
    _drawStarShape(canvas, wrappedPos, particle.size, currentOpacity, rotation);
  }

  void _drawStarShape(Canvas canvas, Offset position, double size, double opacity, double rotation) {
    final paint = Paint()
      ..color = Color.lerp(
        const Color(0xFFFFD700), // 金色
        const Color(0xFFFFA500), // 橙金色
        math.sin(rotation) * 0.5 + 0.5,
      )!.withValues(alpha: opacity)
      ..style = PaintingStyle.fill;
    
    canvas.save();
    canvas.translate(position.dx, position.dy);
    canvas.rotate(rotation);
    
    // 绘制五角星
    final path = Path();
    const numPoints = 5;
    const outerRadius = 1.0;
    const innerRadius = 0.4;
    
    for (int i = 0; i < numPoints * 2; i++) {
      final angle = (i * math.pi) / numPoints;
      final radius = (i % 2 == 0) ? outerRadius : innerRadius;
      final x = math.cos(angle) * radius * size;
      final y = math.sin(angle) * radius * size;
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    
    canvas.drawPath(path, paint);
    
    // 添加发光效果
    final glowPaint = Paint()
      ..color = const Color(0xFFFFD700).withValues(alpha: opacity * 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);
    
    canvas.drawPath(path, glowPaint);
    
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant GoldenStarsPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.mousePosition != mousePosition;
  }
}
