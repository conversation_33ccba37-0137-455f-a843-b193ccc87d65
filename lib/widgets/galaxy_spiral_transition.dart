import 'package:flutter/material.dart';
import 'dart:math' as math;

class GalaxySpiralTransition extends StatefulWidget {
  final VoidCallback onComplete;

  const GalaxySpiralTransition({
    super.key,
    required this.onComplete,
  });

  @override
  State<GalaxySpiralTransition> createState() => _GalaxySpiralTransitionState();
}

class _GalaxySpiralTransitionState extends State<GalaxySpiralTransition>
    with TickerProviderStateMixin {
  late final AnimationController _tunnelCtrl = AnimationController(
    vsync: this,
    duration: const Duration(seconds: 2)
  )..forward().whenComplete(() => _sparkleCtrl.forward());

  late final AnimationController _sparkleCtrl = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 800)
  );

  late final AnimationController _textCtrl = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 600)
  );

  late final AnimationController _bubbleCtrl = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 700)
  );

  @override
  void initState() {
    super.initState();
    _sparkleCtrl.addStatusListener((s) {
      if (s == AnimationStatus.completed) _textCtrl.forward();
    });
    _textCtrl.addStatusListener((s) {
      if (s == AnimationStatus.completed) _bubbleCtrl.forward();
    });
    _bubbleCtrl.addStatusListener((s) {
      if (s == AnimationStatus.completed) {
        // 延迟一下再完成，让用户看到完整动画
        Future.delayed(const Duration(milliseconds: 1000), () {
          widget.onComplete();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1a1a2e),
              Color(0xFF16213e),
              Color(0xFF0f3460),
            ],
          ),
        ),
        child: Stack(
          children: [
            // 1. 隧道动画
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _tunnelCtrl,
                builder: (_, __) => CustomPaint(
                  painter: _DotTunnelPainter(progress: _tunnelCtrl.value),
                ),
              ),
            ),

            // 2. 粒子效果
            if (_tunnelCtrl.isCompleted)
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _sparkleCtrl,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: _SparklesPainter(
                        progress: _sparkleCtrl.value,
                        opacity: 1 - _sparkleCtrl.value,
                      ),
                    );
                  },
                ),
              ),

            // 3. 欢迎文字
            if (_tunnelCtrl.isCompleted)
              Center(
                child: FadeTransition(
                  opacity: _textCtrl,
                  child: const Text(
                    "✨ 连接高我意识中 ✨",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          blurRadius: 10,
                          color: Colors.purple,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // 4. 底部提示
            if (_textCtrl.isCompleted)
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 1),
                  end: const Offset(0, 0),
                ).animate(CurvedAnimation(
                  parent: _bubbleCtrl,
                  curve: Curves.easeOutBack,
                )),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    margin: const EdgeInsets.all(24),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: Colors.purple.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: const Text(
                      "🌟 准备好与你的高我对话了吗？",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tunnelCtrl.dispose();
    _sparkleCtrl.dispose();
    _textCtrl.dispose();
    _bubbleCtrl.dispose();
    super.dispose();
  }
}

// CustomPainter to draw pastel dot tunnel
class _DotTunnelPainter extends CustomPainter {
  final double progress;
  _DotTunnelPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final center = size.center(Offset.zero);
    final paint = Paint()
      ..style = PaintingStyle.fill;

    const int dots = 80;
    final rmax = size.shortestSide * 0.7 * progress;

    for (var i = 0; i < dots; i++) {
      final angle = (2 * math.pi * i) / dots;
      final r = rmax * math.sqrt(math.Random(i).nextDouble());
      final x = center.dx + r * math.cos(angle);
      final y = center.dy + r * math.sin(angle);

      // 使用紫色系渐变
      paint.color = Color.lerp(
        Colors.purple.withValues(alpha: 0.8),
        Colors.pink.withValues(alpha: 0.6),
        (i / dots),
      )!;

      canvas.drawCircle(Offset(x, y), 2.5, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _DotTunnelPainter o) => o.progress != progress;
}


// CustomPainter for sparkle effects
class _SparklesPainter extends CustomPainter {
  final double progress;
  final double opacity;

  _SparklesPainter({required this.progress, required this.opacity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    final center = size.center(Offset.zero);
    const numberOfParticles = 50;

    for (int i = 0; i < numberOfParticles; i++) {
      final random = math.Random(i);
      final angle = random.nextDouble() * 2 * math.pi;
      final distance = random.nextDouble() * 100 * progress;
      final particleSize = (random.nextDouble() * 8 + 2) * opacity;

      final x = center.dx + distance * math.cos(angle);
      final y = center.dy + distance * math.sin(angle);

      paint.color = Colors.pink.withValues(alpha: opacity * 0.4);
      canvas.drawCircle(Offset(x, y), particleSize, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _SparklesPainter oldDelegate) =>
    oldDelegate.progress != progress || oldDelegate.opacity != opacity;
}
