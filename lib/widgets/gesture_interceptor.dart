import 'package:flutter/material.dart';

/// 手势拦截器组件
/// 防止iOS系统手势与应用内手势冲突
class GestureInterceptor extends StatelessWidget {
  final Widget child;
  final bool enableSystemGestures;
  final double edgeThreshold;

  const GestureInterceptor({
    super.key,
    required this.child,
    this.enableSystemGestures = true,
    this.edgeThreshold = 50.0,
  });

  @override
  Widget build(BuildContext context) {
    if (!enableSystemGestures) {
      return child;
    }

    return GestureDetector(
      // 拦截系统手势，防止左滑返回误触发
      onHorizontalDragStart: (details) {
        // 只在屏幕左边缘的滑动才允许系统手势
        if (details.globalPosition.dx > edgeThreshold) {
          // 阻止系统手势
          return;
        }
      },
      onHorizontalDragUpdate: (details) {
        // 阻止大部分水平滑动，只允许边缘滑动
        if (details.globalPosition.dx > edgeThreshold) {
          return;
        }
      },
      // 添加手势行为控制
      behavior: HitTestBehavior.translucent,
      child: child,
    );
  }
} 