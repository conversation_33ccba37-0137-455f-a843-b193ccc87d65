import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/theme/app_theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'liquid_glass_dialog.dart';

class HexagramSelector extends StatelessWidget {
  const HexagramSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AppStateProvider, SubscriptionService>(
      builder: (context, appState, subscriptionService, child) {
        print('🔄 HexagramSelector rebuild - isSubscribed: ${subscriptionService.isSubscribed}, tier: ${subscriptionService.currentTier}');
    
    return SizedBox(
      height: 220,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Hexagram background
          CustomPaint(
            size: const Si<PERSON>(220, 220),
            painter: He<PERSON><PERSON><PERSON><PERSON><PERSON>(
              color: AppTheme.primaryColor.withOpacity(0.2),
              strokeWidth: 2.0,
            ),
          ),
          
          // Single Card Option
          _buildSpreadOption(
            context,
            position: const Offset(0, -90),
            label: '单张牌',
            isSelected: appState.selectedSpreadType == SpreadType.single,
            isEnabled: true,
            onTap: () => appState.setSelectedSpreadType(SpreadType.single),
          ),

          // Three Card Option
          _buildSpreadOption(
            context,
            position: const Offset(78, -45),
            label: '三张牌',
            isSelected: appState.selectedSpreadType == SpreadType.three,
                isEnabled: true, // 允许点击，权限检查在后续页面进行
            onTap: () {
                appState.setSelectedSpreadType(SpreadType.three);
            },
          ),

          // Celtic Cross Option
          _buildSpreadOption(
            context,
            position: const Offset(78, 45),
            label: '凯尔特十字',
            isSelected: appState.selectedSpreadType == SpreadType.celtic,
                isEnabled: true, // 允许点击，权限检查在后续页面进行
            onTap: () {
                appState.setSelectedSpreadType(SpreadType.celtic);
            },
          ),
          
          // Center decoration
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: AppTheme.accentColor,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ).animate().fadeIn(duration: 600.ms).scale(
                begin: const Offset(0.5, 0.5),
                end: const Offset(1.0, 1.0),
                duration: 600.ms,
              ),
        ],
      ),
        );
      },
    );
  }

  Widget _buildSpreadOption(
    BuildContext context, {
    required Offset position,
    required String label,
    required bool isSelected,
    required bool isEnabled,
    required VoidCallback onTap,
  }) {
    return Positioned(
      left: 110 + position.dx,
      top: 110 + position.dy,
      child: GestureDetector(
        onTap: onTap,
        child: Stack(
          children: [
            Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.primaryColor
                    : isEnabled 
                        ? Theme.of(context).cardColor
                        : Colors.grey[300],
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
            label,
            style: TextStyle(
                      color: isSelected 
                          ? Colors.white 
                          : isEnabled 
                              ? null 
                              : Colors.grey[600],
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
                  if (!isEnabled) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.lock,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ).animate().fadeIn(duration: 300.ms).slideY(
              begin: 0.2,
              end: 0,
              duration: 300.ms,
              curve: Curves.easeOutQuad,
            ),
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context, String spreadName) {
    LiquidGlassDialogHelper.show(
      context: context,
      title: '会员功能',
      icon: const Icon(
        Icons.lock,
        color: Colors.orange,
        size: 24,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.orange.withValues(alpha: 0.1),
                  Colors.orange.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '免费会员只支持单张牌阵',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  '$spreadName需要升级到付费会员才能使用。升级后您将获得：',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '• 解锁所有专题牌阵\n• 每日多次AI深度解读\n• 详细占卜指引\n• 专业塔罗建议',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              '稍后再说',
              style: TextStyle(
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF6B46C1), Color(0xFF553C9A)],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF6B46C1).withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.white,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              '立即升级',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class HexagramPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;

  HexagramPainter({
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - strokeWidth;

    // Draw first triangle (pointing up)
    final path1 = Path();
    path1.moveTo(center.dx, center.dy - radius); // Top
    path1.lineTo(center.dx + radius * 0.866, center.dy + radius * 0.5); // Bottom right
    path1.lineTo(center.dx - radius * 0.866, center.dy + radius * 0.5); // Bottom left
    path1.close();
    canvas.drawPath(path1, paint);

    // Draw second triangle (pointing down)
    final path2 = Path();
    path2.moveTo(center.dx, center.dy + radius); // Bottom
    path2.lineTo(center.dx + radius * 0.866, center.dy - radius * 0.5); // Top right
    path2.lineTo(center.dx - radius * 0.866, center.dy - radius * 0.5); // Top left
    path2.close();
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
