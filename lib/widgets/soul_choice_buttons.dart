import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import '../utils/language_manager.dart';
import '../utils/language_manager_extension.dart';
import '../utils/language_patch.dart';

// 辅助函数：安全获取翻译文本
String _safeTranslate(LanguageManager languageManager, String key) {
  if (LanguagePatch.needsPatch(key)) {
    return LanguagePatch.getPatchedTranslation(key, languageManager.currentLanguage);
  }
  return languageManager.translate(key);
}

class SoulChoiceButtons extends StatelessWidget {
  final Function(String) onChoice;

  const SoulChoiceButtons({
    super.key,
    required this.onChoice,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            children: [
              // 🔧 新增：站在终点按钮（更换图标为莫比乌斯环）
              _buildChoiceButton(
                icon: '♾️',
                title: languageManager.translate('soul_from_end'),
                subtitle: languageManager.translate('soul_from_end_subtitle'),
                onTap: () => onChoice('endpoint'),
                gradient: const LinearGradient(
                  colors: [Color(0xFF10B981), Color(0xFF059669)],
                ),
              ),

              const SizedBox(height: 12),

              // 详细选择（分享支线）- 包含Explore Within和Share Joy
              _buildDetailedChoices(languageManager),
            ],
          ),
        );
      },
    );
  }

  Widget _buildChoiceButton({
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Gradient gradient,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: GestureDetector(
        onTap: onTap,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    icon,
                    style: const TextStyle(fontSize: 32),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.black87,
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      shadows: [
                        Shadow(
                          color: Colors.white.withValues(alpha: 0.8),
                          blurRadius: 1,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Colors.black54,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailedChoices(LanguageManager languageManager) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withValues(alpha: 0.2),
              Colors.white.withValues(alpha: 0.1),
            ],
          ),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getDetailTitle(languageManager),
              style: TextStyle(
                color: Colors.black87,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                    color: Colors.white.withValues(alpha: 0.8),
                    blurRadius: 1,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // 主要功能按钮
                _buildSmallButton('💭', languageManager.translate('soul_explore_inner'), () => onChoice('explore')),
                _buildSmallButton('✨', languageManager.translate('soul_share_joy'), () => onChoice('share')),
                // 分享细分选项（移除重复的emoji，因为翻译文本中已包含）
                _buildSmallButtonWithoutIcon(languageManager.translateWithPatch('share_growth_journey'), () => onChoice('growth')),
                _buildSmallButtonWithoutIcon(languageManager.translateWithPatch('share_effort_moments'), () => onChoice('effort')),
                _buildSmallButtonWithoutIcon(languageManager.translateWithPatch('share_grateful_memories'), () => onChoice('grateful')),
                _buildSmallButton('📝', languageManager.translate('soul_diary'), () => onChoice('diary')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSmallButton(String icon, String text, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.4),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(icon, style: const TextStyle(fontSize: 14)),
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                color: Colors.black87,
                fontSize: 12,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                    color: Colors.white.withValues(alpha: 0.8),
                    blurRadius: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSmallButtonWithoutIcon(String text, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.4),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          text,
          style: TextStyle(
            color: Colors.black87,
            fontSize: 12,
            fontWeight: FontWeight.w600,
            shadows: [
              Shadow(
                color: Colors.white.withValues(alpha: 0.8),
                blurRadius: 1,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTitle(String type, String language) {
    switch (type) {
      case 'explore':
        switch (language) {
          case 'zh-CN': return '探索内心';
          case 'zh-TW': return '探索內心';
          case 'en-US': return 'Explore Within';
          case 'ja-JP': return '内面探索';
          case 'ko-KR': return '내면 탐구';
          default: return '探索内心';
        }
      case 'share':
        switch (language) {
          case 'zh-CN': return '分享美好';
          case 'zh-TW': return '分享美好';
          case 'en-US': return 'Share Joy';
          case 'ja-JP': return '美しさを共有';
          case 'ko-KR': return '아름다움 공유';
          default: return '分享美好';
        }
      case 'endpoint':
        switch (language) {
          case 'zh-CN': return '站在终点';
          case 'zh-TW': return '站在終點';
          case 'en-US': return 'From the End';
          case 'ja-JP': return '終点から';
          case 'ko-KR': return '종점에서';
          default: return '站在终点';
        }
      default: return '';
    }
  }

  String _getSubtitle(String type, String language) {
    switch (type) {
      case 'explore':
        switch (language) {
          case 'zh-CN': return '困惑与指引';
          case 'zh-TW': return '困惑與指引';
          case 'en-US': return 'Confusion & Guidance';
          case 'ja-JP': return '困惑と導き';
          case 'ko-KR': return '혼란과 안내';
          default: return '困惑与指引';
        }
      case 'share':
        switch (language) {
          case 'zh-CN': return '获得夸夸';
          case 'zh-TW': return '獲得誇誇';
          case 'en-US': return 'Get Praised';
          case 'ja-JP': return '褒められる';
          case 'ko-KR': return '칭찬받기';
          default: return '获得夸夸';
        }
      case 'endpoint':
        switch (language) {
          case 'zh-CN': return '显化指导';
          case 'zh-TW': return '顯化指導';
          case 'en-US': return 'Manifestation Guide';
          case 'ja-JP': return '実現ガイド';
          case 'ko-KR': return '현실화 가이드';
          default: return '显化指导';
        }
      default: return '';
    }
  }

  String _getDetailTitle(LanguageManager languageManager) {
    return '✨ ${_safeTranslate(languageManager, 'what_to_share_today')}';
  }

  String _getDetailOption(String type, String language) {
    switch (type) {
      case 'happy':
        switch (language) {
          case 'zh-CN': return '开心的事';
          case 'zh-TW': return '開心的事';
          case 'en-US': return 'Happy moments';
          case 'ja-JP': return '嬉しいこと';
          case 'ko-KR': return '기쁜 일';
          default: return '开心的事';
        }
      case 'growth':
        switch (language) {
          case 'zh-CN': return '小小成长';
          case 'zh-TW': return '小小成長';
          case 'en-US': return 'Small growth';
          case 'ja-JP': return '小さな成長';
          case 'ko-KR': return '작은 성장';
          default: return '小小成长';
        }
      case 'effort':
        switch (language) {
          case 'zh-CN': return '努力瞬间';
          case 'zh-TW': return '努力瞬間';
          case 'en-US': return 'Effort moments';
          case 'ja-JP': return '努力の瞬間';
          case 'ko-KR': return '노력의 순간';
          default: return '努力瞬间';
        }
      case 'grateful':
        switch (language) {
          case 'zh-CN': return '感恩时刻';
          case 'zh-TW': return '感恩時刻';
          case 'en-US': return 'Grateful moments';
          case 'ja-JP': return '感謝の瞬間';
          case 'ko-KR': return '감사한 순간';
          default: return '感恩时刻';
        }
      case 'diary':
        switch (language) {
          case 'zh-CN': return '查看日记';
          case 'zh-TW': return '查看日記';
          case 'en-US': return 'View diary';
          case 'ja-JP': return '日記を見る';
          case 'ko-KR': return '일기 보기';
          default: return '查看日记';
        }
      default: return '';
    }
  }
}
