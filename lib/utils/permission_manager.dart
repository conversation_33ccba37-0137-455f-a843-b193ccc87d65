import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/language_manager.dart';

/// 权限管理器
/// 负责处理iOS和Android的各种权限请求
class PermissionManager {
  
  /// 请求相机权限
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }
  
  /// 请求相册权限
  static Future<bool> requestPhotosPermission() async {
    final status = await Permission.photos.request();
    return status == PermissionStatus.granted;
  }
  
  /// 请求通知权限
  static Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status == PermissionStatus.granted;
  }
  
  /// 检查相机权限状态
  static Future<PermissionStatus> getCameraPermissionStatus() async {
    return await Permission.camera.status;
  }
  
  /// 检查相册权限状态
  static Future<PermissionStatus> getPhotosPermissionStatus() async {
    return await Permission.photos.status;
  }
  
  /// 检查通知权限状态
  static Future<PermissionStatus> getNotificationPermissionStatus() async {
    return await Permission.notification.status;
  }
  
  /// 🔧 修复：显示权限被拒绝的对话框（集成语言管理）
  static void showPermissionDeniedDialog(
    BuildContext context,
    String permissionType, // 权限类型：camera, photos, notification
    String descriptionKey, // 描述的翻译键
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return AlertDialog(
              title: Text(languageManager.translate('permission_required_title_$permissionType')),
              content: Text(languageManager.translate(descriptionKey)),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(languageManager.translate('cancel')),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    openAppSettings();
                  },
                  child: Text(languageManager.translate('go_to_settings')),
                ),
              ],
            );
          },
        );
      },
    );
  }
  
  /// 检查并请求相机权限
  static Future<bool> checkAndRequestCameraPermission(BuildContext context) async {
    final status = await getCameraPermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.denied:
        final result = await requestCameraPermission();
        if (!result) {
          showPermissionDeniedDialog(
            context,
            'camera',
            'camera_permission_denied_message',
          );
        }
        return result;
      case PermissionStatus.permanentlyDenied:
        showPermissionDeniedDialog(
          context,
          'camera',
          'camera_permission_permanently_denied_message',
        );
        return false;
      default:
        return false;
    }
  }
  
  /// 检查并请求相册权限
  static Future<bool> checkAndRequestPhotosPermission(BuildContext context) async {
    final status = await getPhotosPermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.denied:
        final result = await requestPhotosPermission();
        if (!result) {
          showPermissionDeniedDialog(
            context,
            'photos',
            'photos_permission_denied_message',
          );
        }
        return result;
      case PermissionStatus.permanentlyDenied:
        showPermissionDeniedDialog(
          context,
          'photos',
          'photos_permission_permanently_denied_message',
        );
        return false;
      default:
        return false;
    }
  }
  
  /// 检查并请求通知权限
  static Future<bool> checkAndRequestNotificationPermission(BuildContext context) async {
    final status = await getNotificationPermissionStatus();
    
    switch (status) {
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.denied:
        final result = await requestNotificationPermission();
        if (!result) {
          showPermissionDeniedDialog(
            context,
            'notification',
            'notification_permission_denied_message',
          );
        }
        return result;
      case PermissionStatus.permanentlyDenied:
        showPermissionDeniedDialog(
          context,
          'notification',
          'notification_permission_permanently_denied_message',
        );
        return false;
      default:
        return false;
    }
  }
  
  /// 一次性请求所有必要权限
  static Future<Map<String, bool>> requestAllPermissions(BuildContext context) async {
    final results = <String, bool>{};
    
    // 请求相机权限
    results['camera'] = await checkAndRequestCameraPermission(context);
    
    // 请求相册权限
    results['photos'] = await checkAndRequestPhotosPermission(context);
    
    // 请求通知权限
    results['notification'] = await checkAndRequestNotificationPermission(context);
    
    return results;
  }
  
  /// 检查所有权限状态
  static Future<Map<String, PermissionStatus>> checkAllPermissions() async {
    return {
      'camera': await getCameraPermissionStatus(),
      'photos': await getPhotosPermissionStatus(),
      'notification': await getNotificationPermissionStatus(),
    };
  }
}
