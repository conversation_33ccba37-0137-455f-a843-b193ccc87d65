/// Figma资源管理器
/// 管理从Figma导出的图片、图标和其他资源
class FigmaAssets {
  // 塔罗牌图片路径
  static const String cardBackImage = 'assets/images/card_back.png';
  static const String cardFrameImage = 'assets/images/card_frame.png';
  
  // 大阿卡纳牌图片
  static const Map<String, String> majorArcanaImages = {
    'The Fool': 'assets/images/major_arcana/00_fool.png',
    'The Magician': 'assets/images/major_arcana/01_magician.png',
    'The High Priestess': 'assets/images/major_arcana/02_high_priestess.png',
    'The Empress': 'assets/images/major_arcana/03_empress.png',
    'The Emperor': 'assets/images/major_arcana/04_emperor.png',
    'The Hierophant': 'assets/images/major_arcana/05_hierophant.png',
    'The Lovers': 'assets/images/major_arcana/06_lovers.png',
    'The Chariot': 'assets/images/major_arcana/07_chariot.png',
    'Strength': 'assets/images/major_arcana/08_strength.png',
    'The Hermit': 'assets/images/major_arcana/09_hermit.png',
    'Wheel of Fortune': 'assets/images/major_arcana/10_wheel_of_fortune.png',
    'Justice': 'assets/images/major_arcana/11_justice.png',
    'The Hanged Man': 'assets/images/major_arcana/12_hanged_man.png',
    'Death': 'assets/images/major_arcana/13_death.png',
    'Temperance': 'assets/images/major_arcana/14_temperance.png',
    'The Devil': 'assets/images/major_arcana/15_devil.png',
    'The Tower': 'assets/images/major_arcana/16_tower.png',
    'The Star': 'assets/images/major_arcana/17_star.png',
    'The Moon': 'assets/images/major_arcana/18_moon.png',
    'The Sun': 'assets/images/major_arcana/19_sun.png',
    'Judgement': 'assets/images/major_arcana/20_judgement.png',
    'The World': 'assets/images/major_arcana/21_world.png',
  };

  // 小阿卡纳牌图片
  static const Map<String, String> minorArcanaImages = {
    // 圣杯牌组
    'Ace of Cups': 'assets/images/minor_arcana/cups/ace_cups.png',
    'Two of Cups': 'assets/images/minor_arcana/cups/two_cups.png',
    'Three of Cups': 'assets/images/minor_arcana/cups/three_cups.png',
    'Four of Cups': 'assets/images/minor_arcana/cups/four_cups.png',
    'Five of Cups': 'assets/images/minor_arcana/cups/five_cups.png',
    'Six of Cups': 'assets/images/minor_arcana/cups/six_cups.png',
    'Seven of Cups': 'assets/images/minor_arcana/cups/seven_cups.png',
    'Eight of Cups': 'assets/images/minor_arcana/cups/eight_cups.png',
    'Nine of Cups': 'assets/images/minor_arcana/cups/nine_cups.png',
    'Ten of Cups': 'assets/images/minor_arcana/cups/ten_cups.png',
    'Page of Cups': 'assets/images/minor_arcana/cups/page_cups.png',
    'Knight of Cups': 'assets/images/minor_arcana/cups/knight_cups.png',
    'Queen of Cups': 'assets/images/minor_arcana/cups/queen_cups.png',
    'King of Cups': 'assets/images/minor_arcana/cups/king_cups.png',
    
    // 权杖牌组
    'Ace of Wands': 'assets/images/minor_arcana/wands/ace_wands.png',
    'Two of Wands': 'assets/images/minor_arcana/wands/two_wands.png',
    'Three of Wands': 'assets/images/minor_arcana/wands/three_wands.png',
    'Four of Wands': 'assets/images/minor_arcana/wands/four_wands.png',
    'Five of Wands': 'assets/images/minor_arcana/wands/five_wands.png',
    'Six of Wands': 'assets/images/minor_arcana/wands/six_wands.png',
    'Seven of Wands': 'assets/images/minor_arcana/wands/seven_wands.png',
    'Eight of Wands': 'assets/images/minor_arcana/wands/eight_wands.png',
    'Nine of Wands': 'assets/images/minor_arcana/wands/nine_wands.png',
    'Ten of Wands': 'assets/images/minor_arcana/wands/ten_wands.png',
    'Page of Wands': 'assets/images/minor_arcana/wands/page_wands.png',
    'Knight of Wands': 'assets/images/minor_arcana/wands/knight_wands.png',
    'Queen of Wands': 'assets/images/minor_arcana/wands/queen_wands.png',
    'King of Wands': 'assets/images/minor_arcana/wands/king_wands.png',
    
    // 宝剑牌组
    'Ace of Swords': 'assets/images/minor_arcana/swords/ace_swords.png',
    'Two of Swords': 'assets/images/minor_arcana/swords/two_swords.png',
    'Three of Swords': 'assets/images/minor_arcana/swords/three_swords.png',
    'Four of Swords': 'assets/images/minor_arcana/swords/four_swords.png',
    'Five of Swords': 'assets/images/minor_arcana/swords/five_swords.png',
    'Six of Swords': 'assets/images/minor_arcana/swords/six_swords.png',
    'Seven of Swords': 'assets/images/minor_arcana/swords/seven_swords.png',
    'Eight of Swords': 'assets/images/minor_arcana/swords/eight_swords.png',
    'Nine of Swords': 'assets/images/minor_arcana/swords/nine_swords.png',
    'Ten of Swords': 'assets/images/minor_arcana/swords/ten_swords.png',
    'Page of Swords': 'assets/images/minor_arcana/swords/page_swords.png',
    'Knight of Swords': 'assets/images/minor_arcana/swords/knight_swords.png',
    'Queen of Swords': 'assets/images/minor_arcana/swords/queen_swords.png',
    'King of Swords': 'assets/images/minor_arcana/swords/king_swords.png',
    
    // 金币牌组
    'Ace of Pentacles': 'assets/images/minor_arcana/pentacles/ace_pentacles.png',
    'Two of Pentacles': 'assets/images/minor_arcana/pentacles/two_pentacles.png',
    'Three of Pentacles': 'assets/images/minor_arcana/pentacles/three_pentacles.png',
    'Four of Pentacles': 'assets/images/minor_arcana/pentacles/four_pentacles.png',
    'Five of Pentacles': 'assets/images/minor_arcana/pentacles/five_pentacles.png',
    'Six of Pentacles': 'assets/images/minor_arcana/pentacles/six_pentacles.png',
    'Seven of Pentacles': 'assets/images/minor_arcana/pentacles/seven_pentacles.png',
    'Eight of Pentacles': 'assets/images/minor_arcana/pentacles/eight_pentacles.png',
    'Nine of Pentacles': 'assets/images/minor_arcana/pentacles/nine_pentacles.png',
    'Ten of Pentacles': 'assets/images/minor_arcana/pentacles/ten_pentacles.png',
    'Page of Pentacles': 'assets/images/minor_arcana/pentacles/page_pentacles.png',
    'Knight of Pentacles': 'assets/images/minor_arcana/pentacles/knight_pentacles.png',
    'Queen of Pentacles': 'assets/images/minor_arcana/pentacles/queen_pentacles.png',
    'King of Pentacles': 'assets/images/minor_arcana/pentacles/king_pentacles.png',
  };

  // UI组件图片
  static const Map<String, String> uiImages = {
    'hexagram': 'assets/images/ui/hexagram.png',
    'star_pattern': 'assets/images/ui/star_pattern.png',
    'moon_phases': 'assets/images/ui/moon_phases.png',
    'crystal_ball': 'assets/images/ui/crystal_ball.png',
    'mystical_border': 'assets/images/ui/mystical_border.png',
    'gradient_overlay': 'assets/images/ui/gradient_overlay.png',
  };

  // 背景图片
  static const Map<String, String> backgroundImages = {
    'main_bg': 'assets/images/backgrounds/main_bg.png',
    'reading_bg': 'assets/images/backgrounds/reading_bg.png',
    'daily_bg': 'assets/images/backgrounds/daily_bg.png',
    'history_bg': 'assets/images/backgrounds/history_bg.png',
    'profile_bg': 'assets/images/backgrounds/profile_bg.png',
  };

  // 图标
  static const Map<String, String> iconImages = {
    'tarot_icon': 'assets/images/icons/tarot_icon.png',
    'crystal_icon': 'assets/images/icons/crystal_icon.png',
    'moon_icon': 'assets/images/icons/moon_icon.png',
    'star_icon': 'assets/images/icons/star_icon.png',
    'magic_icon': 'assets/images/icons/magic_icon.png',
  };

  /// 获取塔罗牌图片路径
  static String getCardImage(String cardName) {
    // 首先检查大阿卡纳
    if (majorArcanaImages.containsKey(cardName)) {
      return majorArcanaImages[cardName]!;
    }
    
    // 然后检查小阿卡纳
    if (minorArcanaImages.containsKey(cardName)) {
      return minorArcanaImages[cardName]!;
    }
    
    // 如果找不到，返回卡牌背面
    return cardBackImage;
  }

  /// 获取UI组件图片路径
  static String getUIImage(String imageName) {
    return uiImages[imageName] ?? '';
  }

  /// 获取背景图片路径
  static String getBackgroundImage(String imageName) {
    return backgroundImages[imageName] ?? '';
  }

  /// 获取图标路径
  static String getIconImage(String iconName) {
    return iconImages[iconName] ?? '';
  }

  /// 检查图片是否存在
  static bool hasCardImage(String cardName) {
    return majorArcanaImages.containsKey(cardName) || 
           minorArcanaImages.containsKey(cardName);
  }

  /// 获取所有可用的卡牌图片
  static List<String> getAllCardImages() {
    return [
      ...majorArcanaImages.values,
      ...minorArcanaImages.values,
    ];
  }

  /// 获取特定牌组的图片
  static List<String> getSuitImages(String suit) {
    switch (suit.toLowerCase()) {
      case 'major arcana':
        return majorArcanaImages.values.toList();
      case 'cups':
        return minorArcanaImages.values
            .where((path) => path.contains('cups'))
            .toList();
      case 'wands':
        return minorArcanaImages.values
            .where((path) => path.contains('wands'))
            .toList();
      case 'swords':
        return minorArcanaImages.values
            .where((path) => path.contains('swords'))
            .toList();
      case 'pentacles':
        return minorArcanaImages.values
            .where((path) => path.contains('pentacles'))
            .toList();
      default:
        return [];
    }
  }
}
