import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 收据验证测试工具
/// 用于测试不同场景下的收据验证逻辑
class ReceiptVerificationTest {
  
  /// 测试收据验证服务的可用性
  static Future<bool> testVerificationService() async {
    try {
      debugPrint('🧪 开始测试收据验证服务...');
      
      // 发送一个测试请求（使用无效收据数据）
      final response = await Supabase.instance.client.functions.invoke(
        'verify-receipt',
        body: {
          'receiptData': 'test_invalid_receipt_data',
        },
      );
      
      debugPrint('📡 测试响应状态: ${response.status}');
      debugPrint('📦 测试响应数据: ${response.data}');
      
      // 如果服务可用，应该返回400或200状态码
      if (response.status == 400 || response.status == 200) {
        debugPrint('✅ 收据验证服务可用');
        return true;
      } else {
        debugPrint('❌ 收据验证服务不可用: ${response.status}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 测试收据验证服务异常: $e');
      return false;
    }
  }
  
  /// 模拟不同的Apple状态码响应
  static Map<int, String> getAppleStatusCodeMeanings() {
    return {
      0: '验证成功',
      21000: 'App Store无法读取提供的JSON对象',
      21002: 'receipt-data属性中的数据格式错误或缺失',
      21003: '收据无法验证',
      21004: '提供的共享密钥与账户文件中的共享密钥不匹配',
      21005: '收据服务器暂时无法提供收据',
      21006: '此收据有效，但订阅已过期',
      21007: '此收据来自沙盒环境，但发送到了生产环境进行验证',
      21008: '此收据来自生产环境，但发送到了沙盒环境进行验证',
      21010: '此收据无法验证，请重新验证',
    };
  }
  
  /// 检查状态码是否应该允许购买继续
  static bool shouldAllowPurchase(int statusCode) {
    // 成功状态码
    if (statusCode == 0) return true;
    
    // 允许的状态码（临时错误或特殊情况）
    final allowedCodes = [21005, 21006, 21007];
    if (allowedCodes.contains(statusCode)) return true;
    
    // 严重错误状态码，拒绝购买
    final criticalErrors = [21000, 21002, 21003, 21004, 21008, 21010];
    if (criticalErrors.contains(statusCode)) return false;
    
    // 未知状态码，保守处理
    return false;
  }
  
  /// 生成收据验证测试报告
  static String generateTestReport() {
    final statusCodes = getAppleStatusCodeMeanings();
    final buffer = StringBuffer();
    
    buffer.writeln('📋 收据验证状态码处理策略');
    buffer.writeln('=' * 50);
    
    for (final entry in statusCodes.entries) {
      final code = entry.key;
      final meaning = entry.value;
      final shouldAllow = shouldAllowPurchase(code);
      final action = shouldAllow ? '✅ 允许' : '❌ 拒绝';
      
      buffer.writeln('$code: $meaning - $action');
    }
    
    buffer.writeln('\n🔧 处理策略说明:');
    buffer.writeln('✅ 允许: 验证成功或临时错误，用户可以继续使用');
    buffer.writeln('❌ 拒绝: 严重验证错误，需要用户重新购买或联系客服');
    
    return buffer.toString();
  }
  
  /// 验证Edge Function的部署状态
  static Future<Map<String, dynamic>> checkEdgeFunctionStatus() async {
    final result = <String, dynamic>{
      'isAvailable': false,
      'responseTime': 0,
      'error': null,
    };
    
    try {
      final startTime = DateTime.now();
      
      final response = await Supabase.instance.client.functions.invoke(
        'verify-receipt',
        body: {'receiptData': 'health_check'},
      );
      
      final endTime = DateTime.now();
      result['responseTime'] = endTime.difference(startTime).inMilliseconds;
      
      if (response.status == 400 || response.status == 200) {
        result['isAvailable'] = true;
        result['status'] = response.status;
        result['data'] = response.data;
      } else {
        result['error'] = 'HTTP ${response.status}';
      }
    } catch (e) {
      result['error'] = e.toString();
    }
    
    return result;
  }
  
  /// 打印完整的测试报告
  static Future<void> runFullTest() async {
    if (!kDebugMode) {
      debugPrint('⚠️ 收据验证测试仅在调试模式下运行');
      return;
    }
    
    debugPrint('🧪 开始完整收据验证测试...');
    debugPrint('=' * 60);
    
    // 1. 测试Edge Function状态
    debugPrint('1️⃣ 测试Edge Function状态...');
    final functionStatus = await checkEdgeFunctionStatus();
    debugPrint('   可用性: ${functionStatus['isAvailable']}');
    debugPrint('   响应时间: ${functionStatus['responseTime']}ms');
    if (functionStatus['error'] != null) {
      debugPrint('   错误: ${functionStatus['error']}');
    }
    
    // 2. 打印状态码处理策略
    debugPrint('\n2️⃣ 状态码处理策略:');
    final report = generateTestReport();
    debugPrint(report);
    
    // 3. 测试服务可用性
    debugPrint('\n3️⃣ 测试服务可用性...');
    final isServiceAvailable = await testVerificationService();
    debugPrint('   服务状态: ${isServiceAvailable ? "✅ 可用" : "❌ 不可用"}');
    
    debugPrint('\n✅ 收据验证测试完成');
    debugPrint('=' * 60);
  }
}
