import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Apple ID登录管理器
/// 负责处理Apple ID登录、注销和状态管理
class AppleSignInManager extends ChangeNotifier {
  static const String _userIdKey = 'apple_user_id';
  static const String _userEmailKey = 'apple_user_email';
  static const String _userNameKey = 'apple_user_name';
  static const String _isSignedInKey = 'apple_is_signed_in';
  
  // 用户登录状态
  bool _isSignedIn = false;
  String? _userId;
  String? _userEmail;
  String? _userName;
  
  // Getters
  bool get isSignedIn => _isSignedIn;
  String? get userId => _userId;
  String? get userEmail => _userEmail;
  String? get userName => _userName;
  
  /// 初始化Apple登录管理器
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _isSignedIn = prefs.getBool(_isSignedInKey) ?? false;
    _userId = prefs.getString(_userIdKey);
    _userEmail = prefs.getString(_userEmailKey);
    _userName = prefs.getString(_userNameKey);
    notifyListeners();
  }
  
  /// 检查Apple ID登录是否可用
  static Future<bool> isAppleSignInAvailable() async {
    try {
      return await SignInWithApple.isAvailable();
    } catch (e) {
      print('检查Apple登录可用性失败: $e');
      return false;
    }
  }
  
  /// 执行Apple ID登录
  Future<bool> signInWithApple() async {
    try {
      // 检查是否支持Apple登录
      if (!await isAppleSignInAvailable()) {
        throw Exception('此设备不支持Apple ID登录');
      }
      
      // 请求Apple ID登录
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      
      // 保存用户信息
      _userId = credential.userIdentifier;
      _userEmail = credential.email;
      _userName = _buildUserName(credential.givenName, credential.familyName);
      _isSignedIn = true;
      
      // 保存到本地存储
      await _saveUserData();
      
      notifyListeners();
      return true;
      
    } catch (e) {
      print('Apple ID登录失败: $e');
      return false;
    }
  }
  
  /// 注销Apple ID
  Future<void> signOut() async {
    _isSignedIn = false;
    _userId = null;
    _userEmail = null;
    _userName = null;
    
    // 清除本地存储
    await _clearUserData();
    
    notifyListeners();
  }
  
  /// 构建用户姓名
  String? _buildUserName(String? givenName, String? familyName) {
    if (givenName != null && familyName != null) {
      return '$givenName $familyName';
    } else if (givenName != null) {
      return givenName;
    } else if (familyName != null) {
      return familyName;
    }
    return null;
  }
  
  /// 保存用户数据到本地存储
  Future<void> _saveUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isSignedInKey, _isSignedIn);
    if (_userId != null) await prefs.setString(_userIdKey, _userId!);
    if (_userEmail != null) await prefs.setString(_userEmailKey, _userEmail!);
    if (_userName != null) await prefs.setString(_userNameKey, _userName!);
  }
  
  /// 清除用户数据
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_isSignedInKey);
    await prefs.remove(_userIdKey);
    await prefs.remove(_userEmailKey);
    await prefs.remove(_userNameKey);
  }
  
  /// 获取用户显示名称
  String getDisplayName() {
    if (_userName != null && _userName!.isNotEmpty) {
      return _userName!;
    } else if (_userEmail != null && _userEmail!.isNotEmpty) {
      return _userEmail!.split('@').first;
    } else {
      return 'Apple用户';
    }
  }
  
  /// 获取登录状态文本
  String getStatusText() {
    if (_isSignedIn) {
      return '已登录';
    } else {
      return '未登录';
    }
  }
  
  /// 验证登录状态
  Future<bool> validateSignInStatus() async {
    if (!_isSignedIn || _userId == null) {
      return false;
    }
    
    try {
      // 这里可以添加服务器验证逻辑
      // 目前只检查本地状态
      return true;
    } catch (e) {
      print('验证登录状态失败: $e');
      await signOut();
      return false;
    }
  }
  
  /// 显示登录错误对话框
  static void showSignInErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 8),
              Text('登录失败'),
            ],
          ),
          content: Text(error),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
  
  /// 显示登录成功对话框
  static void showSignInSuccessDialog(BuildContext context, String userName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green),
              SizedBox(width: 8),
              Text('登录成功'),
            ],
          ),
          content: Text('欢迎回来，$userName！'),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
