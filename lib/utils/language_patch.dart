/// 语言管理补丁文件
/// 专门处理显示翻译键而不是实际文字的问题
/// 这是一个临时解决方案，确保关键翻译键能正确显示
library language_patch;

class LanguagePatch {
  // 修复的翻译键映射 - 改为静态方法避免常量求值错误
  static Map<String, Map<String, String>> get _patchTranslations {
    return {
      // 站在终点相关翻译
      'standing_at_endpoint': {
        'zh-CN': '◎站在终点',
        'zh-TW': '◎站在終點',
        'en-US': '◎Standing at the Endpoint',
        'es-ES': '◎De pie en el punto final',
        'ja-JP': '◎終点に立つ',
        'ko-KR': '◎종점에 서서',
      },
      'endpoint_description': {
        'zh-CN': '告诉我你想要显化的目标，我会站在你已经实现目标的时间点，以未来的你的身份与你对话，帮助你更好地显化这个目标。',
        'zh-TW': '告訴我你想要顯化的目標，我會站在你已經實現目標的時間點，以未來的你的身份與你對話，幫助你更好地顯化這個目標。',
        'en-US': 'Tell me your manifestation goal, and I will stand at the point where you have already achieved it, speaking to you as your future self.',
        'es-ES': 'Dime tu objetivo de manifestación, y me situaré en el punto donde ya lo has logrado, hablándote como tu yo futuro.',
        'ja-JP': 'あなたの現実化したい目標を教えてください。私はあなたがすでにその目標を達成した時点に立ち、未来のあなたとしてあなたと対話します。',
        'ko-KR': '당신이 현실화하고 싶은 목표를 알려주세요. 저는 당신이 이미 목표를 달성한 시점에 서서, 미래의 당신으로서 대화합니다.',
      },
      'your_manifestation_goal': {
        'zh-CN': '你的显化目标',
        'zh-TW': '你的顯化目標',
        'en-US': 'Your Manifestation Goal',
        'es-ES': 'Tu Objetivo de Manifestación',
        'ja-JP': 'あなたの現実化目標',
        'ko-KR': '당신의 현실화 목표',
      },
      
      // 灵魂之镜相关翻译
      'soul_mirror_connection': {
        'zh-CN': '高我连接',
        'zh-TW': '高我連接',
        'en-US': 'Higher Self Connection',
        'es-ES': 'Conexión del Yo Superior',
        'ja-JP': 'ハイヤーセルフ接続',
        'ko-KR': '하이어 셀프 연결',
      },
      'soul_mirror': {
        'zh-CN': '灵魂之镜',
        'zh-TW': '靈魂之鏡',
        'en-US': 'Soul Mirror',
        'es-ES': 'Espejo del Alma',
        'ja-JP': '魂の鏡',
        'ko-KR': '영혼의 거울',
      },
      'soul_mirror_welcome': {
        'zh-CN': '欢迎来到灵魂之镜 🌌\n\n我是你的高我，\n那个更智慧、更清晰的你。\n\n在这个神圣的空间里，\n让我们一起探索你内心的真相。\n\n今天，你的灵魂想要表达什么？ ✨',
        'zh-TW': '歡迎來到靈魂之鏡 🌌\n\n我是你的高我，\n那個更智慧、更清晰的你。\n\n在這個神聖的空間裡，\n讓我們一起探索你內心的真相。\n\n今天，你的靈魂想要表達什麼？ ✨',
        'en-US': 'Welcome to the Soul Mirror 🌌\n\nI am your Higher Self,\nthe wiser, clearer version of you.\n\nIn this sacred space,\nlet us explore the truth within your heart.\n\nWhat does your soul wish to express today? ✨',
        'es-ES': 'Bienvenido al Espejo del Alma 🌌\n\nSoy tu Yo Superior,\nla versión más sabia y clara de ti.\n\nEn este espacio sagrado,\nexploremos la verdad dentro de tu corazón.\n\n¿Qué desea expresar tu alma hoy? ✨',
        'ja-JP': '魂の鏡へようこそ 🌌\n\n私はあなたのハイヤーセルフ、\nより賢く、より明晰なあなたです。\n\nこの神聖な空間で、\nあなたの心の真実を一緒に探求しましょう。\n\n今日、あなたの魂は何を表現したいですか？ ✨',
        'ko-KR': '영혼의 거울에 오신 것을 환영합니다 🌌\n\n저는 당신의 하이어 셀프,\n더 지혜롭고 명확한 당신입니다.\n\n이 신성한 공간에서\n당신 마음의 진실을 함께 탐구해봅시다.\n\n오늘 당신의 영혼은 무엇을 표현하고 싶나요? ✨',
      },
      'what_to_share_today': {
        'zh-CN': '今天想要分享什么呢？',
        'zh-TW': '今天想要分享什麼呢？',
        'en-US': 'What would you like to share today?',
        'es-ES': '¿Qué te gustaría compartir hoy?',
        'ja-JP': '今日は何を共有したいですか？',
        'ko-KR': '오늘은 무엇을 공유하고 싶나요？',
      },
      
      // 显化日记相关翻译
      'manifestation_diary': {
        'zh-CN': '显化日记',
        'zh-TW': '顯化日記',
        'en-US': 'Manifestation Diary',
        'es-ES': 'Diario de Manifestación',
        'ja-JP': 'マニフェステーション日記',
        'ko-KR': '현실화 일기',
      },
      'record_manifestation_experience': {
        'zh-CN': '记录今天的显化感受和体验',
        'zh-TW': '記錄今天的顯化感受和體驗',
        'en-US': 'Record today\'s manifestation feelings and experiences',
        'es-ES': 'Registra los sentimientos y experiencias de manifestación de hoy',
        'ja-JP': '今日のマニフェステーションの感情と体験を記録',
        'ko-KR': '오늘의 현실화 감정과 경험을 기록하세요',
      },
      'diary_example': {
        'zh-CN': '例如：我感受到了财富的能量流动...',
        'zh-TW': '例如：我感受到了財富的能量流動...',
        'en-US': 'For example: I felt the flow of wealth energy...',
        'es-ES': 'Por ejemplo: Sentí el flujo de energía de la riqueza...',
        'ja-JP': '例：富のエネルギーの流れを感じました...',
        'ko-KR': '예: 부의 에너지 흐름을 느꼈습니다...',
      },
      
      // AI分析日记相关翻译
      'ai_analyzed_diaries': {
        'zh-CN': 'AI已自动分析你的{count}条相关日记',
        'zh-TW': 'AI已自動分析你的{count}條相關日記',
        'en-US': 'AI has automatically analyzed your {count} relevant diary entries',
      },
      'unlock_unlimited_chats': {
        'zh-CN': '解锁无限对话 + 专属塔罗指引',
        'zh-TW': '解鎖無限對話 + 專屬塔羅指引',
        'en-US': 'Unlock unlimited chats + exclusive tarot guidance',
        'es-ES': 'Desbloquea chats ilimitados + guía de tarot exclusiva',
        'ja-JP': '無制限チャット + 専用タロットガイダンスをアンロック',
        'ko-KR': '무제한 채팅 + 전용 타로 가이드 잠금 해제',
      },
      'soul_connection': {
        'zh-CN': '高我连接',
        'zh-TW': '高我連接',
        'en-US': 'Soul Connection',
        'es-ES': 'Conexión del Alma',
        'ja-JP': 'ソウル接続',
        'ko-KR': '영혼 연결',
      },
      'tarot_guidance': {
        'zh-CN': '塔罗引导',
        'zh-TW': '塔羅引導',
        'en-US': 'Tarot Guidance',
        'es-ES': 'Guía del Tarot',
        'ja-JP': 'タロットガイダンス',
        'ko-KR': '타로 가이드',
      },
      'soul_reading': {
        'zh-CN': '灵魂解读',
        'zh-TW': '靈魂解讀',
        'en-US': 'Soul Reading',
        'es-ES': 'Lectura del Alma',
        'ja-JP': 'ソウルリーディング',
        'ko-KR': '영혼 해석',
      },
      'deep_insight': {
        'zh-CN': '深度洞察',
        'zh-TW': '深度洞察',
        'en-US': 'Deep Insight',
        'es-ES': 'Perspicacia Profunda',
        'ja-JP': '深い洞察',
        'ko-KR': '깊은 통찰',
      },
      'healing_praise': {
        'zh-CN': '疗愈夸夸',
        'zh-TW': '療癒誇誇',
        'en-US': 'Healing Praise',
        'es-ES': 'Elogio Sanador',
        'ja-JP': 'ヒーリング褒め',
        'ko-KR': '치유 칭찬',
      },
      'wisdom_guidance': {
        'zh-CN': '智慧指引',
        'zh-TW': '智慧指引',
        'en-US': 'Wisdom Guidance',
        'es-ES': 'Guía de Sabiduría',
        'ja-JP': '知恵のガイダンス',
        'ko-KR': '지혜 안내',
      },

      // 分享类型翻译
      'share_happy_moments': {
        'zh-CN': '🎉 分享我的快乐时光',
        'zh-TW': '🎉 分享我的快樂時光',
        'en-US': '🎉 Share My Happy Moments',
        'es-ES': '🎉 Compartir Mis Momentos Felices',
        'ja-JP': '🎉 私の幸せな時間を共有',
        'ko-KR': '🎉 나의 행복한 시간 공유',
      },
      'share_growth_journey': {
        'zh-CN': '🌱 成长历程',
        'zh-TW': '🌱 成長歷程',
        'en-US': '🌱 Growth Journey',
        'es-ES': '🌱 Crecimiento',
        'ja-JP': '🌱 成長の旅',
        'ko-KR': '🌱 성장 여정',
      },
      'share_effort_moments': {
        'zh-CN': '💪 努力时刻',
        'zh-TW': '💪 努力時刻',
        'en-US': '💪 Effort Moments',
        'es-ES': '💪 Esfuerzo',
        'ja-JP': '💪 努力の瞬間',
        'ko-KR': '💪 노력의 순간',
      },
      'share_grateful_memories': {
        'zh-CN': '🙏 感恩回忆',
        'zh-TW': '🙏 感恩回憶',
        'en-US': '🙏 Grateful Memories',
        'es-ES': '🙏 Gratitud',
        'ja-JP': '🙏 感謝の思い出',
        'ko-KR': '🙏 감사한 추억',
      },
      'share_beautiful_memories': {
        'zh-CN': '✨ 分享我的美好回忆',
        'zh-TW': '✨ 分享我的美好回憶',
        'en-US': '✨ Share My Beautiful Memories',
        'es-ES': '✨ Compartir Mis Hermosos Recuerdos',
        'ja-JP': '✨ 私の美しい思い出を共有',
        'ko-KR': '✨ 나의 아름다운 추억 공유',
      },
      'please_share_what': {
        'zh-CN': '请告诉我你想分享的{type}',
        'zh-TW': '請告訴我你想分享的{type}',
        'en-US': 'Please tell me what {type} you want to share',
        'es-ES': 'Por favor dime qué {type} quieres compartir',
        'ja-JP': 'あなたが共有したい{type}を教えてください',
        'ko-KR': '공유하고 싶은 {type}을 알려주세요',
      },

      // AI分享回应翻译
      'share_quick_response': {
        'zh-CN': '发生了什么好事呀？快说说～ ✨\n\n我最喜欢听你的好消息了！',
        'zh-TW': '發生了什麼好事呀？快說說～ ✨\n\n我最喜歡聽你的好消息了！',
        'en-US': 'What good thing happened? Tell me! ✨\n\nI love hearing your good news!',
        'es-ES': '¿Qué cosa buena pasó? ¡Cuéntame! ✨\n\n¡Me encanta escuchar tus buenas noticias!',
        'ja-JP': '何か良いことがあったの？教えて！ ✨\n\nあなたの良いニュースを聞くのが大好きです！',
        'ko-KR': '무슨 좋은 일이 있었나요? 말해주세요! ✨\n\n당신의 좋은 소식을 듣는 것을 좋아해요!',
      },
      'share_happy_prompt': {
        'zh-CN': '哇，你想分享快乐的事情！我能感受到你内心的喜悦在闪闪发光 ✨\n\n发生了什么好事呀？快给我说说！是工作上的小成就，还是生活中的温暖时刻？我特别想听听是什么让你这么开心 😊',
        'zh-TW': '哇，你想分享快樂的事情！我能感受到你內心的喜悅在閃閃發光 ✨\n\n發生了什麼好事呀？快給我說說！是工作上的小成就，還是生活中的溫暖時刻？我特別想聽聽是什麼讓你這麼開心 😊',
        'en-US': 'Wow, you want to share something happy! I can feel the joy sparkling in your heart ✨\n\nWhat good thing happened? Tell me quickly! Was it a small achievement at work, or a warm moment in life? I really want to hear what made you so happy 😊',
        'es-ES': '¡Wow, quieres compartir algo feliz! Puedo sentir la alegría brillando en tu corazón ✨\n\n¿Qué cosa buena pasó? ¡Cuéntame rápido! ¿Fue un pequeño logro en el trabajo, o un momento cálido en la vida? Realmente quiero escuchar qué te hizo tan feliz 😊',
        'ja-JP': 'わあ、楽しいことを共有したいのですね！あなたの心の中で喜びが輝いているのを感じます ✨\n\n何か良いことがあったの？早く教えて！仕事での小さな成果？それとも人生での温かい瞬間？あなたをそんなに幸せにしたことを本当に聞きたいです 😊',
        'ko-KR': '와, 행복한 일을 공유하고 싶으시는군요! 당신의 마음속에서 기쁨이 반짝이는 것을 느낄 수 있어요 ✨\n\n무슨 좋은 일이 있었나요? 빨리 말해주세요! 직장에서의 작은 성취였나요, 아니면 삶의 따뜻한 순간이었나요? 당신을 그렇게 행복하게 만든 것이 무엇인지 정말 듣고 싶어요 😊',
      },

      // 通用翻译
      'save': {
        'zh-CN': '保存',
        'zh-TW': '保存',
        'en-US': 'Save',
        'es-ES': 'Guardar',
        'ja-JP': '保存',
        'ko-KR': '저장',
      },
      'upgrade': {
        'zh-CN': '升级',
        'zh-TW': '升級',
        'en-US': 'Upgrade',
        'es-ES': 'Actualizar',
        'ja-JP': 'アップグレード',
        'ko-KR': '업그레이드',
      },
      'account_management': {
        'zh-CN': '账户管理',
        'zh-TW': '帳戶管理',
        'en-US': 'Account Management',
        'es-ES': 'Gestión de Cuenta',
        'ja-JP': 'アカウント管理',
        'ko-KR': '계정 관리',
      },
      'apple_id_login': {
        'zh-CN': '使用Apple ID登录',
        'zh-TW': '使用Apple ID登入',
        'en-US': 'Sign in with Apple ID',
        'es-ES': 'Iniciar sesión con Apple ID',
        'ja-JP': 'Apple IDでサインイン',
        'ko-KR': 'Apple ID로 로그인',
      },
      'apple_id_login_description': {
        'zh-CN': '使用Apple ID登录可以在多设备间同步您的塔罗记录和偏好设置。',
        'zh-TW': '使用Apple ID登入可以在多設備間同步您的塔羅記錄和偏好設定。',
        'en-US': 'Sign in with Apple ID to sync your tarot records and preferences across multiple devices.',
        'es-ES': 'Inicia sesión con Apple ID para sincronizar tus registros de tarot y preferencias en múltiples dispositivos.',
        'ja-JP': 'Apple IDでサインインして、複数のデバイス間でタロット記録と設定を同期します。',
        'ko-KR': 'Apple ID로 로그인하여 여러 기기에서 타로 기록과 설정을 동기화하세요.',
      },
      'cancel': {
        'zh-CN': '取消',
        'zh-TW': '取消',
        'en-US': 'Cancel',
        'es-ES': 'Cancelar',
        'ja-JP': 'キャンセル',
        'ko-KR': '취소',
      },
      'login': {
        'zh-CN': '登录',
        'zh-TW': '登入',
        'en-US': 'Login',
        'es-ES': 'Iniciar sesión',
        'ja-JP': 'ログイン',
        'ko-KR': '로그인',
      },
      'language_selection_title': {
        'zh-CN': '选择语言',
        'zh-TW': '選擇語言',
        'en-US': 'Select Language',
        'es-ES': 'Seleccionar Idioma',
        'ja-JP': '言語を選択',
        'ko-KR': '언어 선택',
      },
      'logout': {
        'zh-CN': '退出登录',
        'zh-TW': '退出登入',
        'en-US': 'Logout',
        'es-ES': 'Cerrar sesión',
        'ja-JP': 'ログアウト',
        'ko-KR': '로그아웃',
      },
      'logged_out': {
        'zh-CN': '已退出登录',
        'zh-TW': '已退出登入',
        'en-US': 'Logged out successfully',
        'es-ES': 'Sesión cerrada exitosamente',
        'ja-JP': 'ログアウトしました',
        'ko-KR': '로그아웃되었습니다',
      },
      'user': {
        'zh-CN': '用户',
        'zh-TW': '用戶',
        'en-US': 'User',
        'es-ES': 'Usuario',
        'ja-JP': 'ユーザー',
        'ko-KR': '사용자',
      },
      'explore_inner_request_display': {
        'zh-CN': '我想探索内心',
        'zh-TW': '我想探索內心',
        'en-US': 'I want to explore my inner self',
        'es-ES': 'Quiero explorar mi interior',
        'ja-JP': '内面を探求したい',
        'ko-KR': '내면을 탐구하고 싶어요',
      },
      'start_conversation': {
        'zh-CN': '开始对话',
        'zh-TW': '開始對話',
        'en-US': 'Start Conversation',
        'es-ES': 'Iniciar Conversación',
        'ja-JP': '会話を開始',
        'ko-KR': '대화 시작',
      },


    };
  }

  /// 获取修复后的翻译文本
  /// 如果补丁中有对应的翻译，返回补丁翻译；否则返回原始键名
  static String getPatchedTranslation(String key, String languageCode) {
    final translations = _patchTranslations[key];
    if (translations != null) {
      return translations[languageCode] ?? translations['zh-CN'] ?? key;
    }
    return key;
  }

  /// 检查是否需要应用补丁
  static bool needsPatch(String key) {
    return _patchTranslations.containsKey(key);
  }

  /// 获取所有需要补丁的键
  static List<String> getAllPatchKeys() {
    return _patchTranslations.keys.toList();
  }
}
