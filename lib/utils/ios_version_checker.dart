import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';

/// iOS版本检测工具
/// 用于检测iOS版本并判断是否支持VisionKit功能
class IOSVersionChecker {
  static const MethodChannel _channel = MethodChannel('ios_version_checker');
  
  /// 检查是否为iOS平台
  static bool get isIOS => Platform.isIOS;
  
  /// 获取iOS版本信息
  static Future<IOSVersionInfo> getIOSVersion() async {
    if (!isIOS) {
      return IOSVersionInfo(
        majorVersion: 0,
        minorVersion: 0,
        patchVersion: 0,
        isSupported: false,
        versionString: 'Not iOS',
      );
    }
    
    try {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      
      // 解析版本号
      final versionParts = iosInfo.systemVersion.split('.');
      final majorVersion = int.tryParse(versionParts[0]) ?? 0;
      final minorVersion = int.tryParse(versionParts.length > 1 ? versionParts[1] : '0') ?? 0;
      final patchVersion = int.tryParse(versionParts.length > 2 ? versionParts[2] : '0') ?? 0;
      
      return IOSVersionInfo(
        majorVersion: majorVersion,
        minorVersion: minorVersion,
        patchVersion: patchVersion,
        isSupported: majorVersion >= 17, // iOS 17+支持VisionKit抠图
        versionString: iosInfo.systemVersion,
        deviceModel: iosInfo.model,
        deviceName: iosInfo.name,
      );
    } catch (e) {
      print('❌ 获取iOS版本信息失败: $e');
      return IOSVersionInfo(
        majorVersion: 0,
        minorVersion: 0,
        patchVersion: 0,
        isSupported: false,
        versionString: 'Unknown',
      );
    }
  }
  
  /// 检查是否支持VisionKit抠图功能
  static Future<bool> supportsVisionKitBackgroundRemoval() async {
    if (!isIOS) return false;
    
    final versionInfo = await getIOSVersion();
    return versionInfo.isSupported;
  }
  
  /// 调用原生VisionKit抠图功能
  static Future<String?> removeBackgroundWithVisionKit(String imagePath) async {
    if (!isIOS) {
      throw UnsupportedError('VisionKit只在iOS平台可用');
    }
    
    final versionInfo = await getIOSVersion();
    if (!versionInfo.isSupported) {
      throw UnsupportedError('VisionKit抠图需要iOS 17或更高版本');
    }
    
    try {
      // 调用原生方法进行抠图
      final result = await _channel.invokeMethod('removeBackground', {
        'imagePath': imagePath,
      });
      return result as String?;
    } on PlatformException catch (e) {
      print('❌ VisionKit抠图失败: ${e.message}');
      throw Exception('抠图失败: ${e.message}');
    }
  }
}

/// iOS版本信息类
class IOSVersionInfo {
  final int majorVersion;
  final int minorVersion;
  final int patchVersion;
  final bool isSupported;
  final String versionString;
  final String? deviceModel;
  final String? deviceName;
  
  const IOSVersionInfo({
    required this.majorVersion,
    required this.minorVersion,
    required this.patchVersion,
    required this.isSupported,
    required this.versionString,
    this.deviceModel,
    this.deviceName,
  });
  
  /// 获取完整版本描述
  String get fullVersionString {
    if (deviceModel != null && deviceName != null) {
      return '$deviceName ($deviceModel) - iOS $versionString';
    }
    return 'iOS $versionString';
  }
  
  /// 获取支持状态描述
  String get supportStatusDescription {
    if (isSupported) {
      return '✅ 支持VisionKit抠图功能';
    } else {
      return '❌ 需要iOS 17或更高版本才能使用抠图功能';
    }
  }
  
  @override
  String toString() {
    return 'IOSVersionInfo(version: $versionString, supported: $isSupported)';
  }
}
