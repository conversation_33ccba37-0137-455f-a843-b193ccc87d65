/// 语言管理器扩展
/// 为现有的LanguageManager添加补丁功能，确保关键翻译键能正确显示
library;

import 'language_patch.dart';
import 'language_manager.dart';

extension LanguageManagerPatch on LanguageManager {
  /// 增强的翻译方法，会自动应用补丁
  String translateWithPatch(String key) {
    // 首先尝试从补丁获取翻译
    if (LanguagePatch.needsPatch(key)) {
      final patchedTranslation = LanguagePatch.getPatchedTranslation(key, currentLanguage);
      // 如果补丁翻译不是键名本身，说明找到了有效翻译
      if (patchedTranslation != key) {
        return patchedTranslation;
      }
    }

    // 如果补丁中没有，使用原始翻译方法
    final originalTranslation = translate(key);

    // 如果原始翻译返回的是键名本身，说明翻译失败，尝试使用补丁
    if (originalTranslation == key && LanguagePatch.needsPatch(key)) {
      return LanguagePatch.getPatchedTranslation(key, currentLanguage);
    }

    return originalTranslation;
  }

  /// 批量检查翻译状态
  Map<String, String> checkTranslationStatus(List<String> keys) {
    final status = <String, String>{};
    for (final key in keys) {
      final translation = translateWithPatch(key);
      status[key] = translation == key ? 'MISSING' : 'OK';
    }
    return status;
  }

  /// 获取所有有问题的翻译键
  List<String> getProblematicKeys() {
    final problematicKeys = <String>[];
    final allPatchKeys = LanguagePatch.getAllPatchKeys();

    for (final key in allPatchKeys) {
      final originalTranslation = translate(key);
      if (originalTranslation == key) {
        problematicKeys.add(key);
      }
    }

    return problematicKeys;
  }
}

/// 全局辅助函数，用于在Widget中快速获取修复后的翻译
class TranslationHelper {
  /// 安全的翻译方法，确保总是返回可读的文本
  static String safeTranslate(LanguageManager languageManager, String key) {
    return languageManager.translateWithPatch(key);
  }

  /// 批量翻译
  static Map<String, String> batchTranslate(LanguageManager languageManager, List<String> keys) {
    final translations = <String, String>{};
    for (final key in keys) {
      translations[key] = languageManager.translateWithPatch(key);
    }
    return translations;
  }

  /// 调试用：检查翻译状态
  static void debugTranslations(LanguageManager languageManager) {
    final problematicKeys = languageManager.getProblematicKeys();
    if (problematicKeys.isNotEmpty) {
      print('🔧 发现需要补丁的翻译键: $problematicKeys');
    } else {
      print('✅ 所有关键翻译键都正常工作');
    }
  }
}
