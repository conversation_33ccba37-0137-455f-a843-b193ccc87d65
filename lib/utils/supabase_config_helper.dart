import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';

/// Supabase配置助手
/// 帮助验证和测试Supabase连接
class SupabaseConfigHelper {
  
  /// 检查Supabase配置是否正确
  static bool isConfigured() {
    return SupabaseConfig.supabaseUrl != 'YOUR_SUPABASE_URL' &&
           SupabaseConfig.supabaseAnonKey != 'YOUR_SUPABASE_ANON_KEY' &&
           SupabaseConfig.supabaseUrl.isNotEmpty &&
           SupabaseConfig.supabaseAnonKey.isNotEmpty;
  }
  
  /// 测试Supabase连接
  static Future<bool> testConnection() async {
    try {
      if (!isConfigured()) {
        print('❌ Supabase配置未完成');
        return false;
      }
      
      final client = Supabase.instance.client;
      
      // 测试简单的查询
      await client.from('users').select('count').limit(1);
      
      print('✅ Supabase连接成功');
      return true;
    } catch (e) {
      print('❌ Supabase连接失败: $e');
      return false;
    }
  }
  
  /// 显示配置状态对话框
  static void showConfigStatus(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.settings, color: Colors.blue),
              SizedBox(width: 8),
              Text('Supabase配置状态'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusItem(
                '项目URL',
                SupabaseConfig.supabaseUrl,
                SupabaseConfig.supabaseUrl != 'YOUR_SUPABASE_URL',
              ),
              const SizedBox(height: 8),
              _buildStatusItem(
                '匿名密钥',
                SupabaseConfig.supabaseAnonKey.length > 20 
                    ? '${SupabaseConfig.supabaseAnonKey.substring(0, 20)}...'
                    : SupabaseConfig.supabaseAnonKey,
                SupabaseConfig.supabaseAnonKey != 'YOUR_SUPABASE_ANON_KEY',
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isConfigured() ? Colors.green[50] : Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isConfigured() ? Colors.green : Colors.orange,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      isConfigured() ? Icons.check_circle : Icons.warning,
                      color: isConfigured() ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isConfigured() 
                            ? '配置完成，可以使用Supabase功能'
                            : '请完成Supabase配置',
                        style: TextStyle(
                          color: isConfigured() ? Colors.green[800] : Colors.orange[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            if (!isConfigured())
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  showConfigInstructions(context);
                },
                child: const Text('查看配置说明'),
              ),
            if (isConfigured())
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final success = await testConnection();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(success ? '连接成功！' : '连接失败，请检查配置'),
                        backgroundColor: success ? Colors.green : Colors.red,
                      ),
                    );
                  }
                },
                child: const Text('测试连接'),
              ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
  
  /// 显示配置说明
  static void showConfigInstructions(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Supabase配置说明'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '请按照以下步骤配置Supabase：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildInstructionStep(
                  '1',
                  '登录Supabase',
                  '访问 supabase.com 并登录你的账号',
                ),
                _buildInstructionStep(
                  '2',
                  '进入项目设置',
                  '点击左侧菜单 Settings → API',
                ),
                _buildInstructionStep(
                  '3',
                  '复制项目信息',
                  '复制 Project URL 和 anon public 密钥',
                ),
                _buildInstructionStep(
                  '4',
                  '更新配置文件',
                  '在 lib/config/supabase_config.dart 中替换相应的值',
                ),
                _buildInstructionStep(
                  '5',
                  '创建数据库表',
                  '在Supabase SQL编辑器中执行 supabase_schema.sql',
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue),
                  ),
                  child: const Text(
                    '💡 提示：配置完成后，重启应用以确保配置生效。',
                    style: TextStyle(color: Colors.blue),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('我知道了'),
            ),
          ],
        );
      },
    );
  }
  
  /// 构建状态项
  static Widget _buildStatusItem(String label, String value, bool isConfigured) {
    return Row(
      children: [
        Icon(
          isConfigured ? Icons.check_circle : Icons.error,
          color: isConfigured ? Colors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: isConfigured ? Colors.black87 : Colors.red,
              fontFamily: 'monospace',
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
  
  /// 构建说明步骤
  static Widget _buildInstructionStep(String number, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 获取配置摘要
  static Map<String, dynamic> getConfigSummary() {
    return {
      'isConfigured': isConfigured(),
      'hasUrl': SupabaseConfig.supabaseUrl != 'YOUR_SUPABASE_URL',
      'hasKey': SupabaseConfig.supabaseAnonKey != 'YOUR_SUPABASE_ANON_KEY',
      'urlLength': SupabaseConfig.supabaseUrl.length,
      'keyLength': SupabaseConfig.supabaseAnonKey.length,
    };
  }
}
