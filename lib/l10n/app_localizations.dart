import 'package:flutter/material.dart';

abstract class AppLocalizations {
  const AppLocalizations();

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
        const AppLocalizationsZh();
  }

  // 导航栏
  String get navRetrospect;
  String get navTarot;
  String get navManifestation;
  String get navProfile;

  // 塔罗牌相关
  String get tarotReading;
  String get dailyTarot;
  String get dailyGuidance;
  String get selectSpread;
  String get askQuestion;
  String get drawCard;
  String get drawCards;
  String get cardRevealed;
  String get tapToReveal;
  String get reversed;
  String get upright;

  // 牌阵类型
  String get singleCard;
  String get threeCardClassic;
  String get advisorySpread;
  String get choiceTwoOptions;
  String get loveRelationship;
  String get sixPointStar;
  String get careerGrowth;
  String get soulHealing;
  String get yearlyFortune;

  // 占卜主题
  String get yesOrNo;
  String get twoChoices;
  String get threeChoices;
  String get trueLoveTiming;
  String get breakupReunion;
  String get secretCrush;
  String get thirdParty;
  String get interpersonalRelations;
  String get careerDevelopment;
  String get jobChange;
  String get promotionRaise;
  String get businessOpportunity;
  String get academicPlanning;
  String get examFortune;
  String get selfAwareness;
  String get emotionalHealing;
  String get dailyInspiration;
  String get shoppingWisdom;
  String get wealthFortune;

  // 用户界面
  String get profileTitle;
  String get editProfile;
  String get yourStats;
  String get totalReadings;
  String get dailyStreak;
  String get thisMonthReadings;
  String get averageRating;
  String get memberSince;
  String get preferences;
  String get notifications;
  String get language;
  String get membership;
  String get appleLogin;
  String get notLoggedIn;
  String get about;
  String get appVersion;
  String get privacyPolicy;
  String get termsOfService;
  String get helpSupport;
  String get clearHistory;
  String get deleteAccount;

  // 显化功能
  String get manifestationGoal;
  String get selectGoal;
  String get wealth;
  String get career;
  String get beauty;
  String get fame;
  String get love;
  String get affirmation;
  String get manifestationJournal;
  String get writeManifestation;
  String get saveJournal;

  // 历史记录
  String get historyTitle;
  String get searchReadings;
  String get filterReadings;
  String get allTypes;
  String get allRatings;
  String get emptyHistory;
  String get noReadingsYet;
  String get startFirstReading;

  // 通用文本
  String get confirm;
  String get cancel;
  String get save;
  String get delete;
  String get edit;
  String get done;
  String get next;
  String get previous;
  String get close;
  String get retry;
  String get loading;
  String get error;
  String get success;
  String get warning;

  // 对话框
  String get confirmDeleteTitle;
  String get confirmDeleteMessage;
  String get confirmClearHistoryTitle;
  String get confirmClearHistoryMessage;
  String get languageSelectionTitle;
  String get editProfileTitle;

  // 错误信息
  String get networkError;
  String get serverError;
  String get unknownError;
  String get permissionDenied;

  // 占卜相关
  String get questionPlaceholder;
  String get enterYourQuestion;
  String get selectCards;
  String get interpretation;
  String get guidance;
  String get advice;
  String get keywords;
  String get meaning;
  String get cardPosition;
  String get past;
  String get present;
  String get future;
  String get situation;
  String get challenge;
  String get action;

  // 时间相关
  String get today;
  String get yesterday;
  String get thisWeek;
  String get thisMonth;
  String get january;
  String get february;
  String get march;
  String get april;
  String get may;
  String get june;
  String get july;
  String get august;
  String get september;
  String get october;
  String get november;
  String get december;

  // 评分
  String get rate;
  String get rateReading;
  String get excellent;
  String get good;
  String get average;
  String get poor;
  String get terrible;

  // 新增每日塔罗标题
  String get dailyTarotTitle;

  // 新增塔罗牌阵文本
  String get threeCardSpread;
  String get celticCross;
}

class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['zh', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    switch (locale.languageCode) {
      case 'en':
        return const AppLocalizationsEn();
      case 'zh':
      default:
        return const AppLocalizationsZh();
    }
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;
}

// 中文实现
class AppLocalizationsZh extends AppLocalizations {
  const AppLocalizationsZh();

  @override
  String get navRetrospect => '回溯';
  @override
  String get navTarot => '塔罗';
  @override
  String get navManifestation => '显化';
  @override
  String get navProfile => '我的';

  @override
  String get tarotReading => '塔罗占卜';
  @override
  String get dailyTarot => '每日塔罗';
  @override
  String get dailyGuidance => '今日塔罗指引';
  @override
  String get selectSpread => '选择牌阵';
  @override
  String get askQuestion => '提出问题';
  @override
  String get drawCard => '抽卡';
  @override
  String get drawCards => '抽牌';
  @override
  String get cardRevealed => '牌已揭示';
  @override
  String get tapToReveal => '点击揭示';
  @override
  String get reversed => '逆位';
  @override
  String get upright => '正位';

  @override
  String get singleCard => '单张牌';
  @override
  String get threeCardClassic => '三张牌阵（经典）';
  @override
  String get advisorySpread => '建议牌阵';
  @override
  String get choiceTwoOptions => '二选一牌阵';
  @override
  String get loveRelationship => '爱情关系牌阵';
  @override
  String get sixPointStar => '六芒星牌阵';
  @override
  String get careerGrowth => '事业成长牌阵';
  @override
  String get soulHealing => '心灵疗愈牌阵';
  @override
  String get yearlyFortune => '全年运势牌阵';

  @override
  String get yesOrNo => 'Yes or No';
  @override
  String get twoChoices => '二选一抉择';
  @override
  String get threeChoices => '三选一抉择';
  @override
  String get trueLoveTiming => '真爱时机';
  @override
  String get breakupReunion => '分手复合';
  @override
  String get secretCrush => '暗恋心意';
  @override
  String get thirdParty => '第三者问题';
  @override
  String get interpersonalRelations => '人际关系';
  @override
  String get careerDevelopment => '事业发展';
  @override
  String get jobChange => '跳槽转职';
  @override
  String get promotionRaise => '升职加薪';
  @override
  String get businessOpportunity => '创业机会';
  @override
  String get academicPlanning => '学业规划';
  @override
  String get examFortune => '考试运势';
  @override
  String get selfAwareness => '自我认知';
  @override
  String get emotionalHealing => '情绪疗愈';
  @override
  String get dailyInspiration => '每日灵感';
  @override
  String get shoppingWisdom => '购物智慧';
  @override
  String get wealthFortune => '财富运势';

  @override
  String get dailyTarotTitle => '每日塔罗';

  @override
  String get threeCardSpread => '三张牌阵';
  @override
  String get celticCross => '凯尔特十字';

  @override
  String get profileTitle => '神秘探索者';
  @override
  String get editProfile => '编辑资料';
  @override
  String get yourStats => '您的数据';
  @override
  String get totalReadings => '总解读';
  @override
  String get dailyStreak => '连续天数';
  @override
  String get thisMonthReadings => '本月解读';
  @override
  String get averageRating => '平均评分';
  @override
  String get memberSince => '注册时间';
  @override
  String get preferences => '偏好设置';
  @override
  String get notifications => '通知设置';
  @override
  String get language => '语言';
  @override
  String get membership => '会员';
  @override
  String get appleLogin => 'Apple ID登录';
  @override
  String get notLoggedIn => '未登录';
  @override
  String get about => '关于';
  @override
  String get appVersion => '应用版本';
  @override
  String get privacyPolicy => '隐私政策';
  @override
  String get termsOfService => '服务条款';
  @override
  String get helpSupport => '帮助支持';
  @override
  String get clearHistory => '清空历史';
  @override
  String get deleteAccount => '删除账号';

  @override
  String get manifestationGoal => '显化目标';
  @override
  String get selectGoal => '选择目标';
  @override
  String get wealth => '财富';
  @override
  String get career => '事业';
  @override
  String get beauty => '美丽';
  @override
  String get fame => '名声';
  @override
  String get love => '爱情';
  @override
  String get affirmation => '肯定语';
  @override
  String get manifestationJournal => '显化日记';
  @override
  String get writeManifestation => '记录显化感悟';
  @override
  String get saveJournal => '保存日记';

  @override
  String get historyTitle => '历史记录';
  @override
  String get searchReadings => '搜索解读';
  @override
  String get filterReadings => '筛选解读';
  @override
  String get allTypes => '所有类型';
  @override
  String get allRatings => '所有评分';
  @override
  String get emptyHistory => '暂无记录';
  @override
  String get noReadingsYet => '还没有塔罗解读记录';
  @override
  String get startFirstReading => '开始你的第一次占卜吧！';

  @override
  String get confirm => '确认';
  @override
  String get cancel => '取消';
  @override
  String get save => '保存';
  @override
  String get delete => '删除';
  @override
  String get edit => '编辑';
  @override
  String get done => '完成';
  @override
  String get next => '下一步';
  @override
  String get previous => '上一步';
  @override
  String get close => '关闭';
  @override
  String get retry => '重试';
  @override
  String get loading => '加载中...';
  @override
  String get error => '错误';
  @override
  String get success => '成功';
  @override
  String get warning => '警告';

  @override
  String get confirmDeleteTitle => '确认删除';
  @override
  String get confirmDeleteMessage => '此操作无法撤销，确定要删除吗？';
  @override
  String get confirmClearHistoryTitle => '清空历史记录';
  @override
  String get confirmClearHistoryMessage => '确定要清空所有历史记录吗？此操作无法撤销。';
  @override
  String get languageSelectionTitle => '选择语言';
  @override
  String get editProfileTitle => '编辑个人资料';

  @override
  String get networkError => '网络连接失败';
  @override
  String get serverError => '服务器错误';
  @override
  String get unknownError => '未知错误';
  @override
  String get permissionDenied => '权限被拒绝';

  @override
  String get questionPlaceholder => '请输入你的问题...';
  @override
  String get enterYourQuestion => '输入你的问题';
  @override
  String get selectCards => '选择卡牌';
  @override
  String get interpretation => '解读';
  @override
  String get guidance => '指引';
  @override
  String get advice => '建议';
  @override
  String get keywords => '关键词';
  @override
  String get meaning => '含义';
  @override
  String get cardPosition => '牌位';
  @override
  String get past => '过去';
  @override
  String get present => '现在';
  @override
  String get future => '未来';
  @override
  String get situation => '现状';
  @override
  String get challenge => '挑战';
  @override
  String get action => '行动';

  @override
  String get today => '今天';
  @override
  String get yesterday => '昨天';
  @override
  String get thisWeek => '本周';
  @override
  String get thisMonth => '本月';
  @override
  String get january => '一月';
  @override
  String get february => '二月';
  @override
  String get march => '三月';
  @override
  String get april => '四月';
  @override
  String get may => '五月';
  @override
  String get june => '六月';
  @override
  String get july => '七月';
  @override
  String get august => '八月';
  @override
  String get september => '九月';
  @override
  String get october => '十月';
  @override
  String get november => '十一月';
  @override
  String get december => '十二月';

  @override
  String get rate => '评分';
  @override
  String get rateReading => '为本次解读评分';
  @override
  String get excellent => '非常准确';
  @override
  String get good => '比较准确';
  @override
  String get average => '一般';
  @override
  String get poor => '不太准确';
  @override
  String get terrible => '完全不准';
}

// 英文实现
class AppLocalizationsEn extends AppLocalizations {
  const AppLocalizationsEn();

  @override
  String get navRetrospect => 'History';
  @override
  String get navTarot => 'Tarot';
  @override
  String get navManifestation => 'Manifest';
  @override
  String get navProfile => 'Profile';

  @override
  String get tarotReading => 'Tarot Reading';
  @override
  String get dailyTarot => 'Daily Tarot';
  @override
  String get dailyGuidance => 'Daily Tarot Guidance';
  @override
  String get selectSpread => 'Select Spread';
  @override
  String get askQuestion => 'Ask Question';
  @override
  String get drawCard => 'Draw Card';
  @override
  String get drawCards => 'Draw Cards';
  @override
  String get cardRevealed => 'Card Revealed';
  @override
  String get tapToReveal => 'Tap to Reveal';
  @override
  String get reversed => 'Reversed';
  @override
  String get upright => 'Upright';

  @override
  String get singleCard => 'Single Card';
  @override
  String get threeCardClassic => 'Three Card (Classic)';
  @override
  String get advisorySpread => 'Advisory Spread';
  @override
  String get choiceTwoOptions => 'Two Choice Spread';
  @override
  String get loveRelationship => 'Love Relationship';
  @override
  String get sixPointStar => 'Six Point Star';
  @override
  String get careerGrowth => 'Career Growth';
  @override
  String get soulHealing => 'Soul Healing';
  @override
  String get yearlyFortune => 'Yearly Fortune';

  @override
  String get yesOrNo => 'Yes or No';
  @override
  String get twoChoices => 'Two Choices';
  @override
  String get threeChoices => 'Three Choices';
  @override
  String get trueLoveTiming => 'True Love Timing';
  @override
  String get breakupReunion => 'Breakup & Reunion';
  @override
  String get secretCrush => 'Secret Crush';
  @override
  String get thirdParty => 'Third Party';
  @override
  String get interpersonalRelations => 'Interpersonal Relations';
  @override
  String get careerDevelopment => 'Career Development';
  @override
  String get jobChange => 'Job Change';
  @override
  String get promotionRaise => 'Promotion & Raise';
  @override
  String get businessOpportunity => 'Business Opportunity';
  @override
  String get academicPlanning => 'Academic Planning';
  @override
  String get examFortune => 'Exam Fortune';
  @override
  String get selfAwareness => 'Self Awareness';
  @override
  String get emotionalHealing => 'Emotional Healing';
  @override
  String get dailyInspiration => 'Daily Inspiration';
  @override
  String get shoppingWisdom => 'Shopping Wisdom';
  @override
  String get wealthFortune => 'Wealth Fortune';

  @override
  String get dailyTarotTitle => 'Daily Tarot';

  @override
  String get threeCardSpread => 'Three Card Spread';
  @override
  String get celticCross => 'Celtic Cross';

  @override
  String get profileTitle => 'Mystic Explorer';
  @override
  String get editProfile => 'Edit Profile';
  @override
  String get yourStats => 'Your Stats';
  @override
  String get totalReadings => 'Total Readings';
  @override
  String get dailyStreak => 'Daily Streak';
  @override
  String get thisMonthReadings => 'This Month';
  @override
  String get averageRating => 'Average Rating';
  @override
  String get memberSince => 'Member Since';
  @override
  String get preferences => 'Preferences';
  @override
  String get notifications => 'Notifications';
  @override
  String get language => 'Language';
  @override
  String get membership => 'Membership';
  @override
  String get appleLogin => 'Sign in with Apple';
  @override
  String get notLoggedIn => 'Not signed in';
  @override
  String get about => 'About';
  @override
  String get appVersion => 'App Version';
  @override
  String get privacyPolicy => 'Privacy Policy';
  @override
  String get termsOfService => 'Terms of Service';
  @override
  String get helpSupport => 'Help & Support';
  @override
  String get clearHistory => 'Clear History';
  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get manifestationGoal => 'Manifestation Goal';
  @override
  String get selectGoal => 'Select Goal';
  @override
  String get wealth => 'Wealth';
  @override
  String get career => 'Career';
  @override
  String get beauty => 'Beauty';
  @override
  String get fame => 'Fame';
  @override
  String get love => 'Love';
  @override
  String get affirmation => 'Affirmation';
  @override
  String get manifestationJournal => 'Manifestation Journal';
  @override
  String get writeManifestation => 'Write your manifestation insights';
  @override
  String get saveJournal => 'Save Journal';

  @override
  String get historyTitle => 'History';
  @override
  String get searchReadings => 'Search Readings';
  @override
  String get filterReadings => 'Filter Readings';
  @override
  String get allTypes => 'All Types';
  @override
  String get allRatings => 'All Ratings';
  @override
  String get emptyHistory => 'No Records';
  @override
  String get noReadingsYet => 'No tarot readings yet';
  @override
  String get startFirstReading => 'Start your first reading!';

  @override
  String get confirm => 'Confirm';
  @override
  String get cancel => 'Cancel';
  @override
  String get save => 'Save';
  @override
  String get delete => 'Delete';
  @override
  String get edit => 'Edit';
  @override
  String get done => 'Done';
  @override
  String get next => 'Next';
  @override
  String get previous => 'Previous';
  @override
  String get close => 'Close';
  @override
  String get retry => 'Retry';
  @override
  String get loading => 'Loading...';
  @override
  String get error => 'Error';
  @override
  String get success => 'Success';
  @override
  String get warning => 'Warning';

  @override
  String get confirmDeleteTitle => 'Confirm Delete';
  @override
  String get confirmDeleteMessage => 'This action cannot be undone. Are you sure you want to delete?';
  @override
  String get confirmClearHistoryTitle => 'Clear History';
  @override
  String get confirmClearHistoryMessage => 'Are you sure you want to clear all history? This action cannot be undone.';
  @override
  String get languageSelectionTitle => 'Select Language';
  @override
  String get editProfileTitle => 'Edit Profile';

  @override
  String get networkError => 'Network connection failed';
  @override
  String get serverError => 'Server error';
  @override
  String get unknownError => 'Unknown error';
  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get questionPlaceholder => 'Enter your question...';
  @override
  String get enterYourQuestion => 'Enter your question';
  @override
  String get selectCards => 'Select Cards';
  @override
  String get interpretation => 'Interpretation';
  @override
  String get guidance => 'Guidance';
  @override
  String get advice => 'Advice';
  @override
  String get keywords => 'Keywords';
  @override
  String get meaning => 'Meaning';
  @override
  String get cardPosition => 'Position';
  @override
  String get past => 'Past';
  @override
  String get present => 'Present';
  @override
  String get future => 'Future';
  @override
  String get situation => 'Situation';
  @override
  String get challenge => 'Challenge';
  @override
  String get action => 'Action';

  @override
  String get today => 'Today';
  @override
  String get yesterday => 'Yesterday';
  @override
  String get thisWeek => 'This Week';
  @override
  String get thisMonth => 'This Month';
  @override
  String get january => 'January';
  @override
  String get february => 'February';
  @override
  String get march => 'March';
  @override
  String get april => 'April';
  @override
  String get may => 'May';
  @override
  String get june => 'June';
  @override
  String get july => 'July';
  @override
  String get august => 'August';
  @override
  String get september => 'September';
  @override
  String get october => 'October';
  @override
  String get november => 'November';
  @override
  String get december => 'December';

  @override
  String get rate => 'Rate';
  @override
  String get rateReading => 'Rate this reading';
  @override
  String get excellent => 'Excellent';
  @override
  String get good => 'Good';
  @override
  String get average => 'Average';
  @override
  String get poor => 'Poor';
  @override
  String get terrible => 'Terrible';
} 