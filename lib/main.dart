import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';

import 'package:ai_tarot_reading/providers/daily_tarot_provider.dart';
import 'package:ai_tarot_reading/l10n/app_localizations.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/utils/apple_signin_manager.dart';
import 'package:ai_tarot_reading/services/supabase_auth_service.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/services/card_preference_service.dart';
import 'package:ai_tarot_reading/services/background_service.dart';
import 'package:ai_tarot_reading/services/app_background_service.dart';
import 'package:ai_tarot_reading/services/notification_service.dart';
import 'package:ai_tarot_reading/services/manifestation_goal_service.dart';
import 'package:ai_tarot_reading/services/connectivity_service.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';
import 'package:ai_tarot_reading/screens/splash_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  print('🚀 应用启动 - 日志系统测试');

  // 初始化Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );

  // 初始化通知服务
  await NotificationService().initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppStateProvider()),
        ChangeNotifierProvider(create: (_) => LanguageManager()..initialize()),
        ChangeNotifierProvider(create: (_) => AppleSignInManager()..initialize()),
        ChangeNotifierProvider(create: (_) => SupabaseAuthService()..initialize()),
        ChangeNotifierProvider(create: (_) => SubscriptionService()),
        ChangeNotifierProvider(create: (_) => InvitationService()..initialize()),
        Provider(create: (_) => SupabaseDataService()),
        ChangeNotifierProvider(create: (_) => DailyTarotProvider()),
        ChangeNotifierProvider(create: (_) => BlurSettingsService()),
        ChangeNotifierProvider(create: (_) => CardPreferenceService()),
        ChangeNotifierProvider(create: (_) => BackgroundService()),
        ChangeNotifierProvider(create: (_) => AppBackgroundService()),
        ChangeNotifierProvider(create: (_) => ManifestationGoalService()..initialize()),
        ChangeNotifierProvider(create: (_) => ConnectivityService()..initialize()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
      // 应用进入后台或被关闭时，触发聊天摘要生成
      debugPrint('📱 应用进入后台，触发聊天摘要生成');
      _triggerChatSummaryOnBackground();
    }
  }

  Future<void> _triggerChatSummaryOnBackground() async {
    try {
      // 这里需要访问HigherSelfService实例
      // 由于这是全局级别，我们可以通过静态方法来处理
      debugPrint('🔄 应用后台触发聊天摘要生成');
      // TODO: 实现全局聊天摘要触发
    } catch (e) {
      debugPrint('❌ 后台聊天摘要生成失败: $e');
    }
  }

  /// 将语言代码转换为Locale
  Locale _getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return const Locale('zh', 'CN');
      case 'zh-TW':
        return const Locale('zh', 'TW');
      case 'en-US':
        return const Locale('en', 'US');
      case 'es-ES':
        return const Locale('es', 'ES');
      case 'ja-JP':
        return const Locale('ja', 'JP');
      case 'ko-KR':
        return const Locale('ko', 'KR');
      default:
        return const Locale('zh', 'CN');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return MaterialApp(
          title: 'GrowCard',
          debugShowCheckedModeBanner: false,

          // 国际化配置
          locale: _getLocaleFromLanguageCode(languageManager.currentLanguage),
          supportedLocales: const [
            Locale('zh', 'CN'), // 中文简体
            Locale('zh', 'TW'), // 中文繁体
            Locale('en', 'US'), // 英文
            Locale('es', 'ES'), // 西班牙语
            Locale('ja', 'JP'), // 日语
            Locale('ko', 'KR'), // 韩语
          ],
          localizationsDelegates: const [
            AppLocalizationsDelegate(),
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          
          theme: FigmaTheme.createTheme(),
          darkTheme: FigmaTheme.createTheme(),
          themeMode: ThemeMode.dark,
          home: const SplashScreen(),

        );
      },
    );
  }
}
