# 📋 收据验证问题排查指南

## 🔧 已修复的问题

### 1. **函数名错误** ✅ 已修复
- **问题**: 代码调用 `swift-responder` 但实际函数是 `verify-receipt`
- **修复**: 更正函数调用名称为 `verify-receipt`

### 2. **过度严格的验证逻辑** ✅ 已优化
- **问题**: 生产模式拒绝所有验证失败的情况
- **修复**: 智能处理不同状态码，区分严重错误和临时问题

## 📊 Apple状态码处理策略

### ✅ 允许继续的状态码
```
0    - 验证成功
21005 - 收据服务器暂时不可用 (临时错误)
21006 - 收据有效但订阅已过期 (正常情况)
21007 - 沙盒收据 (Edge Function会自动处理)
```

### ❌ 拒绝购买的状态码
```
21000 - JSON对象格式错误
21002 - 收据数据格式错误
21003 - 收据无法验证
21004 - 共享密钥不匹配
21008 - 环境错误
21010 - 收据无法验证
```

## 🔍 验证流程

### 1. Edge Function验证流程
```typescript
1. 首先尝试生产环境验证
2. 如果返回21007(沙盒收据)，自动切换到沙盒环境
3. 返回统一的验证结果格式
```

### 2. 客户端处理流程
```dart
1. 调用 verify-receipt Edge Function
2. 检查返回的状态码
3. 根据状态码决定是否允许购买
4. 提供用户友好的错误信息
```

## 🚨 常见问题及解决方案

### 问题1: 网络连接失败
**症状**: 收据验证服务调用失败
**解决方案**:
- HTTP 5xx错误: 允许购买继续（服务器临时问题）
- HTTP 4xx错误: 拒绝购买（客户端问题）
- 网络异常: 允许购买继续（网络问题）

### 问题2: 沙盒vs生产环境
**症状**: 21007或21008状态码
**解决方案**:
- Edge Function自动检测并切换环境
- 客户端无需特殊处理

### 问题3: 订阅过期
**症状**: 21006状态码
**解决方案**:
- 允许用户重新订阅
- 不阻止购买流程

## 🧪 测试工具

### 使用内置测试工具
```dart
import 'package:ai_tarot_reading/utils/receipt_verification_test.dart';

// 在调试模式下运行完整测试
await ReceiptVerificationTest.runFullTest();
```

### 测试内容
1. **Edge Function可用性测试**
2. **状态码处理策略验证**
3. **响应时间测试**
4. **错误处理测试**

## 🔧 部署检查清单

### Edge Function部署
- [ ] `verify-receipt` 函数已部署到Supabase
- [ ] 函数可以正常响应请求
- [ ] CORS配置正确
- [ ] 错误处理完善

### 客户端配置
- [ ] 函数名称正确 (`verify-receipt`)
- [ ] 状态码处理逻辑完善
- [ ] 网络错误处理优化
- [ ] 用户友好的错误信息

### 生产环境验证
- [ ] 真实购买流程测试
- [ ] 不同网络环境测试
- [ ] 边缘情况处理测试

## 📱 监控指标

### 关键指标
- **验证成功率**: 应该 > 95%
- **响应时间**: 应该 < 3秒
- **错误率**: 应该 < 5%

### 监控方法
```dart
// 在生产环境中记录关键指标
debugPrint('📊 验证结果: 成功=${isValid}, 状态码=${statusCode}, 耗时=${duration}ms');
```

## 🚀 生产环境最佳实践

### 1. 错误处理策略
- **宽松处理**: 网络错误、临时服务器问题
- **严格处理**: 收据格式错误、验证失败

### 2. 用户体验优化
- **友好错误信息**: 避免技术术语
- **重试机制**: 网络错误时提供重试选项
- **降级策略**: 验证服务不可用时的备用方案

### 3. 安全考虑
- **防重放攻击**: Edge Function中实现
- **数据加密**: 收据数据传输加密
- **日志安全**: 避免记录敏感信息

## 🔄 故障排除步骤

### 1. 验证Edge Function
```bash
# 测试Edge Function是否可用
curl -X POST https://your-project.supabase.co/functions/v1/verify-receipt \
  -H "Content-Type: application/json" \
  -d '{"receiptData": "test"}'
```

### 2. 检查客户端日志
```dart
// 启用详细日志
if (kDebugMode) {
  debugPrint('🔍 收据验证详细信息...');
}
```

### 3. 验证网络连接
- 检查设备网络连接
- 验证Supabase服务状态
- 测试其他API调用

---

**更新日期**: 2025年1月  
**版本**: 1.0.9  
**负责人**: 开发团队
