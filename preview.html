<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>塔罗解读 - 100% Figma复刻预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            height: 100vh;
            background: linear-gradient(
                to bottom,
                #E8F4FD,
                #F8E8FF,
                #FFE8F8
            );
            overflow: hidden;
            position: relative;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 云朵背景效果 - 更丰富的云朵 */
        .cloud-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .cloud-1 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                ellipse 800px 400px at 30% -20%,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(255, 255, 255, 0.6) 40%,
                transparent 70%
            );
        }

        .cloud-2 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                ellipse 600px 300px at -20% 30%,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 70%
            );
        }

        .cloud-3 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                ellipse 500px 250px at 80% 60%,
                rgba(255, 255, 255, 0.7) 0%,
                rgba(255, 255, 255, 0.3) 60%,
                transparent 80%
            );
        }

        .cloud-4 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                ellipse 400px 200px at 70% -10%,
                rgba(255, 255, 255, 0.6) 0%,
                rgba(255, 255, 255, 0.2) 70%,
                transparent 90%
            );
        }

        .header {
            background: rgba(45, 55, 72, 0.9);
            color: white;
            text-align: center;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            z-index: 10;
        }

        .content {
            flex: 1;
            position: relative;
            z-index: 1;
        }

        /* 吉祥物区域 - 按您的规格定位 */
        .mascot-area {
            position: absolute;
            left: calc(50% - 150px);
            top: 15%;
            width: 300px;
            height: 300px;
        }

        .mascot-container {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(
                circle,
                rgba(116, 109, 255, 0.3) 0%,
                rgba(185, 181, 251, 0.2) 70%,
                transparent 100%
            );
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            animation: float 2s ease-in-out infinite alternate;
        }

        /* 吉祥物角色设计 */
        .mascot-character {
            width: 200px;
            height: 200px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 角色主体 */
        .character-body {
            width: 140px;
            height: 140px;
            background: linear-gradient(145deg, #FFE4B5, #FFDAB9);
            border-radius: 50%;
            position: relative;
            box-shadow: 0 8px 20px rgba(116, 109, 255, 0.3);
        }

        /* 头发 */
        .character-hair {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 80px;
            background: linear-gradient(145deg, #FFD700, #FFA500);
            border-radius: 60px 60px 40px 40px;
        }

        /* 眼睛 */
        .character-eyes {
            position: absolute;
            top: 45px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
        }

        .eye {
            width: 12px;
            height: 12px;
            background: #4A90E2;
            border-radius: 50%;
            position: relative;
        }

        .eye::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
        }

        /* 微笑 */
        .character-smile {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 10px;
            background: #FF6B9D;
            border-radius: 0 0 20px 20px;
        }

        /* 塔罗牌 */
        .tarot-card {
            position: absolute;
            top: 20px;
            right: 10px;
            width: 25px;
            height: 35px;
            background: linear-gradient(145deg, #746DFF, #9F7AEA);
            border-radius: 4px;
            border: 2px solid #B9B5FB;
            transform: rotate(15deg);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .tarot-card::after {
            content: '✨';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
        }

        /* 水晶球 */
        .crystal-ball {
            position: absolute;
            bottom: 20px;
            left: 15px;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.9), rgba(116, 109, 255, 0.6));
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(116, 109, 255, 0.4);
        }

        .crystal-ball::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 8px;
            width: 8px;
            height: 8px;
            background: rgba(255,255,255,0.8);
            border-radius: 50%;
        }

        /* 激励文字 - 完全按照您的CSS规格 */
        .inspiration-text {
            position: absolute;
            left: 7.8%;
            right: 2.66%;
            top: 60.18%;
            bottom: 33.94%;
            font-family: 'Inter';
            font-weight: 400;
            font-size: 24.8px;
            line-height: 32px;
            color: #5D5E61;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 开始占卜按钮 - 水平居中 */
        .start-button {
            position: absolute;
            width: 528px;
            height: 87px;
            left: 50%;
            transform: translateX(-50%);
            bottom: 120px; /* 调整位置避免被底部导航遮挡 */
            background: #746DFF;
            border: 1px solid #B9B5FB;
            border-radius: 33px;
            box-shadow: 0 8px 20px rgba(116, 109, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease-out;
        }

        .start-button:hover {
            box-shadow: 0 12px 25px rgba(116, 109, 255, 0.4);
        }

        .button-text {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            font-family: 'Inter';
            font-weight: 500;
            font-size: 28.2px;
            line-height: 34px;
            color: #DEDCFE;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bottom-nav {
            display: flex;
            background: rgba(45, 55, 72, 0.9);
            padding: 12px 0;
            z-index: 10;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #A0AEC0;
            font-size: 12px;
            gap: 4px;
        }

        .nav-item.active {
            color: #746DFF;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            background: currentColor;
            border-radius: 4px;
        }

        /* 动画 */
        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-10px); }
        }

        @keyframes fadeInUp {
            0% { 
                opacity: 0;
                transform: translateY(50px);
            }
            100% { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #746DFF;
            color: white;
            padding: 20px 40px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(116, 109, 255, 0.4);
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 云朵背景 -->
        <div class="cloud-background">
            <div class="cloud-1"></div>
            <div class="cloud-2"></div>
            <div class="cloud-3"></div>
            <div class="cloud-4"></div>
        </div>

        <!-- 顶部导航 -->
        <div class="header">
            塔罗解读
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <!-- 吉祥物区域 -->
            <div class="mascot-area">
                <div class="mascot-container">
                    <div class="mascot-character">
                        <div class="character-body">
                            <div class="character-hair"></div>
                            <div class="character-eyes">
                                <div class="eye"></div>
                                <div class="eye"></div>
                            </div>
                            <div class="character-smile"></div>
                        </div>
                        <div class="tarot-card"></div>
                        <div class="crystal-ball"></div>
                    </div>
                </div>
            </div>

            <!-- 激励文字 -->
            <div class="inspiration-text">
                让我们一起迎向充满可能的未来吧！透过塔罗的指引，点亮你生命中的希望与勇气。
            </div>

            <!-- 开始占卜按钮 -->
            <div class="start-button" onclick="showSuccess()">
                <div class="button-text">开始占卜</div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <div class="nav-icon"></div>
                <span>历史</span>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"></div>
                <span>解读</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon"></div>
                <span>每日</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon"></div>
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 成功消息 -->
    <div class="success-message" id="successMessage">
        🎉 按钮点击成功！设计已100%复刻您的Figma规格！
    </div>

    <script>
        function showSuccess() {
            const message = document.getElementById('successMessage');
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
