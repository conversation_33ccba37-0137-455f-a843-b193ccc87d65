-- Supabase数据库表结构
-- 用于AI塔罗占卜应用

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表 (扩展auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 塔罗解读记录表
CREATE TABLE public.tarot_readings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    spread_type TEXT NOT NULL,
    cards JSONB NOT NULL,
    interpretation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 每日塔罗表
CREATE TABLE public.daily_tarot (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    card JSONB,
    fortune TEXT,
    advice TEXT,
    is_drawn BOOLEAN DEFAULT FALSE,
    manifestation_goal TEXT,
    affirmation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- 用户偏好设置表
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    preferences JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_tarot_readings_user_id ON public.tarot_readings(user_id);
CREATE INDEX idx_tarot_readings_created_at ON public.tarot_readings(created_at);
CREATE INDEX idx_daily_tarot_user_id ON public.daily_tarot(user_id);
CREATE INDEX idx_daily_tarot_date ON public.daily_tarot(date);
CREATE INDEX idx_daily_tarot_user_date ON public.daily_tarot(user_id, date);

-- 行级安全策略 (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tarot_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_tarot ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- 用户表策略
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 塔罗解读记录策略
CREATE POLICY "Users can view own readings" ON public.tarot_readings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own readings" ON public.tarot_readings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own readings" ON public.tarot_readings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own readings" ON public.tarot_readings
    FOR DELETE USING (auth.uid() = user_id);

-- 每日塔罗策略
CREATE POLICY "Users can view own daily tarot" ON public.daily_tarot
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own daily tarot" ON public.daily_tarot
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own daily tarot" ON public.daily_tarot
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own daily tarot" ON public.daily_tarot
    FOR DELETE USING (auth.uid() = user_id);

-- 用户偏好设置策略
CREATE POLICY "Users can view own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own preferences" ON public.user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- 创建函数：自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON public.user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建函数：自动创建用户资料
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器：当新用户注册时自动创建资料
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 创建存储过程：获取用户统计信息
CREATE OR REPLACE FUNCTION get_user_stats(user_uuid UUID)
RETURNS TABLE(
    total_readings BIGINT,
    daily_streak INTEGER,
    total_days BIGINT
) AS $$
DECLARE
    streak_count INTEGER := 0;
    current_date_check DATE := CURRENT_DATE;
BEGIN
    -- 获取总解读次数
    SELECT COUNT(*) INTO total_readings
    FROM public.tarot_readings
    WHERE user_id = user_uuid;
    
    -- 获取每日塔罗总天数
    SELECT COUNT(*) INTO total_days
    FROM public.daily_tarot
    WHERE user_id = user_uuid AND is_drawn = true;
    
    -- 计算连续天数
    LOOP
        IF EXISTS (
            SELECT 1 FROM public.daily_tarot
            WHERE user_id = user_uuid 
            AND date = current_date_check 
            AND is_drawn = true
        ) THEN
            streak_count := streak_count + 1;
            current_date_check := current_date_check - INTERVAL '1 day';
        ELSE
            EXIT;
        END IF;
    END LOOP;
    
    daily_streak := streak_count;
    
    RETURN QUERY SELECT total_readings, daily_streak, total_days;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建视图：用户仪表板数据
CREATE OR REPLACE VIEW user_dashboard AS
SELECT 
    u.id,
    u.email,
    u.full_name,
    u.avatar_url,
    u.created_at as member_since,
    COUNT(DISTINCT tr.id) as total_readings,
    COUNT(DISTINCT dt.date) as total_daily_tarot_days,
    COALESCE(MAX(dt.date), u.created_at::date) as last_activity_date
FROM public.users u
LEFT JOIN public.tarot_readings tr ON u.id = tr.user_id
LEFT JOIN public.daily_tarot dt ON u.id = dt.user_id AND dt.is_drawn = true
GROUP BY u.id, u.email, u.full_name, u.avatar_url, u.created_at;

-- 授予必要的权限
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- 注释
COMMENT ON TABLE public.users IS '用户资料表，扩展auth.users';
COMMENT ON TABLE public.tarot_readings IS '塔罗解读记录表';
COMMENT ON TABLE public.daily_tarot IS '每日塔罗记录表';
COMMENT ON TABLE public.user_preferences IS '用户偏好设置表';
COMMENT ON FUNCTION get_user_stats(UUID) IS '获取用户统计信息的函数';
COMMENT ON VIEW user_dashboard IS '用户仪表板数据视图';
