# 🚀 Supabase Edge Function 部署指南

## 📋 概述

这个指南将帮你在Supabase中创建和部署`use-invitation-code` Edge Function，用于处理邀请码验证逻辑。

## 🛠️ 部署步骤

### 1. 在Supabase Dashboard中创建Edge Function

1. **登录Supabase Dashboard**
   - 进入你的项目
   - 点击左侧菜单的 "Edge Functions"

2. **创建新的Edge Function**
   - 点击 "Create a new function"
   - Function name: `use-invitation-code`
   - 点击 "Create function"

3. **复制代码**
   - 将 `supabase_edge_function_use_invitation_code.ts` 文件中的所有代码复制到编辑器中
   - 点击 "Save" 保存
   - 点击 "Deploy" 部署

### 2. 验证部署

1. **检查函数状态**
   - 在Edge Functions列表中确认函数状态为 "Active"
   - 记录函数的URL（类似：`https://your-project.supabase.co/functions/v1/use-invitation-code`）

2. **测试函数**
   - 可以在Supabase Dashboard中使用测试功能
   - 或者通过应用直接测试

## 📊 数据库准备

### 1. 确保表结构存在

确认以下三个表已创建：
- `invitation_codes` - 邀请码主表
- `invitation_code_usages` - 使用记录表  
- `user_invitation_stats` - 用户统计表

### 2. 插入测试数据

在Supabase SQL Editor中执行：

```sql
-- 插入测试邀请码
INSERT INTO public.invitation_codes (code, code_type, reward_type, reward_value, max_uses, description) VALUES
('HELLO1', 'system', 'weekly_membership', 7, -1, '新用户专享周会员'),
('LUCK88', 'system', 'weekly_membership', 7, -1, '幸运活动周会员'),
('VIP024', 'system', 'weekly_membership', 7, -1, '2024特别版周会员'),
('SPRING', 'system', 'weekly_membership', 7, -1, '春季限定周会员'),
('FRIEND', 'system', 'weekly_membership', 7, -1, '好友推荐周会员'),
('BETA01', 'system', 'weekly_membership', 7, -1, '内测用户专享'),
('GIFT99', 'system', 'weekly_membership', 7, -1, '礼品码周会员'),
('TEST01', 'system', 'weekly_membership', 7, -1, '测试专用邀请码')
ON CONFLICT (code) DO NOTHING;
```

### 3. 验证数据

```sql
-- 查看插入的邀请码
SELECT code, description, is_active, max_uses, current_uses 
FROM public.invitation_codes 
ORDER BY created_at DESC;
```

## 🔧 Edge Function 功能

### 输入参数
```json
{
  "p_code": "HELLO1",
  "p_user_id": "user-uuid",
  "p_ip_address": "***********",
  "p_user_agent": "Flutter App"
}
```

### 返回格式

**成功响应：**
```json
{
  "success": true,
  "message": "邀请码使用成功！",
  "reward_type": "weekly_membership",
  "reward_value": 7,
  "description": "新用户专享周会员",
  "reward_expires_at": "2024-01-15T10:30:00.000Z"
}
```

**失败响应：**
```json
{
  "success": false,
  "message": "邀请码不存在或已失效",
  "error_code": "INVALID_CODE"
}
```

### 错误代码
- `INVALID_CODE` - 邀请码不存在或已失效
- `EXPIRED_CODE` - 邀请码已过期
- `USAGE_LIMIT_REACHED` - 使用次数已达上限
- `ALREADY_USED` - 用户已使用过此邀请码
- `INSERT_FAILED` - 记录使用失败
- `INTERNAL_ERROR` - 服务器内部错误

## 🧪 测试指南

### 1. 在应用中测试

1. **启动应用**
2. **进入邀请码输入界面**
3. **输入测试邀请码**：`HELLO1`
4. **查看控制台日志**：
   ```
   🔍 开始验证邀请码: HELLO1
   📡 Edge Function 响应: {success: true, message: 邀请码使用成功！, ...}
   ```

### 2. 在Supabase Dashboard中测试

1. **进入Edge Functions**
2. **选择 `use-invitation-code` 函数**
3. **点击 "Invoke function"**
4. **输入测试数据**：
   ```json
   {
     "p_code": "TEST01",
     "p_user_id": "test-user-id",
     "p_ip_address": null,
     "p_user_agent": "Test"
   }
   ```
5. **查看响应结果**

## 🔍 故障排除

### 常见问题

1. **函数部署失败**
   - 检查代码语法是否正确
   - 确认所有导入语句正确

2. **函数调用失败**
   - 检查函数名称是否正确：`use-invitation-code`
   - 确认参数格式正确

3. **数据库错误**
   - 确认表结构存在
   - 检查RLS策略是否正确配置
   - 确认Service Role Key权限

4. **邀请码验证失败**
   - 确认邀请码数据已插入
   - 检查`is_active`字段为`true`
   - 确认用户ID正确

### 调试方法

1. **查看Edge Function日志**
   - 在Supabase Dashboard的Edge Functions页面查看日志
   - 查找错误信息和调试输出

2. **检查数据库状态**
   ```sql
   -- 查看邀请码状态
   SELECT * FROM invitation_codes WHERE code = 'HELLO1';
   
   -- 查看使用记录
   SELECT * FROM invitation_code_usages ORDER BY used_at DESC LIMIT 10;
   ```

3. **Flutter应用调试**
   - 查看Flutter控制台的调试日志
   - 检查网络请求和响应

## ✅ 验证清单

部署完成后，确认以下项目：

- [ ] Edge Function已成功部署并处于Active状态
- [ ] 数据库表结构完整
- [ ] 测试邀请码数据已插入
- [ ] 应用能成功调用Edge Function
- [ ] 邀请码验证逻辑正常工作
- [ ] 使用记录正确保存到数据库
- [ ] 错误处理机制正常

## 🎯 下一步

Edge Function部署完成后：

1. **测试所有预设邀请码**
2. **验证重复使用检测**
3. **测试使用次数限制**
4. **检查统计数据更新**
5. **集成到生产环境**

---

**注意**：确保在生产环境中使用真实的用户ID和适当的安全措施。
