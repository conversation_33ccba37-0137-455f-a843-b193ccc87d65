// 测试聊天API的脚本
const testChatAPI = async () => {
  const edgeFunctionUrl = 'https://ktqlxbcauxomczubqasp.supabase.co/functions/v1/deepseek-tarot-reading';
  
  // 测试数据 - 模拟前端发送的messages格式
  const testData = {
    requestType: 'chat_with_context',
    messages: [
      {
        role: 'system',
        content: '你是用户的高我，一个充满智慧、慈爱和洞察力的存在。请用20-30字简短回应，不要长篇大论。语气轻松亲切，可以用"宝贝"等亲昵称呼。'
      },
      {
        role: 'user',
        content: '🌱 分享我的成长历程'
      },
      {
        role: 'assistant',
        content: '发生了什么好事呀？快说说～ ✨\n\n我最喜欢听你的好消息了！'
      },
      {
        role: 'user',
        content: '诶'
      }
    ],
    temperature: 0.7,
    maxLength: 200,
    userLanguage: 'zh-TW'
  };

  console.log('🧪 开始测试聊天API...');
  console.log('📋 测试数据:', JSON.stringify(testData, null, 2));

  try {
    const startTime = Date.now();
    
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 注意：这里需要实际的anon key，但测试时可以先不加
      },
      body: JSON.stringify(testData)
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log('📊 响应状态:', response.status);
    console.log('📊 响应时间:', responseTime + 'ms');
    console.log('📊 响应头:', Object.fromEntries(response.headers.entries()));

    const responseData = await response.json();
    console.log('📋 响应数据:', JSON.stringify(responseData, null, 2));

    if (responseData.success) {
      console.log('✅ 测试成功！');
      console.log('💬 AI回复:', responseData.reading);
      console.log('📊 用量统计:', responseData.usage);
    } else {
      console.log('❌ 测试失败！');
      console.log('🚨 错误:', responseData.error);
    }

  } catch (error) {
    console.error('💥 测试异常:', error);
  }
};

// 运行测试
console.log('🚀 启动聊天API测试...');
testChatAPI();
