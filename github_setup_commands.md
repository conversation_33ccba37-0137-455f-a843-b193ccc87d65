# GitHub 设置命令

## 在创建GitHub仓库后，运行以下命令：

```bash
# 进入项目目录
cd "/Users/<USER>/Desktop/AI Tarot Reading App with Figma UI"

# 添加GitHub远程仓库（替换YOUR_USERNAME为你的GitHub用户名）
git remote add origin https://github.com/YOUR_USERNAME/ai-tarot-reading-app.git

# 重命名主分支为main（推荐）
git branch -M main

# 推送代码到GitHub
git push -u origin main
```

## 替换示例：
如果你的GitHub用户名是 `hali-na`，那么命令应该是：
```bash
git remote add origin https://github.com/hali-na/ai-tarot-reading-app.git
git branch -M main
git push -u origin main
```

## 验证推送成功：
推送完成后，你可以在GitHub页面看到所有的文件和提交历史。

## 后续开发工作流：
```bash
# 添加新的更改
git add .

# 提交更改
git commit -m "描述你的更改"

# 推送到GitHub
git push
```

## 创建发布版本（可选）：
```bash
# 创建标签
git tag -a v1.0.0 -m "🎯 First stable release - AI Tarot Reading App"

# 推送标签
git push origin v1.0.0
```

## 项目状态：
✅ Git仓库已初始化
✅ 所有文件已提交（222个文件，22,232行代码）
✅ 准备推送到GitHub
🔄 等待GitHub仓库创建和远程连接
