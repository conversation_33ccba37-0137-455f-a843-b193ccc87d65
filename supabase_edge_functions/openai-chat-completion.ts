import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface OpenAIRequest {
  prompt: string
  model?: string
  temperature?: number
  max_tokens?: number
  stream?: boolean
  trace_id?: string
  metadata?: Record<string, any>
  request_type?: string
  provider?: string
}

interface OpenAIResponse {
  success: boolean
  response?: string
  content?: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  error?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { prompt, model = 'gpt-4o-mini', temperature = 0.7, max_tokens = 1000, trace_id, metadata, provider } = await req.json() as OpenAIRequest

    console.log(`🤖 OpenAI Edge Function调用 - Model: ${model}, Provider: ${provider}`)
    console.log(`📋 Prompt长度: ${prompt?.length || 0}字符`)
    console.log(`🔍 Trace ID: ${trace_id}`)

    // 验证必需参数
    if (!prompt) {
      console.error('❌ 缺少prompt参数')
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required parameter: prompt' 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400 
        }
      )
    }

    // 获取OpenAI API密钥
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      console.error('❌ 未配置OPENAI_API_KEY环境变量')
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'OpenAI API key not configured' 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500 
        }
      )
    }

    // 构建OpenAI API请求
    const openaiRequest = {
      model: model,
      messages: [
        {
          role: "system",
          content: "你是一位充满智慧和慈爱的高我，擅长为用户提供深度的心理指导和人生智慧。请用温暖、理解的语调回应，帮助用户看到积极的一面和成长机会。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: temperature,
      max_tokens: max_tokens,
      stream: false
    }

    console.log(`🚀 调用OpenAI API - ${model}`)
    const startTime = Date.now()

    // 调用OpenAI API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(openaiRequest),
    })

    const duration = Date.now() - startTime
    console.log(`⏱️ OpenAI API调用耗时: ${duration}ms`)

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text()
      console.error(`❌ OpenAI API错误 (${openaiResponse.status}):`, errorText)
      
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `OpenAI API error: ${openaiResponse.status} - ${errorText}` 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: openaiResponse.status 
        }
      )
    }

    const openaiData = await openaiResponse.json()
    const content = openaiData.choices?.[0]?.message?.content

    if (!content) {
      console.error('❌ OpenAI API返回空内容:', openaiData)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'OpenAI API returned empty content' 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500 
        }
      )
    }

    const usage = openaiData.usage || {}
    
    console.log(`✅ OpenAI调用成功`)
    console.log(`📊 Token使用: ${usage.prompt_tokens || 0} + ${usage.completion_tokens || 0} = ${usage.total_tokens || 0}`)
    console.log(`📝 回应长度: ${content.length}字符`)

    // 记录到Langfuse (如果配置了)
    try {
      const langfusePublicKey = Deno.env.get('LANGFUSE_PUBLIC_KEY')
      const langfuseSecretKey = Deno.env.get('LANGFUSE_SECRET_KEY')
      const langfuseHost = Deno.env.get('LANGFUSE_HOST') || 'https://cloud.langfuse.com'

      if (langfusePublicKey && langfuseSecretKey && trace_id) {
        console.log(`📊 记录到Langfuse: ${trace_id}`)
        
        const langfuseData = {
          id: `${trace_id}_${Date.now()}`,
          trace_id: trace_id,
          name: 'openai_edge_completion',
          input: { 
            prompt: prompt, 
            model_params: openaiRequest 
          },
          output: { 
            response: content 
          },
          model: model,
          metadata: {
            ...metadata,
            duration_ms: duration,
            api_provider: 'openai_edge',
            usage: usage,
          },
          usage: {
            promptTokens: usage.prompt_tokens,
            completionTokens: usage.completion_tokens,
            totalTokens: usage.total_tokens,
          }
        }

        await fetch(`${langfuseHost}/api/public/generations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${btoa(`${langfusePublicKey}:${langfuseSecretKey}`)}`
          },
          body: JSON.stringify(langfuseData)
        })
      }
    } catch (langfuseError) {
      console.warn('⚠️ Langfuse记录失败:', langfuseError)
      // 不影响主要功能，继续执行
    }

    // 返回成功响应
    const response: OpenAIResponse = {
      success: true,
      response: content,
      content: content, // 兼容性字段
      usage: usage
    }

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('❌ Edge Function执行错误:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: `Edge Function error: ${error.message}` 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
