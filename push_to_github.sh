#!/bin/bash

# 🚀 推送GrowCard应用到新的私人GitHub仓库
# 请先在GitHub上创建名为 'growcard-app-private' 的私人仓库

echo "🔧 配置新的远程仓库..."

# 替换 YOUR_USERNAME 为你的GitHub用户名
GITHUB_USERNAME="hali-na"  # 请修改为你的实际用户名
REPO_NAME="growcard-app-private"

# 添加新的远程仓库
git remote add origin https://github.com/$GITHUB_USERNAME/$REPO_NAME.git

echo "📤 推送代码到GitHub..."

# 推送到main分支
git branch -M main
git push -u origin main

echo "✅ 代码已成功推送到私人仓库！"
echo "🔗 仓库地址: https://github.com/$GITHUB_USERNAME/$REPO_NAME"

# 显示仓库状态
echo "📊 当前仓库状态:"
git remote -v
git status
