-- 🎁 邀请码系统数据库表结构
-- 在 Supabase SQL Editor 中执行此脚本

-- ============================================================================
-- 1. 邀请码表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.invitation_codes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    code_type VARCHAR(50) NOT NULL, -- 'system', 'user_generated', 'admin'
    creator_user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    reward_type VARCHAR(50) NOT NULL, -- 'weekly_membership', 'monthly_membership', 'free_readings'
    reward_value INTEGER DEFAULT 7, -- 奖励天数或次数
    max_uses INTEGER DEFAULT 1, -- 最大使用次数，-1表示无限制
    current_uses INTEGER DEFAULT 0, -- 当前使用次数
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 2. 邀请码使用记录表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.invitation_code_usages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    invitation_code_id UUID REFERENCES public.invitation_codes(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reward_granted BOOLEAN DEFAULT FALSE,
    reward_expires_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    UNIQUE(invitation_code_id, user_id) -- 防止同一用户多次使用同一邀请码
);

-- ============================================================================
-- 3. 用户邀请统计表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.user_invitation_stats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    personal_invitation_code VARCHAR(10) UNIQUE NOT NULL,
    total_invitations INTEGER DEFAULT 0, -- 总邀请数
    successful_invitations INTEGER DEFAULT 0, -- 成功邀请数
    total_reward_days INTEGER DEFAULT 0, -- 获得的总奖励天数
    has_used_invitation_code BOOLEAN DEFAULT FALSE, -- 是否使用过邀请码
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 4. 创建索引
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_invitation_codes_code ON public.invitation_codes(code);
CREATE INDEX IF NOT EXISTS idx_invitation_codes_creator ON public.invitation_codes(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_invitation_codes_active ON public.invitation_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_invitation_code_usages_user ON public.invitation_code_usages(user_id);
CREATE INDEX IF NOT EXISTS idx_invitation_code_usages_code ON public.invitation_code_usages(invitation_code_id);
CREATE INDEX IF NOT EXISTS idx_user_invitation_stats_code ON public.user_invitation_stats(personal_invitation_code);

-- ============================================================================
-- 5. 行级安全策略 (RLS)
-- ============================================================================
ALTER TABLE public.invitation_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitation_code_usages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitation_stats ENABLE ROW LEVEL SECURITY;

-- 邀请码表策略
CREATE POLICY "Users can view active invitation codes" ON public.invitation_codes
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can view own created codes" ON public.invitation_codes
    FOR SELECT USING (auth.uid() = creator_user_id);

-- 邀请码使用记录策略
CREATE POLICY "Users can view own usage records" ON public.invitation_code_usages
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage records" ON public.invitation_code_usages
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 用户邀请统计策略
CREATE POLICY "Users can view own invitation stats" ON public.user_invitation_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own invitation stats" ON public.user_invitation_stats
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own invitation stats" ON public.user_invitation_stats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- 6. 触发器函数
-- ============================================================================

-- 自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_invitation_codes_updated_at
    BEFORE UPDATE ON public.invitation_codes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_invitation_stats_updated_at
    BEFORE UPDATE ON public.user_invitation_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 7. 邀请码使用函数
-- ============================================================================
CREATE OR REPLACE FUNCTION use_invitation_code(
    p_code VARCHAR(10),
    p_user_id UUID,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    v_invitation_code_record RECORD;
    v_usage_exists BOOLEAN;
    v_result JSON;
BEGIN
    -- 检查邀请码是否存在且有效
    SELECT * INTO v_invitation_code_record
    FROM public.invitation_codes
    WHERE code = p_code
      AND is_active = true
      AND (expires_at IS NULL OR expires_at > NOW())
      AND (max_uses = -1 OR current_uses < max_uses);
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'message', '邀请码无效或已过期',
            'error_code', 'INVALID_CODE'
        );
    END IF;
    
    -- 检查用户是否已经使用过此邀请码
    SELECT EXISTS(
        SELECT 1 FROM public.invitation_code_usages
        WHERE invitation_code_id = v_invitation_code_record.id
          AND user_id = p_user_id
    ) INTO v_usage_exists;
    
    IF v_usage_exists THEN
        RETURN json_build_object(
            'success', false,
            'message', '您已经使用过此邀请码',
            'error_code', 'ALREADY_USED'
        );
    END IF;
    
    -- 记录使用
    INSERT INTO public.invitation_code_usages (
        invitation_code_id,
        user_id,
        reward_granted,
        reward_expires_at,
        ip_address,
        user_agent
    ) VALUES (
        v_invitation_code_record.id,
        p_user_id,
        true,
        CASE 
            WHEN v_invitation_code_record.reward_type = 'weekly_membership' THEN NOW() + INTERVAL '7 days'
            WHEN v_invitation_code_record.reward_type = 'monthly_membership' THEN NOW() + INTERVAL '30 days'
            ELSE NULL
        END,
        p_ip_address,
        p_user_agent
    );
    
    -- 更新邀请码使用次数
    UPDATE public.invitation_codes
    SET current_uses = current_uses + 1
    WHERE id = v_invitation_code_record.id;
    
    -- 如果是用户生成的邀请码，更新邀请者统计
    IF v_invitation_code_record.creator_user_id IS NOT NULL THEN
        UPDATE public.user_invitation_stats
        SET successful_invitations = successful_invitations + 1,
            total_reward_days = total_reward_days + v_invitation_code_record.reward_value
        WHERE user_id = v_invitation_code_record.creator_user_id;
    END IF;
    
    -- 更新被邀请用户的统计
    INSERT INTO public.user_invitation_stats (user_id, personal_invitation_code, has_used_invitation_code)
    VALUES (p_user_id, generate_personal_invitation_code(p_user_id), true)
    ON CONFLICT (user_id) DO UPDATE SET has_used_invitation_code = true;
    
    RETURN json_build_object(
        'success', true,
        'message', '邀请码使用成功！',
        'reward_type', v_invitation_code_record.reward_type,
        'reward_value', v_invitation_code_record.reward_value,
        'description', v_invitation_code_record.description
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 8. 生成个人邀请码函数
-- ============================================================================
CREATE OR REPLACE FUNCTION generate_personal_invitation_code(p_user_id UUID)
RETURNS VARCHAR(10) AS $$
DECLARE
    v_code VARCHAR(10);
    v_exists BOOLEAN;
    v_counter INTEGER := 0;
BEGIN
    LOOP
        -- 基于用户ID和计数器生成邀请码
        v_code := UPPER(SUBSTRING(MD5(p_user_id::TEXT || v_counter::TEXT) FROM 1 FOR 6));
        
        -- 检查是否已存在
        SELECT EXISTS(
            SELECT 1 FROM public.user_invitation_stats WHERE personal_invitation_code = v_code
        ) INTO v_exists;
        
        EXIT WHEN NOT v_exists;
        
        v_counter := v_counter + 1;
        
        -- 防止无限循环
        IF v_counter > 1000 THEN
            RAISE EXCEPTION '无法生成唯一邀请码';
        END IF;
    END LOOP;
    
    RETURN v_code;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 9. 插入系统预设邀请码
-- ============================================================================
INSERT INTO public.invitation_codes (code, code_type, reward_type, reward_value, max_uses, description) VALUES
('HELLO1', 'system', 'weekly_membership', 7, -1, '新用户专享周会员'),
('LUCK88', 'system', 'weekly_membership', 7, -1, '幸运活动周会员'),
('VIP024', 'system', 'weekly_membership', 7, -1, '2024特别版周会员'),
('SPRING', 'system', 'weekly_membership', 7, -1, '春季限定周会员'),
('FRIEND', 'system', 'weekly_membership', 7, -1, '好友推荐周会员'),
('BETA01', 'system', 'weekly_membership', 7, -1, '内测用户专享'),
('GIFT99', 'system', 'weekly_membership', 7, -1, '礼品码周会员'),
('TEST01', 'system', 'weekly_membership', 7, -1, '测试专用邀请码')
ON CONFLICT (code) DO NOTHING;

-- ============================================================================
-- 10. 授予权限
-- ============================================================================
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.invitation_codes TO anon, authenticated;
GRANT ALL ON public.invitation_code_usages TO anon, authenticated;
GRANT ALL ON public.user_invitation_stats TO anon, authenticated;
GRANT EXECUTE ON FUNCTION use_invitation_code(VARCHAR, UUID, INET, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION generate_personal_invitation_code(UUID) TO anon, authenticated;

-- ============================================================================
-- 11. 注释
-- ============================================================================
COMMENT ON TABLE public.invitation_codes IS '邀请码表';
COMMENT ON TABLE public.invitation_code_usages IS '邀请码使用记录表';
COMMENT ON TABLE public.user_invitation_stats IS '用户邀请统计表';
COMMENT ON FUNCTION use_invitation_code(VARCHAR, UUID, INET, TEXT) IS '使用邀请码函数';
COMMENT ON FUNCTION generate_personal_invitation_code(UUID) IS '生成个人邀请码函数';
