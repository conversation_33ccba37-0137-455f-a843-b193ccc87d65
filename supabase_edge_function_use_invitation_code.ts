// Supabase Edge Function: use-invitation-code
// 复制此代码到 Supabase Dashboard > Edge Functions

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  console.log('🚀 Edge Function 启动 - use-invitation-code')
  console.log('📡 请求方法:', req.method)
  console.log('🌐 请求URL:', req.url)

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('✅ 处理 CORS 预检请求')
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🔧 创建 Supabase 客户端...')

    // 检查环境变量
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    console.log('🔑 SUPABASE_URL 存在:', !!supabaseUrl)
    console.log('🔑 SERVICE_ROLE_KEY 存在:', !!serviceRoleKey)
    console.log('🔑 SUPABASE_URL 值:', supabaseUrl ? supabaseUrl.substring(0, 30) + '...' : 'undefined')

    // 创建 Supabase 客户端
    const supabaseClient = createClient(
      supabaseUrl ?? '',
      serviceRoleKey ?? '',
    )

    console.log('✅ Supabase 客户端创建成功')

    // 获取请求参数
    console.log('📥 解析请求体...')
    const requestBody = await req.json()
    console.log('📋 原始请求体:', JSON.stringify(requestBody, null, 2))

    const { p_invitation_code, p_code, p_user_id, p_ip_address, p_user_agent, p_language } = requestBody

    // 兼容两种参数名称
    const codeParam = p_invitation_code || p_code

    console.log('🔍 解析后的参数:')
    console.log('  - 邀请码:', codeParam)
    console.log('  - 用户ID:', p_user_id)
    console.log('  - IP地址:', p_ip_address)
    console.log('  - User Agent:', p_user_agent)
    console.log('  - 语言:', p_language || 'zh-CN')

    // 参数验证
    if (!codeParam || !p_user_id) {
      console.log('❌ 缺少必要参数')
      return new Response(
        JSON.stringify({
          success: false,
          message: getLocalizedMessage('missing_params', p_language),
          error_code: 'MISSING_PARAMS',
          debug: { codeParam: !!codeParam, p_user_id: !!p_user_id }
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    }

    console.log('🔍 开始验证邀请码:', codeParam)

    // 1. 检查邀请码是否存在且有效
    console.log('🔍 步骤1: 查询邀请码表...')
    console.log('📊 查询条件: code =', codeParam, ', is_active = true')

    const { data: invitationCode, error: codeError } = await supabaseClient
      .from('invitation_codes')
      .select('id, code, reward_type, reward_value, description, max_uses, current_uses, expires_at, creator_user_id')
      .eq('code', codeParam)
      .eq('is_active', true)
      .single()

    console.log('📋 数据库查询结果:')
    console.log('  - 错误:', codeError)
    console.log('  - 数据:', invitationCode)

    if (codeError || !invitationCode) {
      console.log('❌ 邀请码不存在或已失效')
      console.log('🔍 详细错误信息:', JSON.stringify(codeError, null, 2))

      // 额外调试：查看表中所有邀请码
      const { data: allCodes } = await supabaseClient
        .from('invitation_codes')
        .select('code, is_active')
        .limit(10)

      console.log('📊 表中现有邀请码:', allCodes)

      return new Response(
        JSON.stringify({
          success: false,
          message: getLocalizedMessage('invalid_code', p_language),
          error_code: 'INVALID_CODE',
          debug: {
            query_error: codeError,
            searched_code: p_code,
            existing_codes: allCodes
          }
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    console.log('✅ 找到有效邀请码:', invitationCode.code)

    // 2. 检查邀请码是否过期
    if (invitationCode.expires_at && new Date(invitationCode.expires_at) < new Date()) {
      console.log('❌ 邀请码已过期')
      return new Response(
        JSON.stringify({
          success: false,
          message: getLocalizedMessage('expired_code', p_language),
          error_code: 'EXPIRED_CODE'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // 3. 检查使用次数限制
    if (invitationCode.max_uses !== -1 && invitationCode.current_uses >= invitationCode.max_uses) {
      console.log('❌ 邀请码使用次数已达上限')
      return new Response(
        JSON.stringify({
          success: false,
          message: getLocalizedMessage('usage_limit_reached', p_language),
          error_code: 'USAGE_LIMIT_REACHED'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // 4. 检查用户是否已经使用过此邀请码
    const { data: existingUsage, error: usageError } = await supabaseClient
      .from('invitation_code_usages')
      .select('id, code, reward_type, reward_value')
      .eq('code', invitationCode.code)
      .eq('user_id', p_user_id)
      .single()

    // 处理查询错误（除了 PGRST116 - 没有找到记录）
    if (usageError && usageError.code !== 'PGRST116') {
      console.log('❌ 检查使用记录时出错:', usageError)
      return new Response(
        JSON.stringify({
          success: false,
          message: getLocalizedMessage('network_error', p_language),
          error_code: 'DATABASE_ERROR'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }

    if (existingUsage) {
      console.log('❌ 用户已使用过此邀请码')
      return new Response(
        JSON.stringify({
          success: false,
          message: getLocalizedMessage('already_used', p_language),
          error_code: 'ALREADY_USED'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // 5. 计算奖励过期时间
    let rewardExpiresAt: string | null = null
    if (invitationCode.reward_type === 'weekly_membership') {
      rewardExpiresAt = new Date(Date.now() + (invitationCode.reward_value || 7) * 24 * 60 * 60 * 1000).toISOString()
    } else if (invitationCode.reward_type === 'monthly_membership') {
      rewardExpiresAt = new Date(Date.now() + (invitationCode.reward_value || 30) * 24 * 60 * 60 * 1000).toISOString()
    } else if (invitationCode.reward_type === 'premium_membership') {
      rewardExpiresAt = new Date(Date.now() + (invitationCode.reward_value || 7) * 24 * 60 * 60 * 1000).toISOString()
    } else {
      // 默认使用 reward_value 天数
      rewardExpiresAt = new Date(Date.now() + (invitationCode.reward_value || 7) * 24 * 60 * 60 * 1000).toISOString()
    }

    // 6. 记录使用
    const { error: insertError } = await supabaseClient
      .from('invitation_code_usages')
      .insert({
        invitation_code_id: invitationCode.id,
        user_id: p_user_id,
        code: invitationCode.code,
        reward_type: invitationCode.reward_type,
        reward_value: invitationCode.reward_value,
        reward_granted: true,
        reward_expires_at: rewardExpiresAt,
        ip_address: p_ip_address,
        user_agent: p_user_agent,
      })

    if (insertError) {
      console.log('❌ 记录使用失败:', insertError)
      return new Response(
        JSON.stringify({
          success: false,
          message: '记录使用失败',
          error_code: 'INSERT_FAILED'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }

    // 7. 更新邀请码使用次数
    const { error: updateError } = await supabaseClient
      .from('invitation_codes')
      .update({ current_uses: invitationCode.current_uses + 1 })
      .eq('id', invitationCode.id)

    if (updateError) {
      console.log('⚠️ 更新使用次数失败:', updateError)
      // 不返回错误，因为主要操作已成功
    }

    // 8. 如果是用户生成的邀请码，更新邀请者统计
    if (invitationCode.creator_user_id) {
      const { error: statsError } = await supabaseClient
        .from('user_invitation_stats')
        .upsert({
          user_id: invitationCode.creator_user_id,
          successful_invitations: 1, // 这里应该是增量更新，简化处理
          total_reward_days: invitationCode.reward_value,
        }, {
          onConflict: 'user_id'
        })

      if (statsError) {
        console.log('⚠️ 更新邀请者统计失败:', statsError)
      }
    }

    // 9. 更新被邀请用户的统计
    const personalCode = generatePersonalInvitationCode(p_user_id)
    const { error: userStatsError } = await supabaseClient
      .from('user_invitation_stats')
      .upsert({
        user_id: p_user_id,
        personal_invitation_code: personalCode,
        has_used_invitation_code: true,
      }, {
        onConflict: 'user_id'
      })

    if (userStatsError) {
      console.log('⚠️ 更新用户统计失败:', userStatsError)
    }

    // 10. 创建后端订阅记录
    const subscriptionResult = await createBackendSubscription(supabaseClient, p_user_id, invitationCode.reward_type, invitationCode.reward_value)
    if (!subscriptionResult.success) {
      console.log('⚠️ 创建后端订阅记录失败，但邀请码使用记录已保存:', subscriptionResult.error)
      // 不阻断流程，因为邀请码使用记录已经保存成功
    } else {
      console.log('✅ 后端订阅记录创建成功')
    }

    console.log('✅ 邀请码使用成功')

    // 根据奖励类型生成相应的成功消息
    let successMessage = getLocalizedMessage('success_message', p_language)

    if (invitationCode.reward_type === 'weekly_membership') {
      successMessage = getLocalizedMessage('weekly_membership_reward', p_language)
    } else if (invitationCode.reward_type === 'monthly_membership') {
      successMessage = getLocalizedMessage('monthly_membership_reward', p_language)
    } else if (invitationCode.reward_type === 'premium_membership') {
      successMessage = getLocalizedMessage('premium_membership_reward', p_language)
    } else {
      // 使用通用模板，替换天数
      const template = getLocalizedMessage('membership_reward_received', p_language)
      successMessage = template.replace('{days}', (invitationCode.reward_value || 7).toString())
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: successMessage,
        reward_type: invitationCode.reward_type,
        reward_value: invitationCode.reward_value,
        description: invitationCode.description,
        reward_expires_at: rewardExpiresAt,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('❌ Edge Function 执行失败:', error)
    return new Response(
      JSON.stringify({
        success: false,
        message: getLocalizedMessage('internal_error', 'zh-CN'),
        error_code: 'INTERNAL_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

// 多语言消息 - 与客户端语言管理器保持一致
function getLocalizedMessage(key: string, language: string = 'zh-CN'): string {
  const messages: Record<string, Record<string, string>> = {
    'missing_params': {
      'zh-CN': '缺少必要参数',
      'zh-TW': '缺少必要參數',
      'en-US': 'Missing required parameters',
      'es-ES': 'Faltan parámetros requeridos',
      'ja-JP': '必要なパラメータが不足しています',
      'ko-KR': '필수 매개변수가 누락되었습니다',
    },
    'invalid_code': {
      'zh-CN': '邀请码不存在或已失效',
      'zh-TW': '邀請碼不存在或已失效',
      'en-US': 'Invitation code does not exist or has expired',
      'es-ES': 'El código de invitación no existe o ha expirado',
      'ja-JP': '招待コードが存在しないか期限切れです',
      'ko-KR': '초대 코드가 존재하지 않거나 만료되었습니다',
    },
    'expired_code': {
      'zh-CN': '邀请码已过期',
      'zh-TW': '邀請碼已過期',
      'en-US': 'Invitation code has expired',
      'es-ES': 'El código de invitación ha expirado',
      'ja-JP': '招待コードの有効期限が切れています',
      'ko-KR': '초대 코드가 만료되었습니다',
    },
    'usage_limit_reached': {
      'zh-CN': '邀请码使用次数已达上限',
      'zh-TW': '邀請碼使用次數已達上限',
      'en-US': 'Invitation code usage limit reached',
      'es-ES': 'Se ha alcanzado el límite de uso del código de invitación',
      'ja-JP': '招待コードの使用回数が上限に達しました',
      'ko-KR': '초대 코드 사용 한도에 도달했습니다',
    },
    'already_used': {
      'zh-CN': '您已经使用过这个邀请码了',
      'zh-TW': '您已經使用過這個邀請碼了',
      'en-US': 'You have already used this invitation code',
      'es-ES': 'Ya has usado este código de invitación',
      'ja-JP': 'この招待コードは既に使用済みです',
      'ko-KR': '이미 이 초대 코드를 사용했습니다',
    },
    'network_error': {
      'zh-CN': '网络错误，请稍后重试',
      'zh-TW': '網絡錯誤，請稍後重試',
      'en-US': 'Network error, please try again later',
      'es-ES': 'Error de red, por favor inténtalo de nuevo más tarde',
      'ja-JP': 'ネットワークエラーです。後でもう一度お試しください',
      'ko-KR': '네트워크 오류입니다. 나중에 다시 시도하세요',
    },
    'success_message': {
      'zh-CN': '邀请码使用成功！',
      'zh-TW': '邀請碼使用成功！',
      'en-US': 'Invitation code used successfully!',
      'es-ES': '¡Código de invitación usado exitosamente!',
      'ja-JP': '招待コードの使用に成功しました！',
      'ko-KR': '초대 코드 사용 성공!',
    },
    'weekly_membership_reward': {
      'zh-CN': '恭喜！您获得了7天免费基础会员权益',
      'zh-TW': '恭喜！您獲得了7天免費基礎會員權益',
      'en-US': 'Congratulations! You received 7 days free basic membership',
      'es-ES': '¡Felicidades! Recibiste 7 días de membresía básica gratuita',
      'ja-JP': 'おめでとうございます！7日間の無料ベーシックメンバーシップを獲得しました',
      'ko-KR': '축하합니다! 7일 무료 베이직 멤버십을 받았습니다',
    },
    'monthly_membership_reward': {
      'zh-CN': '恭喜！您获得了30天免费基础会员权益',
      'zh-TW': '恭喜！您獲得了30天免費基礎會員權益',
      'en-US': 'Congratulations! You received 30 days free basic membership',
      'es-ES': '¡Felicidades! Recibiste 30 días de membresía básica gratuita',
      'ja-JP': 'おめでとうございます！30日間の無料ベーシックメンバーシップを獲得しました',
      'ko-KR': '축하합니다! 30일 무료 베이직 멤버십을 받았습니다',
    },
    'premium_membership_reward': {
      'zh-CN': '恭喜！您获得了高级会员权益',
      'zh-TW': '恭喜！您獲得了高級會員權益',
      'en-US': 'Congratulations! You received premium membership benefits',
      'es-ES': '¡Felicidades! Recibiste beneficios de membresía premium',
      'ja-JP': 'おめでとうございます！プレミアムメンバーシップ特典を獲得しました',
      'ko-KR': '축하합니다! 프리미엄 멤버십 혜택을 받았습니다',
    },
    'membership_reward_received': {
      'zh-CN': '恭喜！您获得了{days}天免费会员权益',
      'zh-TW': '恭喜！您獲得了{days}天免費會員權益',
      'en-US': 'Congratulations! You received {days} days of free membership',
      'es-ES': '¡Felicidades! Recibiste {days} días de membresía gratuita',
      'ja-JP': 'おめでとうございます！{days}日間の無料メンバーシップを獲得しました',
      'ko-KR': '축하합니다! {days}일 무료 멤버십을 받았습니다',
    },
    'internal_error': {
      'zh-CN': '服务器内部错误',
      'zh-TW': '伺服器內部錯誤',
      'en-US': 'Internal server error',
      'es-ES': 'Error interno del servidor',
      'ja-JP': 'サーバー内部エラー',
      'ko-KR': '서버 내부 오류',
    },
  }

  return messages[key]?.[language] || messages[key]?.['zh-CN'] || key
}

// 生成个人邀请码的辅助函数
function generatePersonalInvitationCode(userId: string): string {
  // 基于用户ID生成6位邀请码
  const hash = Array.from(userId)
    .reduce((acc, char) => acc + char.charCodeAt(0), 0)
    .toString(36)
    .toUpperCase()
    .padStart(6, '0')
    .slice(0, 6)

  return hash
}

// 创建后端订阅记录
async function createBackendSubscription(
  supabaseClient: any,
  userId: string,
  rewardType: string,
  rewardValue: number
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('📊 开始创建后端订阅记录')
    console.log('👤 用户ID:', userId)
    console.log('🎁 奖励类型:', rewardType)
    console.log('📅 奖励天数:', rewardValue)

    // 根据奖励类型确定订阅计划
    let planName: string
    switch (rewardType) {
      case 'weekly_membership':
        planName = 'basic_weekly'
        break
      case 'monthly_membership':
        planName = 'basic_monthly'
        break
      case 'premium_membership':
        planName = 'premium_weekly'
        break
      case 'premium_monthly_membership':
        planName = 'premium_monthly'
        break
      default:
        planName = 'basic_weekly'
    }

    // 获取订阅计划ID
    const { data: planData, error: planError } = await supabaseClient
      .from('subscription_plans')
      .select('id')
      .eq('name', planName)
      .single()

    if (planError || !planData) {
      console.log('❌ 找不到订阅计划:', planName, planError)
      return { success: false, error: `找不到订阅计划: ${planName}` }
    }

    const planId = planData.id
    const now = new Date()
    const endDate = new Date(now.getTime() + (rewardValue * 24 * 60 * 60 * 1000))

    console.log('📋 订阅计划ID:', planId)
    console.log('⏰ 开始时间:', now.toISOString())
    console.log('⏰ 结束时间:', endDate.toISOString())

    // 先取消现有的活跃订阅
    const { error: cancelError } = await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        cancelled_at: now.toISOString(),
        updated_at: now.toISOString(),
      })
      .eq('user_id', userId)
      .eq('status', 'active')

    if (cancelError) {
      console.log('⚠️ 取消现有订阅时出错:', cancelError)
      // 继续执行，可能用户没有现有订阅
    } else {
      console.log('🔄 已取消现有活跃订阅')
    }

    // 创建新的订阅记录
    const { data: subscriptionData, error: subscriptionError } = await supabaseClient
      .from('user_subscriptions')
      .insert({
        user_id: userId,
        plan_id: planId,
        status: 'active',
        start_date: now.toISOString(),
        end_date: endDate.toISOString(),
        payment_method: 'invitation_code',
        payment_status: 'completed',
        amount_paid: 0.00,
        auto_renew: false,
      })
      .select()
      .single()

    if (subscriptionError) {
      console.log('❌ 创建订阅记录失败:', subscriptionError)
      return { success: false, error: `创建订阅记录失败: ${subscriptionError.message}` }
    }

    console.log('✅ 后端订阅记录创建成功')
    console.log('📊 订阅记录:', subscriptionData)

    return { success: true }

  } catch (error) {
    console.log('❌ 创建后端订阅记录时发生异常:', error)
    return { success: false, error: `异常: ${error}` }
  }
}


