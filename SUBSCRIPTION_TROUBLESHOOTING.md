# 🔧 订阅功能故障排除指南

## 🚨 问题：点击订阅后没有出现苹果付费弹窗

### ✅ 已修复的问题

#### 1. **购买方法错误**
- **问题**: 代码使用了 `buyConsumable` 而不是 `buyNonConsumable`
- **修复**: 已更改为正确的 `buyNonConsumable` 方法
- **影响**: 这是导致弹窗不出现的主要原因

#### 2. **产品加载不完整**
- **问题**: 代码中注释掉了部分产品ID
- **修复**: 已启用所有订阅产品ID
- **影响**: 某些订阅选项可能无法正常工作

#### 3. **TestFlight模式被禁用**
- **问题**: `_isTestFlightMode = false`
- **修复**: 已启用为 `true`
- **影响**: 可能影响测试环境的功能

### 🔍 需要检查的配置

#### 1. **Xcode项目配置**
确保在Xcode中添加了In-App Purchase capability：

1. 打开 `ios/Runner.xcworkspace`
2. 选择Runner项目
3. 选择Runner target
4. 点击 "Signing & Capabilities"
5. 点击 "+ Capability"
6. 搜索并添加 "In-App Purchase"

#### 2. **App Store Connect配置**
确保以下配置正确：

1. **Paid Apps Agreement**: 必须为Active状态
2. **产品状态**: 所有产品必须为"Ready for Sale"
3. **产品类型**: 确保配置为Subscription类型
4. **沙盒测试账号**: 创建并激活测试账号

#### 3. **StoreKit配置文件**
检查 `ios/Runner/Products.storekit` 文件：
- 产品ID是否与代码中的一致
- 价格设置是否正确
- 订阅周期配置是否正确

### 🧪 测试步骤

#### 步骤1: 运行调试应用
```bash
flutter run -d ios --target test_subscription_debug.dart
```

#### 步骤2: 检查控制台输出
观察以下关键信息：
- 应用内购买是否可用
- 产品是否成功加载
- 购买请求是否发送

#### 步骤3: 沙盒环境测试
1. 在iOS设备上退出所有Apple ID
2. 启动应用
3. 尝试购买时登录沙盒测试账号

### 📱 常见错误及解决方案

#### 错误1: "应用内购买不可用"
**原因**: 设备或模拟器不支持应用内购买
**解决**: 
- 使用真机测试
- 确保设备已登录Apple ID
- 检查设备设置中的应用内购买权限

#### 错误2: "找不到产品"
**原因**: 产品ID不匹配或产品未配置
**解决**:
- 检查App Store Connect中的产品ID
- 确保产品状态为"Ready for Sale"
- 在沙盒环境中测试

#### 错误3: "StoreKit错误"
**原因**: 网络问题或配置错误
**解决**:
- 检查网络连接
- 确保使用沙盒测试账号
- 验证App Store Connect配置

### 🔧 代码修复总结

#### 主要修复内容：
1. ✅ 修复购买方法：`buyConsumable` → `buyNonConsumable`
2. ✅ 启用所有产品ID加载
3. ✅ 启用TestFlight模式
4. ✅ 创建调试测试应用

#### 需要手动配置：
1. 🔧 在Xcode中添加In-App Purchase capability
2. 🔧 确保App Store Connect配置正确
3. 🔧 创建并配置沙盒测试账号

### 📞 下一步操作

1. **立即测试**: 运行修复后的代码
2. **Xcode配置**: 添加In-App Purchase capability
3. **沙盒测试**: 使用测试账号验证功能
4. **真机测试**: 在iPhone上测试完整流程

### 🎯 预期结果

修复后，点击订阅按钮应该：
1. ✅ 显示苹果的订阅确认弹窗
2. ✅ 显示正确的价格和订阅条款
3. ✅ 允许用户确认购买
4. ✅ 完成订阅流程

---

**如果问题仍然存在，请检查控制台输出并提供详细的错误信息。** 