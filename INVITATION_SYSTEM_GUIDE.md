# 🎁 邀请码系统使用指南

## 📋 功能概述

邀请码系统是AI塔罗应用的核心推广功能，允许用户通过邀请好友获得会员奖励，同时为新用户提供免费体验机会。

## 🏗️ 系统架构

### 数据库表结构
1. **invitation_codes** - 邀请码主表
2. **invitation_code_usages** - 使用记录表
3. **user_invitation_stats** - 用户邀请统计表

### 核心服务
- **InvitationService** - 邀请码业务逻辑
- **数据库函数** - 邀请码使用和验证

## 🚀 功能特性

### 1. 邀请码类型
- **系统邀请码**：管理员创建的通用邀请码
- **用户邀请码**：每个用户的专属6位邀请码
- **活动邀请码**：特殊活动或推广使用

### 2. 奖励机制
- **新用户奖励**：使用邀请码获得7天免费会员
- **邀请者奖励**：每成功邀请一人获得额外奖励
- **累积奖励**：邀请越多，奖励越丰富

### 3. 安全机制
- **防重复使用**：每个用户只能使用一次邀请码
- **使用记录**：完整的使用历史和IP追踪
- **有效期控制**：支持邀请码过期时间设置

## 💻 技术实现

### 数据库设置

1. **执行SQL脚本**
```bash
# 在Supabase SQL Editor中执行
invitation_system_schema.sql
```

2. **预设邀请码**
系统自动创建以下邀请码：
- `HELLO1` - 新用户专享
- `LUCK88` - 幸运活动
- `VIP024` - 2024特别版
- `SPRING` - 春季限定
- `FRIEND` - 好友推荐
- `BETA01` - 内测用户
- `GIFT99` - 礼品码
- `TEST01` - 测试专用

### 服务集成

1. **初始化邀请服务**
```dart
// 在main.dart中添加
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => InvitationService()),
    // ... 其他providers
  ],
)
```

2. **服务初始化**
```dart
// 在应用启动时调用
final invitationService = Provider.of<InvitationService>(context, listen: false);
await invitationService.initialize();
```

## 🎯 使用流程

### 用户使用邀请码
1. 用户点击"邀请好友"功能
2. 如果未使用过邀请码，显示输入界面
3. 输入6位邀请码
4. 系统验证并激活奖励
5. 用户获得会员权益

### 用户分享邀请码
1. 用户获得专属6位邀请码
2. 通过分享功能传播邀请码
3. 好友使用邀请码注册
4. 双方都获得奖励

## 🔧 管理功能

### 邀请码管理界面
- **我的邀请码**：显示和复制个人邀请码
- **邀请统计**：总邀请数、成功邀请、获得奖励
- **分享功能**：一键分享邀请文案
- **规则说明**：详细的邀请规则和奖励机制

### 数据库管理

1. **查看邀请码使用情况**
```sql
SELECT 
    ic.code,
    ic.description,
    ic.current_uses,
    ic.max_uses,
    COUNT(icu.id) as actual_uses
FROM invitation_codes ic
LEFT JOIN invitation_code_usages icu ON ic.id = icu.invitation_code_id
GROUP BY ic.id;
```

2. **查看用户邀请统计**
```sql
SELECT 
    u.email,
    uis.personal_invitation_code,
    uis.successful_invitations,
    uis.total_reward_days
FROM user_invitation_stats uis
JOIN users u ON uis.user_id = u.id
ORDER BY uis.successful_invitations DESC;
```

## 🎨 UI界面

### 邀请码输入界面
- **现代化设计**：毛玻璃效果背景
- **输入验证**：6位字母数字组合
- **实时反馈**：输入格式提示
- **多语言支持**：中英文界面

### 邀请管理界面
- **统计卡片**：可视化邀请数据
- **邀请码展示**：大字体显示个人邀请码
- **分享按钮**：一键复制和分享
- **规则说明**：清晰的奖励机制说明

## 🌍 多语言支持

系统支持以下语言：
- 🇨🇳 简体中文
- 🇹🇼 繁体中文
- 🇺🇸 英语
- 🇪🇸 西班牙语
- 🇯🇵 日语
- 🇰🇷 韩语

## 🔒 安全考虑

### 防刷机制
- **IP限制**：记录使用IP，防止批量注册
- **设备指纹**：结合设备信息验证
- **时间限制**：限制邀请码使用频率

### 数据保护
- **行级安全**：RLS策略保护用户数据
- **权限控制**：严格的数据库访问权限
- **审计日志**：完整的操作记录

## 📊 监控指标

### 关键指标
- **邀请码使用率**：有效邀请码的使用比例
- **转化率**：邀请用户的注册转化率
- **留存率**：通过邀请码注册用户的留存情况
- **奖励成本**：邀请奖励的总成本

### 数据分析
```sql
-- 邀请码效果分析
SELECT 
    DATE_TRUNC('week', used_at) as week,
    COUNT(*) as total_uses,
    COUNT(DISTINCT user_id) as unique_users
FROM invitation_code_usages
GROUP BY week
ORDER BY week DESC;
```

## 🚀 未来扩展

### 计划功能
1. **等级邀请**：不同等级用户的邀请奖励
2. **团队邀请**：团队邀请挑战和奖励
3. **动态奖励**：根据活动调整奖励机制
4. **社交分享**：集成社交媒体分享
5. **邀请排行榜**：邀请达人排行榜

### 技术优化
1. **缓存优化**：邀请码验证缓存
2. **异步处理**：奖励发放异步处理
3. **批量操作**：批量邀请码生成和管理
4. **API接口**：RESTful API支持

## 📞 技术支持

如有问题，请参考：
1. **数据库日志**：检查Supabase日志
2. **应用日志**：查看Flutter调试输出
3. **错误处理**：InvitationService错误信息
4. **用户反馈**：收集用户使用反馈

---

**注意**：邀请码系统涉及用户奖励和会员权益，请确保在生产环境中进行充分测试，并建立完善的监控和风控机制。
